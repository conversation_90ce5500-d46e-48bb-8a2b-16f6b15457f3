import * as vscode from 'vscode';
import { DynamicIdentityGenerator } from './utils/random-generator';

export class GitConfigSpoofer {
    private identityGenerator = DynamicIdentityGenerator.getInstance();
    private isActive = false;

    activate(): void {
        if (this.isActive) {
            return;
        }

        this.isActive = true;
        this.interceptGitCommands();
        console.log('🔧 Git Config Spoofer 已激活');
    }

    deactivate(): void {
        this.isActive = false;
        console.log('🔧 Git Config Spoofer 已停用');
    }

    private interceptGitCommands(): void {
        // 劫持shell命令执行
        const originalExecuteCommand = vscode.commands.executeCommand;
        
        (vscode.commands as any).executeCommand = async (command: string, ...args: any[]) => {
            // 如果是git config命令
            if (command === 'shellCommand.execute' && args[0]?.command) {
                const shellCommand = args[0].command;
                
                if (shellCommand.includes('git config user.name')) {
                    const identity = this.identityGenerator.getCurrentIdentity();
                    console.log('🔧 劫持 git config user.name:', identity.user.username);
                    
                    // 返回虚拟的用户名
                    return {
                        text: identity.user.username,
                        isError: false
                    };
                }
                
                if (shellCommand.includes('git config user.email')) {
                    const identity = this.identityGenerator.getCurrentIdentity();
                    console.log('🔧 劫持 git config user.email:', identity.user.email);
                    
                    // 返回虚拟的邮箱
                    return {
                        text: identity.user.email,
                        isError: false
                    };
                }
            }
            
            return originalExecuteCommand.call(vscode.commands, command, ...args);
        };
    }

    getStatus(): string {
        if (!this.isActive) {
            return '❌ 未激活';
        }
        return '✅ 已激活';
    }
} 