import { DynamicIdentityGenerator } from './utils/random-generator';

export class NetworkSpoofer {
    private identityGenerator = DynamicIdentityGenerator.getInstance();
    private isActive = false;
    private originalFetch: any = null;
    private originalXHROpen: any = null;
    private originalWebSocket: any = null;

    activate(): void {
        if (this.isActive) {
            return;
        }

        try {
            // 保存原始函数引用
            this.originalFetch = (globalThis as any).fetch || (typeof window !== 'undefined' ? (window as any).fetch : null);
            
            // 检查是否在浏览器环境中
            if (typeof XMLHttpRequest !== 'undefined') {
                this.originalXHROpen = XMLHttpRequest.prototype.open;
            }
            
            this.originalWebSocket = (globalThis as any).WebSocket || (typeof window !== 'undefined' ? (window as any).WebSocket : null);

            // 劫持AugmentCode的API请求
            this.interceptAugmentCodeAPI();
            
            // 劫持GitHub API请求
            this.interceptGitHubAPI();
            
            // 劫持通用网络请求
            this.interceptGeneralRequests();
            
            // 劫持WebSocket连接
            this.interceptWebSocket();
            
            this.isActive = true;
            console.log('🌐 Network Spoofer 已激活');
        } catch (error) {
            console.warn('⚠️ Network Spoofer 激活失败:', error);
        }
    }

    deactivate(): void {
        if (!this.isActive) {
            return;
        }

        try {
            // 恢复原始函数
            if (this.originalFetch) {
                (globalThis as any).fetch = this.originalFetch;
                if (typeof window !== 'undefined') {
                    (window as any).fetch = this.originalFetch;
                }
            }
            if (this.originalXHROpen && typeof XMLHttpRequest !== 'undefined') {
                XMLHttpRequest.prototype.open = this.originalXHROpen;
            }
            if (this.originalWebSocket) {
                (globalThis as any).WebSocket = this.originalWebSocket;
                if (typeof window !== 'undefined') {
                    (window as any).WebSocket = this.originalWebSocket;
                }
            }

            this.isActive = false;
            console.log('🌐 Network Spoofer 已停用');
        } catch (error) {
            console.warn('⚠️ Network Spoofer 停用失败:', error);
        }
    }

    // 劫持AugmentCode的API请求
    private interceptAugmentCodeAPI(): void {
        const originalFetch = this.originalFetch;
        if (!originalFetch) return;
        
        const newFetch = async (input: any, init?: any) => {
            const url = typeof input === 'string' ? input : input.toString();
            
            // 如果是AugmentCode的API请求
            if (url.includes('augmentcode') || url.includes('api.augmentcode')) {
                const identity = this.identityGenerator.getCurrentIdentity();
                
                // 修改请求头中的设备信息 - 基于实际分析
                if (init?.headers) {
                    init.headers = {
                        ...init.headers,
                        // 重点虚拟化会话相关的字段
                        'x-request-session-id': identity.device.sessionId,
                        'x-request-id': `req-${identity.device.clientId}-${Date.now()}`,
                        // User-Agent保持原样，因为所有用户都相同
                        'User-Agent': 'Augment-VSCode/1.0',
                        'x-api-version': '2'
                    };
                }

                // 修改请求体中的设备信息
                if (init?.body) {
                    try {
                        const body = JSON.parse(init.body.toString());
                        body.machineId = identity.device.machineId;
                        body.sessionId = identity.device.sessionId;
                        body.userName = identity.user.username;
                        body.workspaceName = identity.workspace.workspaceName;
                        body.githubUser = identity.github.login;
                        init.body = JSON.stringify(body);
                    } catch (e) {
                        // 如果不是JSON格式，跳过
                    }
                }
            }
            
            return originalFetch(input, init);
        };

        (globalThis as any).fetch = newFetch;
        if (typeof window !== 'undefined') {
            (window as any).fetch = newFetch;
        }
    }

    // 劫持GitHub API请求
    private interceptGitHubAPI(): void {
        const originalFetch = this.originalFetch;
        if (!originalFetch) return;
        
        const newFetch = async (input: any, init?: any) => {
            const url = typeof input === 'string' ? input : input.toString();
            
            // 如果是GitHub API请求
            if (url.includes('api.github.com') || url.includes('github.com/api')) {
                const identity = this.identityGenerator.getCurrentIdentity();
                
                // 修改请求头中的GitHub信息
                if (init?.headers) {
                    init.headers = {
                        ...init.headers,
                        'Authorization': `Bearer ${identity.github.token}`,
                        'User-Agent': `GitHub-${identity.github.login}/1.0`,
                        'Accept': 'application/vnd.github.v3+json',
                        'X-GitHub-User': identity.github.login,
                        'X-GitHub-User-ID': identity.github.id
                    };
                }
            }
            
            return originalFetch(input, init);
        };

        (globalThis as any).fetch = newFetch;
        if (typeof window !== 'undefined') {
            (window as any).fetch = newFetch;
        }
    }

    // 劫持通用网络请求
    private interceptGeneralRequests(): void {
        const originalXHROpen = this.originalXHROpen;
        if (!originalXHROpen || typeof XMLHttpRequest === 'undefined') return;
        
        XMLHttpRequest.prototype.open = function(method: string, url: string, ...args: any[]) {
            // 如果是AugmentCode相关的请求
            if (url.includes('augmentcode') || url.includes('api.augmentcode')) {
                const identity = DynamicIdentityGenerator.getInstance().getCurrentIdentity();
                
                // 在发送请求前修改请求头
                const originalSetRequestHeader = this.setRequestHeader;
                this.setRequestHeader = function(name: string, value: string) {
                    // 劫持特定的请求头
                    if (name.toLowerCase() === 'x-machine-id') {
                        value = identity.device.machineId;
                    } else if (name.toLowerCase() === 'x-session-id') {
                        value = identity.device.sessionId;
                    } else if (name.toLowerCase() === 'x-user-name') {
                        value = identity.user.username;
                    }
                    
                    return originalSetRequestHeader.call(this, name, value);
                };
            }
            
            return originalXHROpen.call(this, method, url, ...args);
        };
    }

    // 劫持WebSocket连接
    private interceptWebSocket(): void {
        const originalWebSocket = this.originalWebSocket;
        if (!originalWebSocket) return;
        
        const newWebSocket = function(url: string, protocols?: string | string[]) {
            // 如果是AugmentCode的WebSocket连接
            if (url.includes('augmentcode') || url.includes('ws.augmentcode')) {
                const identity = DynamicIdentityGenerator.getInstance().getCurrentIdentity();
                
                // 修改URL参数
                const urlObj = new URL(url);
                urlObj.searchParams.set('machineId', identity.device.machineId);
                urlObj.searchParams.set('sessionId', identity.device.sessionId);
                urlObj.searchParams.set('userName', identity.user.username);
                urlObj.searchParams.set('githubUser', identity.github.login);
                
                url = urlObj.toString();
            }
            
            return new originalWebSocket(url, protocols);
        };

        (globalThis as any).WebSocket = newWebSocket;
        if (typeof window !== 'undefined') {
            (window as any).WebSocket = newWebSocket;
        }
    }

    // 获取当前网络状态
    getStatus(): string {
        if (!this.isActive) {
            return '❌ 未激活';
        }
        
        const identity = this.identityGenerator.getCurrentIdentity();
        return `✅ 已激活 | 设备: ${identity.device.machineId.substring(0, 8)}... | 用户: ${identity.user.username}`;
    }

    // 获取详细的网络状态信息
    getDetailedStatus(): object {
        if (!this.isActive) {
            return { active: false };
        }
        
        const identity = this.identityGenerator.getCurrentIdentity();
        return {
            active: true,
            device: {
                machineId: identity.device.machineId.substring(0, 8) + '...',
                sessionId: identity.device.sessionId.substring(0, 8) + '...',
                clientId: identity.device.clientId.substring(0, 8) + '...'
            },
            user: {
                username: identity.user.username,
                email: identity.user.email
            },
            workspace: {
                name: identity.workspace.workspaceName,
                path: identity.workspace.root
            },
            github: {
                login: identity.github.login,
                id: identity.github.id
            }
        };
    }
} 