var Ja=Object.defineProperty;var Ua=(o,e,t)=>e in o?Ja(o,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[e]=t;var x=(o,e,t)=>Ua(o,typeof e!="symbol"?e+"":e,t);import{ao as Ke,A as ke,ap as re,ar as Ga,y as nt,S as oe,i as ie,s as se,d as B,t as M,q as $,o as ae,p as ce,c as V,N as dt,al as Ct,ah as Za,af as Pn,D as R,E as _,F as te,G as P,h as w,X as je,Y as qe,n as W,aw as $r,a8 as hn,$ as Oe,I as So,z as el,V as le,a6 as Ya,U as Ze,J as Te,K as Ne,L as Le,M as Ae,P as pr,e as N,ag as Xa,R as ve,aa as Qa,a3 as Oo,f as F,W as Mt,a as Yr,b as jo,g as ec,u as tc,v as nc,w as rc,x as oc,H as ic,j as qo,ay as un,a7 as sc}from"./SpinnerAugment-VfHtkDdv.js";import{C as lc}from"./CalloutAugment-jvmj3vIU.js";import{E as tl}from"./exclamation-triangle-BgK0UWCq.js";import{C as ac,T as cc}from"./CardAugment-CMpdst0l.js";import{e as Ko}from"./IconButtonAugment-BlRCK7lJ.js";import{D as ln,U as Ln,V as nl,W as Wo,q as dc,X as hc,Y as rl,Z as ol,_ as il,u as sl,x as ll,z as al,y as cl,w as dl,v as hl,I as ul,J as pl,$ as uc,B as fl,a0 as pc,f as Jo,g as Uo}from"./index-C5qylk65.js";import"./BaseTextInput-C9A3t790.js";import{t as fc}from"./index-6WVCg-U8.js";import{P as Vn}from"./message-broker-DxXjuHCW.js";import{F as Ie}from"./Filespan-UFj4_Gis.js";import{M as wn}from"./MaterialIcon-YT2PSBkc.js";import{P as mc}from"./pen-to-square-DiN4Ry3-.js";import{A as gc}from"./augment-logo-DHqqkJ4i.js";import{F as ml,b as yc}from"./folder-opened-CgcyGshw.js";function ge(o){this.content=o}function gl(o,e,t){for(let n=0;;n++){if(n==o.childCount||n==e.childCount)return o.childCount==e.childCount?null:t;let r=o.child(n),i=e.child(n);if(r!=i){if(!r.sameMarkup(i))return t;if(r.isText&&r.text!=i.text){for(let s=0;r.text[s]==i.text[s];s++)t++;return t}if(r.content.size||i.content.size){let s=gl(r.content,i.content,t+1);if(s!=null)return s}t+=r.nodeSize}else t+=r.nodeSize}}function yl(o,e,t,n){for(let r=o.childCount,i=e.childCount;;){if(r==0||i==0)return r==i?null:{a:t,b:n};let s=o.child(--r),l=e.child(--i),a=s.nodeSize;if(s!=l){if(!s.sameMarkup(l))return{a:t,b:n};if(s.isText&&s.text!=l.text){let c=0,d=Math.min(s.text.length,l.text.length);for(;c<d&&s.text[s.text.length-c-1]==l.text[l.text.length-c-1];)c++,t--,n--;return{a:t,b:n}}if(s.content.size||l.content.size){let c=yl(s.content,l.content,t-1,n-1);if(c)return c}t-=a,n-=a}else t-=a,n-=a}}ge.prototype={constructor:ge,find:function(o){for(var e=0;e<this.content.length;e+=2)if(this.content[e]===o)return e;return-1},get:function(o){var e=this.find(o);return e==-1?void 0:this.content[e+1]},update:function(o,e,t){var n=t&&t!=o?this.remove(t):this,r=n.find(o),i=n.content.slice();return r==-1?i.push(t||o,e):(i[r+1]=e,t&&(i[r]=t)),new ge(i)},remove:function(o){var e=this.find(o);if(e==-1)return this;var t=this.content.slice();return t.splice(e,2),new ge(t)},addToStart:function(o,e){return new ge([o,e].concat(this.remove(o).content))},addToEnd:function(o,e){var t=this.remove(o).content.slice();return t.push(o,e),new ge(t)},addBefore:function(o,e,t){var n=this.remove(e),r=n.content.slice(),i=n.find(o);return r.splice(i==-1?r.length:i,0,e,t),new ge(r)},forEach:function(o){for(var e=0;e<this.content.length;e+=2)o(this.content[e],this.content[e+1])},prepend:function(o){return(o=ge.from(o)).size?new ge(o.content.concat(this.subtract(o).content)):this},append:function(o){return(o=ge.from(o)).size?new ge(this.subtract(o).content.concat(o.content)):this},subtract:function(o){var e=this;o=ge.from(o);for(var t=0;t<o.content.length;t+=2)e=e.remove(o.content[t]);return e},toObject:function(){var o={};return this.forEach(function(e,t){o[e]=t}),o},get size(){return this.content.length>>1}},ge.from=function(o){if(o instanceof ge)return o;var e=[];if(o)for(var t in o)e.push(t,o[t]);return new ge(e)};class O{constructor(e,t){if(this.content=e,this.size=t||0,t==null)for(let n=0;n<e.length;n++)this.size+=e[n].nodeSize}nodesBetween(e,t,n,r=0,i){for(let s=0,l=0;l<t;s++){let a=this.content[s],c=l+a.nodeSize;if(c>e&&n(a,r+l,i||null,s)!==!1&&a.content.size){let d=l+1;a.nodesBetween(Math.max(0,e-d),Math.min(a.content.size,t-d),n,r+d)}l=c}}descendants(e){this.nodesBetween(0,this.size,e)}textBetween(e,t,n,r){let i="",s=!0;return this.nodesBetween(e,t,(l,a)=>{let c=l.isText?l.text.slice(Math.max(e,a)-a,t-a):l.isLeaf?r?typeof r=="function"?r(l):r:l.type.spec.leafText?l.type.spec.leafText(l):"":"";l.isBlock&&(l.isLeaf&&c||l.isTextblock)&&n&&(s?s=!1:i+=n),i+=c},0),i}append(e){if(!e.size)return this;if(!this.size)return e;let t=this.lastChild,n=e.firstChild,r=this.content.slice(),i=0;for(t.isText&&t.sameMarkup(n)&&(r[r.length-1]=t.withText(t.text+n.text),i=1);i<e.content.length;i++)r.push(e.content[i]);return new O(r,this.size+e.size)}cut(e,t=this.size){if(e==0&&t==this.size)return this;let n=[],r=0;if(t>e)for(let i=0,s=0;s<t;i++){let l=this.content[i],a=s+l.nodeSize;a>e&&((s<e||a>t)&&(l=l.isText?l.cut(Math.max(0,e-s),Math.min(l.text.length,t-s)):l.cut(Math.max(0,e-s-1),Math.min(l.content.size,t-s-1))),n.push(l),r+=l.nodeSize),s=a}return new O(n,r)}cutByIndex(e,t){return e==t?O.empty:e==0&&t==this.content.length?this:new O(this.content.slice(e,t))}replaceChild(e,t){let n=this.content[e];if(n==t)return this;let r=this.content.slice(),i=this.size+t.nodeSize-n.nodeSize;return r[e]=t,new O(r,i)}addToStart(e){return new O([e].concat(this.content),this.size+e.nodeSize)}addToEnd(e){return new O(this.content.concat(e),this.size+e.nodeSize)}eq(e){if(this.content.length!=e.content.length)return!1;for(let t=0;t<this.content.length;t++)if(!this.content[t].eq(e.content[t]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(e){let t=this.content[e];if(!t)throw new RangeError("Index "+e+" out of range for "+this);return t}maybeChild(e){return this.content[e]||null}forEach(e){for(let t=0,n=0;t<this.content.length;t++){let r=this.content[t];e(r,n,t),n+=r.nodeSize}}findDiffStart(e,t=0){return gl(this,e,t)}findDiffEnd(e,t=this.size,n=e.size){return yl(this,e,t,n)}findIndex(e,t=-1){if(e==0)return Hn(0,e);if(e==this.size)return Hn(this.content.length,e);if(e>this.size||e<0)throw new RangeError(`Position ${e} outside of fragment (${this})`);for(let n=0,r=0;;n++){let i=r+this.child(n).nodeSize;if(i>=e)return i==e||t>0?Hn(n+1,i):Hn(n,r);r=i}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map(e=>e.toJSON()):null}static fromJSON(e,t){if(!t)return O.empty;if(!Array.isArray(t))throw new RangeError("Invalid input for Fragment.fromJSON");return new O(t.map(e.nodeFromJSON))}static fromArray(e){if(!e.length)return O.empty;let t,n=0;for(let r=0;r<e.length;r++){let i=e[r];n+=i.nodeSize,r&&i.isText&&e[r-1].sameMarkup(i)?(t||(t=e.slice(0,r)),t[t.length-1]=i.withText(t[t.length-1].text+i.text)):t&&t.push(i)}return new O(t||e,n)}static from(e){if(!e)return O.empty;if(e instanceof O)return e;if(Array.isArray(e))return this.fromArray(e);if(e.attrs)return new O([e],e.nodeSize);throw new RangeError("Can not convert "+e+" to a Fragment"+(e.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}}O.empty=new O([],0);const kr={index:0,offset:0};function Hn(o,e){return kr.index=o,kr.offset=e,kr}function Yn(o,e){if(o===e)return!0;if(!o||typeof o!="object"||!e||typeof e!="object")return!1;let t=Array.isArray(o);if(Array.isArray(e)!=t)return!1;if(t){if(o.length!=e.length)return!1;for(let n=0;n<o.length;n++)if(!Yn(o[n],e[n]))return!1}else{for(let n in o)if(!(n in e)||!Yn(o[n],e[n]))return!1;for(let n in e)if(!(n in o))return!1}return!0}let ee=class Xr{constructor(e,t){this.type=e,this.attrs=t}addToSet(e){let t,n=!1;for(let r=0;r<e.length;r++){let i=e[r];if(this.eq(i))return e;if(this.type.excludes(i.type))t||(t=e.slice(0,r));else{if(i.type.excludes(this.type))return e;!n&&i.type.rank>this.type.rank&&(t||(t=e.slice(0,r)),t.push(this),n=!0),t&&t.push(i)}}return t||(t=e.slice()),n||t.push(this),t}removeFromSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return e.slice(0,t).concat(e.slice(t+1));return e}isInSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return!0;return!1}eq(e){return this==e||this.type==e.type&&Yn(this.attrs,e.attrs)}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return e}static fromJSON(e,t){if(!t)throw new RangeError("Invalid input for Mark.fromJSON");let n=e.marks[t.type];if(!n)throw new RangeError(`There is no mark type ${t.type} in this schema`);let r=n.create(t.attrs);return n.checkAttrs(r.attrs),r}static sameSet(e,t){if(e==t)return!0;if(e.length!=t.length)return!1;for(let n=0;n<e.length;n++)if(!e[n].eq(t[n]))return!1;return!0}static setFrom(e){if(!e||Array.isArray(e)&&e.length==0)return Xr.none;if(e instanceof Xr)return[e];let t=e.slice();return t.sort((n,r)=>n.type.rank-r.type.rank),t}};ee.none=[];class Xn extends Error{}class L{constructor(e,t,n){this.content=e,this.openStart=t,this.openEnd=n}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(e,t){let n=wl(this.content,e+this.openStart,t);return n&&new L(n,this.openStart,this.openEnd)}removeBetween(e,t){return new L(Cl(this.content,e+this.openStart,t+this.openStart),this.openStart,this.openEnd)}eq(e){return this.content.eq(e.content)&&this.openStart==e.openStart&&this.openEnd==e.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let e={content:this.content.toJSON()};return this.openStart>0&&(e.openStart=this.openStart),this.openEnd>0&&(e.openEnd=this.openEnd),e}static fromJSON(e,t){if(!t)return L.empty;let n=t.openStart||0,r=t.openEnd||0;if(typeof n!="number"||typeof r!="number")throw new RangeError("Invalid input for Slice.fromJSON");return new L(O.fromJSON(e,t.content),n,r)}static maxOpen(e,t=!0){let n=0,r=0;for(let i=e.firstChild;i&&!i.isLeaf&&(t||!i.type.spec.isolating);i=i.firstChild)n++;for(let i=e.lastChild;i&&!i.isLeaf&&(t||!i.type.spec.isolating);i=i.lastChild)r++;return new L(e,n,r)}}function Cl(o,e,t){let{index:n,offset:r}=o.findIndex(e),i=o.maybeChild(n),{index:s,offset:l}=o.findIndex(t);if(r==e||i.isText){if(l!=t&&!o.child(s).isText)throw new RangeError("Removing non-flat range");return o.cut(0,e).append(o.cut(t))}if(n!=s)throw new RangeError("Removing non-flat range");return o.replaceChild(n,i.copy(Cl(i.content,e-r-1,t-r-1)))}function wl(o,e,t,n){let{index:r,offset:i}=o.findIndex(e),s=o.maybeChild(r);if(i==e||s.isText)return o.cut(0,e).append(t).append(o.cut(e));let l=wl(s.content,e-i-1,t);return l&&o.replaceChild(r,s.copy(l))}function Cc(o,e,t){if(t.openStart>o.depth)throw new Xn("Inserted content deeper than insertion position");if(o.depth-t.openStart!=e.depth-t.openEnd)throw new Xn("Inconsistent open depths");return vl(o,e,t,0)}function vl(o,e,t,n){let r=o.index(n),i=o.node(n);if(r==e.index(n)&&n<o.depth-t.openStart){let s=vl(o,e,t,n+1);return i.copy(i.content.replaceChild(r,s))}if(t.content.size){if(t.openStart||t.openEnd||o.depth!=n||e.depth!=n){let{start:s,end:l}=function(a,c){let d=c.depth-a.openStart,h=c.node(d).copy(a.content);for(let u=d-1;u>=0;u--)h=c.node(u).copy(O.from(h));return{start:h.resolveNoCache(a.openStart+d),end:h.resolveNoCache(h.content.size-a.openEnd-d)}}(t,o);return Vt(i,bl(o,s,l,e,n))}{let s=o.parent,l=s.content;return Vt(s,l.cut(0,o.parentOffset).append(t.content).append(l.cut(e.parentOffset)))}}return Vt(i,Qn(o,e,n))}function xl(o,e){if(!e.type.compatibleContent(o.type))throw new Xn("Cannot join "+e.type.name+" onto "+o.type.name)}function Qr(o,e,t){let n=o.node(t);return xl(n,e.node(t)),n}function Bt(o,e){let t=e.length-1;t>=0&&o.isText&&o.sameMarkup(e[t])?e[t]=o.withText(e[t].text+o.text):e.push(o)}function Sn(o,e,t,n){let r=(e||o).node(t),i=0,s=e?e.index(t):r.childCount;o&&(i=o.index(t),o.depth>t?i++:o.textOffset&&(Bt(o.nodeAfter,n),i++));for(let l=i;l<s;l++)Bt(r.child(l),n);e&&e.depth==t&&e.textOffset&&Bt(e.nodeBefore,n)}function Vt(o,e){return o.type.checkContent(e),o.copy(e)}function bl(o,e,t,n,r){let i=o.depth>r&&Qr(o,e,r+1),s=n.depth>r&&Qr(t,n,r+1),l=[];return Sn(null,o,r,l),i&&s&&e.index(r)==t.index(r)?(xl(i,s),Bt(Vt(i,bl(o,e,t,n,r+1)),l)):(i&&Bt(Vt(i,Qn(o,e,r+1)),l),Sn(e,t,r,l),s&&Bt(Vt(s,Qn(t,n,r+1)),l)),Sn(n,null,r,l),new O(l)}function Qn(o,e,t){let n=[];return Sn(null,o,t,n),o.depth>t&&Bt(Vt(Qr(o,e,t+1),Qn(o,e,t+1)),n),Sn(e,null,t,n),new O(n)}L.empty=new L(O.empty,0,0);class An{constructor(e,t,n){this.pos=e,this.path=t,this.parentOffset=n,this.depth=t.length/3-1}resolveDepth(e){return e==null?this.depth:e<0?this.depth+e:e}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(e){return this.path[3*this.resolveDepth(e)]}index(e){return this.path[3*this.resolveDepth(e)+1]}indexAfter(e){return e=this.resolveDepth(e),this.index(e)+(e!=this.depth||this.textOffset?1:0)}start(e){return(e=this.resolveDepth(e))==0?0:this.path[3*e-1]+1}end(e){return e=this.resolveDepth(e),this.start(e)+this.node(e).content.size}before(e){if(!(e=this.resolveDepth(e)))throw new RangeError("There is no position before the top-level node");return e==this.depth+1?this.pos:this.path[3*e-1]}after(e){if(!(e=this.resolveDepth(e)))throw new RangeError("There is no position after the top-level node");return e==this.depth+1?this.pos:this.path[3*e-1]+this.path[3*e].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let e=this.parent,t=this.index(this.depth);if(t==e.childCount)return null;let n=this.pos-this.path[this.path.length-1],r=e.child(t);return n?e.child(t).cut(n):r}get nodeBefore(){let e=this.index(this.depth),t=this.pos-this.path[this.path.length-1];return t?this.parent.child(e).cut(0,t):e==0?null:this.parent.child(e-1)}posAtIndex(e,t){t=this.resolveDepth(t);let n=this.path[3*t],r=t==0?0:this.path[3*t-1]+1;for(let i=0;i<e;i++)r+=n.child(i).nodeSize;return r}marks(){let e=this.parent,t=this.index();if(e.content.size==0)return ee.none;if(this.textOffset)return e.child(t).marks;let n=e.maybeChild(t-1),r=e.maybeChild(t);if(!n){let l=n;n=r,r=l}let i=n.marks;for(var s=0;s<i.length;s++)i[s].type.spec.inclusive!==!1||r&&i[s].isInSet(r.marks)||(i=i[s--].removeFromSet(i));return i}marksAcross(e){let t=this.parent.maybeChild(this.index());if(!t||!t.isInline)return null;let n=t.marks,r=e.parent.maybeChild(e.index());for(var i=0;i<n.length;i++)n[i].type.spec.inclusive!==!1||r&&n[i].isInSet(r.marks)||(n=n[i--].removeFromSet(n));return n}sharedDepth(e){for(let t=this.depth;t>0;t--)if(this.start(t)<=e&&this.end(t)>=e)return t;return 0}blockRange(e=this,t){if(e.pos<this.pos)return e.blockRange(this);for(let n=this.depth-(this.parent.inlineContent||this.pos==e.pos?1:0);n>=0;n--)if(e.pos<=this.end(n)&&(!t||t(this.node(n))))return new er(this,e,n);return null}sameParent(e){return this.pos-this.parentOffset==e.pos-e.parentOffset}max(e){return e.pos>this.pos?e:this}min(e){return e.pos<this.pos?e:this}toString(){let e="";for(let t=1;t<=this.depth;t++)e+=(e?"/":"")+this.node(t).type.name+"_"+this.index(t-1);return e+":"+this.parentOffset}static resolve(e,t){if(!(t>=0&&t<=e.content.size))throw new RangeError("Position "+t+" out of range");let n=[],r=0,i=t;for(let s=e;;){let{index:l,offset:a}=s.content.findIndex(i),c=i-a;if(n.push(s,l,r+a),!c||(s=s.child(l),s.isText))break;i=c-1,r+=a+1}return new An(t,n,i)}static resolveCached(e,t){let n=Go.get(e);if(n)for(let i=0;i<n.elts.length;i++){let s=n.elts[i];if(s.pos==t)return s}else Go.set(e,n=new wc);let r=n.elts[n.i]=An.resolve(e,t);return n.i=(n.i+1)%vc,r}}class wc{constructor(){this.elts=[],this.i=0}}const vc=12,Go=new WeakMap;class er{constructor(e,t,n){this.$from=e,this.$to=t,this.depth=n}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}}const xc=Object.create(null);let Ht=class eo{constructor(e,t,n,r=ee.none){this.type=e,this.attrs=t,this.marks=r,this.content=n||O.empty}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(e){return this.content.child(e)}maybeChild(e){return this.content.maybeChild(e)}forEach(e){this.content.forEach(e)}nodesBetween(e,t,n,r=0){this.content.nodesBetween(e,t,n,r,this)}descendants(e){this.nodesBetween(0,this.content.size,e)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(e,t,n,r){return this.content.textBetween(e,t,n,r)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(e){return this==e||this.sameMarkup(e)&&this.content.eq(e.content)}sameMarkup(e){return this.hasMarkup(e.type,e.attrs,e.marks)}hasMarkup(e,t,n){return this.type==e&&Yn(this.attrs,t||e.defaultAttrs||xc)&&ee.sameSet(this.marks,n||ee.none)}copy(e=null){return e==this.content?this:new eo(this.type,this.attrs,e,this.marks)}mark(e){return e==this.marks?this:new eo(this.type,this.attrs,this.content,e)}cut(e,t=this.content.size){return e==0&&t==this.content.size?this:this.copy(this.content.cut(e,t))}slice(e,t=this.content.size,n=!1){if(e==t)return L.empty;let r=this.resolve(e),i=this.resolve(t),s=n?0:r.sharedDepth(t),l=r.start(s),a=r.node(s).content.cut(r.pos-l,i.pos-l);return new L(a,r.depth-s,i.depth-s)}replace(e,t,n){return Cc(this.resolve(e),this.resolve(t),n)}nodeAt(e){for(let t=this;;){let{index:n,offset:r}=t.content.findIndex(e);if(t=t.maybeChild(n),!t)return null;if(r==e||t.isText)return t;e-=r+1}}childAfter(e){let{index:t,offset:n}=this.content.findIndex(e);return{node:this.content.maybeChild(t),index:t,offset:n}}childBefore(e){if(e==0)return{node:null,index:0,offset:0};let{index:t,offset:n}=this.content.findIndex(e);if(n<e)return{node:this.content.child(t),index:t,offset:n};let r=this.content.child(t-1);return{node:r,index:t-1,offset:n-r.nodeSize}}resolve(e){return An.resolveCached(this,e)}resolveNoCache(e){return An.resolve(this,e)}rangeHasMark(e,t,n){let r=!1;return t>e&&this.nodesBetween(e,t,i=>(n.isInSet(i.marks)&&(r=!0),!r)),r}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let e=this.type.name;return this.content.size&&(e+="("+this.content.toStringInner()+")"),$l(this.marks,e)}contentMatchAt(e){let t=this.type.contentMatch.matchFragment(this.content,0,e);if(!t)throw new Error("Called contentMatchAt on a node with invalid content");return t}canReplace(e,t,n=O.empty,r=0,i=n.childCount){let s=this.contentMatchAt(e).matchFragment(n,r,i),l=s&&s.matchFragment(this.content,t);if(!l||!l.validEnd)return!1;for(let a=r;a<i;a++)if(!this.type.allowsMarks(n.child(a).marks))return!1;return!0}canReplaceWith(e,t,n,r){if(r&&!this.type.allowsMarks(r))return!1;let i=this.contentMatchAt(e).matchType(n),s=i&&i.matchFragment(this.content,t);return!!s&&s.validEnd}canAppend(e){return e.content.size?this.canReplace(this.childCount,this.childCount,e.content):this.type.compatibleContent(e.type)}check(){this.type.checkContent(this.content),this.type.checkAttrs(this.attrs);let e=ee.none;for(let t=0;t<this.marks.length;t++){let n=this.marks[t];n.type.checkAttrs(n.attrs),e=n.addToSet(e)}if(!ee.sameSet(e,this.marks))throw new RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(t=>t.type.name)}`);this.content.forEach(t=>t.check())}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return this.content.size&&(e.content=this.content.toJSON()),this.marks.length&&(e.marks=this.marks.map(t=>t.toJSON())),e}static fromJSON(e,t){if(!t)throw new RangeError("Invalid input for Node.fromJSON");let n;if(t.marks){if(!Array.isArray(t.marks))throw new RangeError("Invalid mark data for Node.fromJSON");n=t.marks.map(e.markFromJSON)}if(t.type=="text"){if(typeof t.text!="string")throw new RangeError("Invalid text node in JSON");return e.text(t.text,n)}let r=O.fromJSON(e,t.content),i=e.nodeType(t.type).create(t.attrs,r,n);return i.type.checkAttrs(i.attrs),i}};Ht.prototype.text=void 0;class tr extends Ht{constructor(e,t,n,r){if(super(e,t,null,r),!n)throw new RangeError("Empty text nodes are not allowed");this.text=n}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):$l(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(e,t){return this.text.slice(e,t)}get nodeSize(){return this.text.length}mark(e){return e==this.marks?this:new tr(this.type,this.attrs,this.text,e)}withText(e){return e==this.text?this:new tr(this.type,this.attrs,e,this.marks)}cut(e=0,t=this.text.length){return e==0&&t==this.text.length?this:this.withText(this.text.slice(e,t))}eq(e){return this.sameMarkup(e)&&this.text==e.text}toJSON(){let e=super.toJSON();return e.text=this.text,e}}function $l(o,e){for(let t=o.length-1;t>=0;t--)e=o[t].type.name+"("+e+")";return e}class Kt{constructor(e){this.validEnd=e,this.next=[],this.wrapCache=[]}static parse(e,t){let n=new bc(e,t);if(n.next==null)return Kt.empty;let r=kl(n);n.next&&n.err("Unexpected trailing text");let i=function(s){let l=Object.create(null);return a(Yo(s,0));function a(c){let d=[];c.forEach(u=>{s[u].forEach(({term:f,to:p})=>{if(!f)return;let m;for(let g=0;g<d.length;g++)d[g][0]==f&&(m=d[g][1]);Yo(s,p).forEach(g=>{m||d.push([f,m=[]]),m.indexOf(g)==-1&&m.push(g)})})});let h=l[c.join(",")]=new Kt(c.indexOf(s.length-1)>-1);for(let u=0;u<d.length;u++){let f=d[u][1].sort(Ml);h.next.push({type:d[u][0],next:l[f.join(",")]||a(f)})}return h}}(function(s){let l=[[]];return d(h(s,0),a()),l;function a(){return l.push([])-1}function c(u,f,p){let m={term:p,to:f};return l[u].push(m),m}function d(u,f){u.forEach(p=>p.to=f)}function h(u,f){if(u.type=="choice")return u.exprs.reduce((p,m)=>p.concat(h(m,f)),[]);if(u.type!="seq"){if(u.type=="star"){let p=a();return c(f,p),d(h(u.expr,p),p),[c(p)]}if(u.type=="plus"){let p=a();return d(h(u.expr,f),p),d(h(u.expr,p),p),[c(p)]}if(u.type=="opt")return[c(f)].concat(h(u.expr,f));if(u.type=="range"){let p=f;for(let m=0;m<u.min;m++){let g=a();d(h(u.expr,p),g),p=g}if(u.max==-1)d(h(u.expr,p),p);else for(let m=u.min;m<u.max;m++){let g=a();c(p,g),d(h(u.expr,p),g),p=g}return[c(p)]}if(u.type=="name")return[c(f,void 0,u.value)];throw new Error("Unknown expr type")}for(let p=0;;p++){let m=h(u.exprs[p],f);if(p==u.exprs.length-1)return m;d(m,f=a())}}}(r));return function(s,l){for(let a=0,c=[s];a<c.length;a++){let d=c[a],h=!d.validEnd,u=[];for(let f=0;f<d.next.length;f++){let{type:p,next:m}=d.next[f];u.push(p.name),!h||p.isText||p.hasRequiredAttrs()||(h=!1),c.indexOf(m)==-1&&c.push(m)}h&&l.err("Only non-generatable nodes ("+u.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}(i,n),i}matchType(e){for(let t=0;t<this.next.length;t++)if(this.next[t].type==e)return this.next[t].next;return null}matchFragment(e,t=0,n=e.childCount){let r=this;for(let i=t;r&&i<n;i++)r=r.matchType(e.child(i).type);return r}get inlineContent(){return this.next.length!=0&&this.next[0].type.isInline}get defaultType(){for(let e=0;e<this.next.length;e++){let{type:t}=this.next[e];if(!t.isText&&!t.hasRequiredAttrs())return t}return null}compatible(e){for(let t=0;t<this.next.length;t++)for(let n=0;n<e.next.length;n++)if(this.next[t].type==e.next[n].type)return!0;return!1}fillBefore(e,t=!1,n=0){let r=[this];return function i(s,l){let a=s.matchFragment(e,n);if(a&&(!t||a.validEnd))return O.from(l.map(c=>c.createAndFill()));for(let c=0;c<s.next.length;c++){let{type:d,next:h}=s.next[c];if(!d.isText&&!d.hasRequiredAttrs()&&r.indexOf(h)==-1){r.push(h);let u=i(h,l.concat(d));if(u)return u}}return null}(this,[])}findWrapping(e){for(let n=0;n<this.wrapCache.length;n+=2)if(this.wrapCache[n]==e)return this.wrapCache[n+1];let t=this.computeWrapping(e);return this.wrapCache.push(e,t),t}computeWrapping(e){let t=Object.create(null),n=[{match:this,type:null,via:null}];for(;n.length;){let r=n.shift(),i=r.match;if(i.matchType(e)){let s=[];for(let l=r;l.type;l=l.via)s.push(l.type);return s.reverse()}for(let s=0;s<i.next.length;s++){let{type:l,next:a}=i.next[s];l.isLeaf||l.hasRequiredAttrs()||l.name in t||r.type&&!a.validEnd||(n.push({match:l.contentMatch,type:l,via:r}),t[l.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(e){if(e>=this.next.length)throw new RangeError(`There's no ${e}th edge in this content match`);return this.next[e]}toString(){let e=[];return function t(n){e.push(n);for(let r=0;r<n.next.length;r++)e.indexOf(n.next[r].next)==-1&&t(n.next[r].next)}(this),e.map((t,n)=>{let r=n+(t.validEnd?"*":" ")+" ";for(let i=0;i<t.next.length;i++)r+=(i?", ":"")+t.next[i].type.name+"->"+e.indexOf(t.next[i].next);return r}).join(`
`)}}Kt.empty=new Kt(!0);class bc{constructor(e,t){this.string=e,this.nodeTypes=t,this.inline=null,this.pos=0,this.tokens=e.split(/\s*(?=\b|\W|$)/),this.tokens[this.tokens.length-1]==""&&this.tokens.pop(),this.tokens[0]==""&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(e){return this.next==e&&(this.pos++||!0)}err(e){throw new SyntaxError(e+" (in content expression '"+this.string+"')")}}function kl(o){let e=[];do e.push($c(o));while(o.eat("|"));return e.length==1?e[0]:{type:"choice",exprs:e}}function $c(o){let e=[];do e.push(kc(o));while(o.next&&o.next!=")"&&o.next!="|");return e.length==1?e[0]:{type:"seq",exprs:e}}function kc(o){let e=function(t){if(t.eat("(")){let n=kl(t);return t.eat(")")||t.err("Missing closing paren"),n}if(!/\W/.test(t.next)){let n=function(r,i){let s=r.nodeTypes,l=s[i];if(l)return[l];let a=[];for(let c in s){let d=s[c];d.isInGroup(i)&&a.push(d)}return a.length==0&&r.err("No node type or group '"+i+"' found"),a}(t,t.next).map(r=>(t.inline==null?t.inline=r.isInline:t.inline!=r.isInline&&t.err("Mixing inline and block content"),{type:"name",value:r}));return t.pos++,n.length==1?n[0]:{type:"choice",exprs:n}}t.err("Unexpected token '"+t.next+"'")}(o);for(;;)if(o.eat("+"))e={type:"plus",expr:e};else if(o.eat("*"))e={type:"star",expr:e};else if(o.eat("?"))e={type:"opt",expr:e};else{if(!o.eat("{"))break;e=Mc(o,e)}return e}function Zo(o){/\D/.test(o.next)&&o.err("Expected number, got '"+o.next+"'");let e=Number(o.next);return o.pos++,e}function Mc(o,e){let t=Zo(o),n=t;return o.eat(",")&&(n=o.next!="}"?Zo(o):-1),o.eat("}")||o.err("Unclosed braced range"),{type:"range",min:t,max:n,expr:e}}function Ml(o,e){return e-o}function Yo(o,e){let t=[];return function n(r){let i=o[r];if(i.length==1&&!i[0].term)return n(i[0].to);t.push(r);for(let s=0;s<i.length;s++){let{term:l,to:a}=i[s];l||t.indexOf(a)!=-1||n(a)}}(e),t.sort(Ml)}function Sl(o){let e=Object.create(null);for(let t in o){let n=o[t];if(!n.hasDefault)return null;e[t]=n.default}return e}function Ol(o,e){let t=Object.create(null);for(let n in o){let r=e&&e[n];if(r===void 0){let i=o[n];if(!i.hasDefault)throw new RangeError("No value supplied for attribute "+n);r=i.default}t[n]=r}return t}function El(o,e,t,n){for(let r in e)if(!(r in o))throw new RangeError(`Unsupported attribute ${r} for ${t} of type ${r}`);for(let r in o){let i=o[r];i.validate&&i.validate(e[r])}}function Tl(o,e){let t=Object.create(null);if(e)for(let n in e)t[n]=new Sc(o,n,e[n]);return t}let Xo=class Nl{constructor(e,t,n){this.name=e,this.schema=t,this.spec=n,this.markSet=null,this.groups=n.group?n.group.split(" "):[],this.attrs=Tl(e,n.attrs),this.defaultAttrs=Sl(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(n.inline||e=="text"),this.isText=e=="text"}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==Kt.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}isInGroup(e){return this.groups.indexOf(e)>-1}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let e in this.attrs)if(this.attrs[e].isRequired)return!0;return!1}compatibleContent(e){return this==e||this.contentMatch.compatible(e.contentMatch)}computeAttrs(e){return!e&&this.defaultAttrs?this.defaultAttrs:Ol(this.attrs,e)}create(e=null,t,n){if(this.isText)throw new Error("NodeType.create can't construct text nodes");return new Ht(this,this.computeAttrs(e),O.from(t),ee.setFrom(n))}createChecked(e=null,t,n){return t=O.from(t),this.checkContent(t),new Ht(this,this.computeAttrs(e),t,ee.setFrom(n))}createAndFill(e=null,t,n){if(e=this.computeAttrs(e),(t=O.from(t)).size){let s=this.contentMatch.fillBefore(t);if(!s)return null;t=s.append(t)}let r=this.contentMatch.matchFragment(t),i=r&&r.fillBefore(O.empty,!0);return i?new Ht(this,e,t.append(i),ee.setFrom(n)):null}validContent(e){let t=this.contentMatch.matchFragment(e);if(!t||!t.validEnd)return!1;for(let n=0;n<e.childCount;n++)if(!this.allowsMarks(e.child(n).marks))return!1;return!0}checkContent(e){if(!this.validContent(e))throw new RangeError(`Invalid content for node ${this.name}: ${e.toString().slice(0,50)}`)}checkAttrs(e){El(this.attrs,e,"node",this.name)}allowsMarkType(e){return this.markSet==null||this.markSet.indexOf(e)>-1}allowsMarks(e){if(this.markSet==null)return!0;for(let t=0;t<e.length;t++)if(!this.allowsMarkType(e[t].type))return!1;return!0}allowedMarks(e){if(this.markSet==null)return e;let t;for(let n=0;n<e.length;n++)this.allowsMarkType(e[n].type)?t&&t.push(e[n]):t||(t=e.slice(0,n));return t?t.length?t:ee.none:e}static compile(e,t){let n=Object.create(null);e.forEach((i,s)=>n[i]=new Nl(i,t,s));let r=t.spec.topNode||"doc";if(!n[r])throw new RangeError("Schema is missing its top node type ('"+r+"')");if(!n.text)throw new RangeError("Every schema needs a 'text' type");for(let i in n.text.attrs)throw new RangeError("The text node type should not have attributes");return n}};class Sc{constructor(e,t,n){this.hasDefault=Object.prototype.hasOwnProperty.call(n,"default"),this.default=n.default,this.validate=typeof n.validate=="string"?function(r,i,s){let l=s.split("|");return a=>{let c=a===null?"null":typeof a;if(l.indexOf(c)<0)throw new RangeError(`Expected value of type ${l} for attribute ${i} on type ${r}, got ${c}`)}}(e,t,n.validate):n.validate}get isRequired(){return!this.hasDefault}}class fr{constructor(e,t,n,r){this.name=e,this.rank=t,this.schema=n,this.spec=r,this.attrs=Tl(e,r.attrs),this.excluded=null;let i=Sl(this.attrs);this.instance=i?new ee(this,i):null}create(e=null){return!e&&this.instance?this.instance:new ee(this,Ol(this.attrs,e))}static compile(e,t){let n=Object.create(null),r=0;return e.forEach((i,s)=>n[i]=new fr(i,r++,t,s)),n}removeFromSet(e){for(var t=0;t<e.length;t++)e[t].type==this&&(e=e.slice(0,t).concat(e.slice(t+1)),t--);return e}isInSet(e){for(let t=0;t<e.length;t++)if(e[t].type==this)return e[t]}checkAttrs(e){El(this.attrs,e,"mark",this.name)}excludes(e){return this.excluded.indexOf(e)>-1}}class Oc{constructor(e){this.linebreakReplacement=null,this.cached=Object.create(null);let t=this.spec={};for(let r in e)t[r]=e[r];t.nodes=ge.from(e.nodes),t.marks=ge.from(e.marks||{}),this.nodes=Xo.compile(this.spec.nodes,this),this.marks=fr.compile(this.spec.marks,this);let n=Object.create(null);for(let r in this.nodes){if(r in this.marks)throw new RangeError(r+" can not be both a node and a mark");let i=this.nodes[r],s=i.spec.content||"",l=i.spec.marks;if(i.contentMatch=n[s]||(n[s]=Kt.parse(s,this.nodes)),i.inlineContent=i.contentMatch.inlineContent,i.spec.linebreakReplacement){if(this.linebreakReplacement)throw new RangeError("Multiple linebreak nodes defined");if(!i.isInline||!i.isLeaf)throw new RangeError("Linebreak replacement nodes must be inline leaf nodes");this.linebreakReplacement=i}i.markSet=l=="_"?null:l?Qo(this,l.split(" ")):l!=""&&i.inlineContent?null:[]}for(let r in this.marks){let i=this.marks[r],s=i.spec.excludes;i.excluded=s==null?[i]:s==""?[]:Qo(this,s.split(" "))}this.nodeFromJSON=this.nodeFromJSON.bind(this),this.markFromJSON=this.markFromJSON.bind(this),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(e,t=null,n,r){if(typeof e=="string")e=this.nodeType(e);else{if(!(e instanceof Xo))throw new RangeError("Invalid node type: "+e);if(e.schema!=this)throw new RangeError("Node type from different schema used ("+e.name+")")}return e.createChecked(t,n,r)}text(e,t){let n=this.nodes.text;return new tr(n,n.defaultAttrs,e,ee.setFrom(t))}mark(e,t){return typeof e=="string"&&(e=this.marks[e]),e.create(t)}nodeFromJSON(e){return Ht.fromJSON(this,e)}markFromJSON(e){return ee.fromJSON(this,e)}nodeType(e){let t=this.nodes[e];if(!t)throw new RangeError("Unknown node type: "+e);return t}}function Qo(o,e){let t=[];for(let n=0;n<e.length;n++){let r=e[n],i=o.marks[r],s=i;if(i)t.push(i);else for(let l in o.marks){let a=o.marks[l];(r=="_"||a.spec.group&&a.spec.group.split(" ").indexOf(r)>-1)&&t.push(s=a)}if(!s)throw new SyntaxError("Unknown mark type: '"+e[n]+"'")}return t}class pn{constructor(e,t){this.schema=e,this.rules=t,this.tags=[],this.styles=[];let n=this.matchedStyles=[];t.forEach(r=>{if(function(i){return i.tag!=null}(r))this.tags.push(r);else if(function(i){return i.style!=null}(r)){let i=/[^=]*/.exec(r.style)[0];n.indexOf(i)<0&&n.push(i),this.styles.push(r)}}),this.normalizeLists=!this.tags.some(r=>{if(!/^(ul|ol)\b/.test(r.tag)||!r.node)return!1;let i=e.nodes[r.node];return i.contentMatch.matchType(i)})}parse(e,t={}){let n=new ni(this,t,!1);return n.addAll(e,ee.none,t.from,t.to),n.finish()}parseSlice(e,t={}){let n=new ni(this,t,!0);return n.addAll(e,ee.none,t.from,t.to),L.maxOpen(n.finish())}matchTag(e,t,n){for(let r=n?this.tags.indexOf(n)+1:0;r<this.tags.length;r++){let i=this.tags[r];if(Tc(e,i.tag)&&(i.namespace===void 0||e.namespaceURI==i.namespace)&&(!i.context||t.matchesContext(i.context))){if(i.getAttrs){let s=i.getAttrs(e);if(s===!1)continue;i.attrs=s||void 0}return i}}}matchStyle(e,t,n,r){for(let i=r?this.styles.indexOf(r)+1:0;i<this.styles.length;i++){let s=this.styles[i],l=s.style;if(!(l.indexOf(e)!=0||s.context&&!n.matchesContext(s.context)||l.length>e.length&&(l.charCodeAt(e.length)!=61||l.slice(e.length+1)!=t))){if(s.getAttrs){let a=s.getAttrs(t);if(a===!1)continue;s.attrs=a||void 0}return s}}}static schemaRules(e){let t=[];function n(r){let i=r.priority==null?50:r.priority,s=0;for(;s<t.length;s++){let l=t[s];if((l.priority==null?50:l.priority)<i)break}t.splice(s,0,r)}for(let r in e.marks){let i=e.marks[r].spec.parseDOM;i&&i.forEach(s=>{n(s=ri(s)),s.mark||s.ignore||s.clearMark||(s.mark=r)})}for(let r in e.nodes){let i=e.nodes[r].spec.parseDOM;i&&i.forEach(s=>{n(s=ri(s)),s.node||s.ignore||s.mark||(s.node=r)})}return t}static fromSchema(e){return e.cached.domParser||(e.cached.domParser=new pn(e,pn.schemaRules(e)))}}const Ll={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},Ec={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},ei={ol:!0,ul:!0};function ti(o,e,t){return e!=null?(e?1:0)|(e==="full"?2:0):o&&o.whitespace=="pre"?3:-5&t}class Mr{constructor(e,t,n,r,i,s){this.type=e,this.attrs=t,this.marks=n,this.solid=r,this.options=s,this.content=[],this.activeMarks=ee.none,this.match=i||(4&s?null:e.contentMatch)}findWrapping(e){if(!this.match){if(!this.type)return[];let t=this.type.contentMatch.fillBefore(O.from(e));if(!t){let n,r=this.type.contentMatch;return(n=r.findWrapping(e.type))?(this.match=r,n):null}this.match=this.type.contentMatch.matchFragment(t)}return this.match.findWrapping(e.type)}finish(e){if(!(1&this.options)){let n,r=this.content[this.content.length-1];if(r&&r.isText&&(n=/[ \t\r\n\u000c]+$/.exec(r.text))){let i=r;r.text.length==n[0].length?this.content.pop():this.content[this.content.length-1]=i.withText(i.text.slice(0,i.text.length-n[0].length))}}let t=O.from(this.content);return!e&&this.match&&(t=t.append(this.match.fillBefore(O.empty,!0))),this.type?this.type.create(this.attrs,t,this.marks):t}inlineContext(e){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:e.parentNode&&!Ll.hasOwnProperty(e.parentNode.nodeName.toLowerCase())}}class ni{constructor(e,t,n){this.parser=e,this.options=t,this.isOpen=n,this.open=0;let r,i=t.topNode,s=ti(null,t.preserveWhitespace,0)|(n?4:0);r=i?new Mr(i.type,i.attrs,ee.none,!0,t.topMatch||i.type.contentMatch,s):new Mr(n?null:e.schema.topNodeType,null,ee.none,!0,null,s),this.nodes=[r],this.find=t.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(e,t){e.nodeType==3?this.addTextNode(e,t):e.nodeType==1&&this.addElement(e,t)}addTextNode(e,t){let n=e.nodeValue,r=this.top;if(2&r.options||r.inlineContext(e)||/[^ \t\r\n\u000c]/.test(n)){if(1&r.options)n=2&r.options?n.replace(/\r\n?/g,`
`):n.replace(/\r?\n|\r/g," ");else if(n=n.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(n)&&this.open==this.nodes.length-1){let i=r.content[r.content.length-1],s=e.previousSibling;(!i||s&&s.nodeName=="BR"||i.isText&&/[ \t\r\n\u000c]$/.test(i.text))&&(n=n.slice(1))}n&&this.insertNode(this.parser.schema.text(n),t),this.findInText(e)}else this.findInside(e)}addElement(e,t,n){let r,i=e.nodeName.toLowerCase();ei.hasOwnProperty(i)&&this.parser.normalizeLists&&function(l){for(let a=l.firstChild,c=null;a;a=a.nextSibling){let d=a.nodeType==1?a.nodeName.toLowerCase():null;d&&ei.hasOwnProperty(d)&&c?(c.appendChild(a),a=c):d=="li"?c=a:d&&(c=null)}}(e);let s=this.options.ruleFromNode&&this.options.ruleFromNode(e)||(r=this.parser.matchTag(e,this,n));if(s?s.ignore:Ec.hasOwnProperty(i))this.findInside(e),this.ignoreFallback(e,t);else if(!s||s.skip||s.closeParent){s&&s.closeParent?this.open=Math.max(0,this.open-1):s&&s.skip.nodeType&&(e=s.skip);let l,a=this.top,c=this.needsBlock;if(Ll.hasOwnProperty(i))a.content.length&&a.content[0].isInline&&this.open&&(this.open--,a=this.top),l=!0,a.type||(this.needsBlock=!0);else if(!e.firstChild)return void this.leafFallback(e,t);let d=s&&s.skip?t:this.readStyles(e,t);d&&this.addAll(e,d),l&&this.sync(a),this.needsBlock=c}else{let l=this.readStyles(e,t);l&&this.addElementByRule(e,s,l,s.consuming===!1?r:void 0)}}leafFallback(e,t){e.nodeName=="BR"&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(e.ownerDocument.createTextNode(`
`),t)}ignoreFallback(e,t){e.nodeName!="BR"||this.top.type&&this.top.type.inlineContent||this.findPlace(this.parser.schema.text("-"),t)}readStyles(e,t){let n=e.style;if(n&&n.length)for(let r=0;r<this.parser.matchedStyles.length;r++){let i=this.parser.matchedStyles[r],s=n.getPropertyValue(i);if(s)for(let l;;){let a=this.parser.matchStyle(i,s,this,l);if(!a)break;if(a.ignore)return null;if(t=a.clearMark?t.filter(c=>!a.clearMark(c)):t.concat(this.parser.schema.marks[a.mark].create(a.attrs)),a.consuming!==!1)break;l=a}}return t}addElementByRule(e,t,n,r){let i,s;if(t.node)if(s=this.parser.schema.nodes[t.node],s.isLeaf)this.insertNode(s.create(t.attrs),n)||this.leafFallback(e,n);else{let a=this.enter(s,t.attrs||null,n,t.preserveWhitespace);a&&(i=!0,n=a)}else{let a=this.parser.schema.marks[t.mark];n=n.concat(a.create(t.attrs))}let l=this.top;if(s&&s.isLeaf)this.findInside(e);else if(r)this.addElement(e,n,r);else if(t.getContent)this.findInside(e),t.getContent(e,this.parser.schema).forEach(a=>this.insertNode(a,n));else{let a=e;typeof t.contentElement=="string"?a=e.querySelector(t.contentElement):typeof t.contentElement=="function"?a=t.contentElement(e):t.contentElement&&(a=t.contentElement),this.findAround(e,a,!0),this.addAll(a,n),this.findAround(e,a,!1)}i&&this.sync(l)&&this.open--}addAll(e,t,n,r){let i=n||0;for(let s=n?e.childNodes[n]:e.firstChild,l=r==null?null:e.childNodes[r];s!=l;s=s.nextSibling,++i)this.findAtPoint(e,i),this.addDOM(s,t);this.findAtPoint(e,i)}findPlace(e,t){let n,r;for(let i=this.open;i>=0;i--){let s=this.nodes[i],l=s.findWrapping(e);if(l&&(!n||n.length>l.length)&&(n=l,r=s,!l.length)||s.solid)break}if(!n)return null;this.sync(r);for(let i=0;i<n.length;i++)t=this.enterInner(n[i],null,t,!1);return t}insertNode(e,t){if(e.isInline&&this.needsBlock&&!this.top.type){let r=this.textblockFromContext();r&&(t=this.enterInner(r,null,t))}let n=this.findPlace(e,t);if(n){this.closeExtra();let r=this.top;r.match&&(r.match=r.match.matchType(e.type));let i=ee.none;for(let s of n.concat(e.marks))(r.type?r.type.allowsMarkType(s.type):oi(s.type,e.type))&&(i=s.addToSet(i));return r.content.push(e.mark(i)),!0}return!1}enter(e,t,n,r){let i=this.findPlace(e.create(t),n);return i&&(i=this.enterInner(e,t,n,!0,r)),i}enterInner(e,t,n,r=!1,i){this.closeExtra();let s=this.top;s.match=s.match&&s.match.matchType(e);let l=ti(e,i,s.options);4&s.options&&s.content.length==0&&(l|=4);let a=ee.none;return n=n.filter(c=>!(s.type?s.type.allowsMarkType(c.type):oi(c.type,e))||(a=c.addToSet(a),!1)),this.nodes.push(new Mr(e,t,a,r,null,l)),this.open++,n}closeExtra(e=!1){let t=this.nodes.length-1;if(t>this.open){for(;t>this.open;t--)this.nodes[t-1].content.push(this.nodes[t].finish(e));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(this.isOpen||this.options.topOpen)}sync(e){for(let t=this.open;t>=0;t--)if(this.nodes[t]==e)return this.open=t,!0;return!1}get currentPos(){this.closeExtra();let e=0;for(let t=this.open;t>=0;t--){let n=this.nodes[t].content;for(let r=n.length-1;r>=0;r--)e+=n[r].nodeSize;t&&e++}return e}findAtPoint(e,t){if(this.find)for(let n=0;n<this.find.length;n++)this.find[n].node==e&&this.find[n].offset==t&&(this.find[n].pos=this.currentPos)}findInside(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].pos==null&&e.nodeType==1&&e.contains(this.find[t].node)&&(this.find[t].pos=this.currentPos)}findAround(e,t,n){if(e!=t&&this.find)for(let r=0;r<this.find.length;r++)this.find[r].pos==null&&e.nodeType==1&&e.contains(this.find[r].node)&&t.compareDocumentPosition(this.find[r].node)&(n?2:4)&&(this.find[r].pos=this.currentPos)}findInText(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].node==e&&(this.find[t].pos=this.currentPos-(e.nodeValue.length-this.find[t].offset))}matchesContext(e){if(e.indexOf("|")>-1)return e.split(/\s*\|\s*/).some(this.matchesContext,this);let t=e.split("/"),n=this.options.context,r=!(this.isOpen||n&&n.parent.type!=this.nodes[0].type),i=-(n?n.depth+1:0)+(r?0:1),s=(l,a)=>{for(;l>=0;l--){let c=t[l];if(c==""){if(l==t.length-1||l==0)continue;for(;a>=i;a--)if(s(l-1,a))return!0;return!1}{let d=a>0||a==0&&r?this.nodes[a].type:n&&a>=i?n.node(a-i).type:null;if(!d||d.name!=c&&!d.isInGroup(c))return!1;a--}}return!0};return s(t.length-1,this.open)}textblockFromContext(){let e=this.options.context;if(e)for(let t=e.depth;t>=0;t--){let n=e.node(t).contentMatchAt(e.indexAfter(t)).defaultType;if(n&&n.isTextblock&&n.defaultAttrs)return n}for(let t in this.parser.schema.nodes){let n=this.parser.schema.nodes[t];if(n.isTextblock&&n.defaultAttrs)return n}}}function Tc(o,e){return(o.matches||o.msMatchesSelector||o.webkitMatchesSelector||o.mozMatchesSelector).call(o,e)}function ri(o){let e={};for(let t in o)e[t]=o[t];return e}function oi(o,e){let t=e.schema.nodes;for(let n in t){let r=t[n];if(!r.allowsMarkType(o))continue;let i=[],s=l=>{i.push(l);for(let a=0;a<l.edgeCount;a++){let{type:c,next:d}=l.edge(a);if(c==e||i.indexOf(d)<0&&s(d))return!0}};if(s(r.contentMatch))return!0}}class Ut{constructor(e,t){this.nodes=e,this.marks=t}serializeFragment(e,t={},n){n||(n=Sr(t).createDocumentFragment());let r=n,i=[];return e.forEach(s=>{if(i.length||s.marks.length){let l=0,a=0;for(;l<i.length&&a<s.marks.length;){let c=s.marks[a];if(this.marks[c.type.name]){if(!c.eq(i[l][0])||c.type.spec.spanning===!1)break;l++,a++}else a++}for(;l<i.length;)r=i.pop()[1];for(;a<s.marks.length;){let c=s.marks[a++],d=this.serializeMark(c,s.isInline,t);d&&(i.push([c,r]),r.appendChild(d.dom),r=d.contentDOM||d.dom)}}r.appendChild(this.serializeNodeInner(s,t))}),n}serializeNodeInner(e,t){let{dom:n,contentDOM:r}=Un(Sr(t),this.nodes[e.type.name](e),null,e.attrs);if(r){if(e.isLeaf)throw new RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(e.content,t,r)}return n}serializeNode(e,t={}){let n=this.serializeNodeInner(e,t);for(let r=e.marks.length-1;r>=0;r--){let i=this.serializeMark(e.marks[r],e.isInline,t);i&&((i.contentDOM||i.dom).appendChild(n),n=i.dom)}return n}serializeMark(e,t,n={}){let r=this.marks[e.type.name];return r&&Un(Sr(n),r(e,t),null,e.attrs)}static renderSpec(e,t,n=null,r){return Un(e,t,n,r)}static fromSchema(e){return e.cached.domSerializer||(e.cached.domSerializer=new Ut(this.nodesFromSchema(e),this.marksFromSchema(e)))}static nodesFromSchema(e){let t=ii(e.nodes);return t.text||(t.text=n=>n.text),t}static marksFromSchema(e){return ii(e.marks)}}function ii(o){let e={};for(let t in o){let n=o[t].spec.toDOM;n&&(e[t]=n)}return e}function Sr(o){return o.document||window.document}const si=new WeakMap;function Nc(o){let e=si.get(o);return e===void 0&&si.set(o,e=function(t){let n=null;function r(i){if(i&&typeof i=="object")if(Array.isArray(i))if(typeof i[0]=="string")n||(n=[]),n.push(i);else for(let s=0;s<i.length;s++)r(i[s]);else for(let s in i)r(i[s])}return r(t),n}(o)),e}function Un(o,e,t,n){if(typeof e=="string")return{dom:o.createTextNode(e)};if(e.nodeType!=null)return{dom:e};if(e.dom&&e.dom.nodeType!=null)return e;let r,i=e[0];if(typeof i!="string")throw new RangeError("Invalid array passed to renderSpec");if(n&&(r=Nc(n))&&r.indexOf(e)>-1)throw new RangeError("Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.");let s,l=i.indexOf(" ");l>0&&(t=i.slice(0,l),i=i.slice(l+1));let a=t?o.createElementNS(t,i):o.createElement(i),c=e[1],d=1;if(c&&typeof c=="object"&&c.nodeType==null&&!Array.isArray(c)){d=2;for(let h in c)if(c[h]!=null){let u=h.indexOf(" ");u>0?a.setAttributeNS(h.slice(0,u),h.slice(u+1),c[h]):a.setAttribute(h,c[h])}}for(let h=d;h<e.length;h++){let u=e[h];if(u===0){if(h<e.length-1||h>d)throw new RangeError("Content hole must be the only child of its parent node");return{dom:a,contentDOM:a}}{let{dom:f,contentDOM:p}=Un(o,u,t,n);if(a.appendChild(f),p){if(s)throw new RangeError("Multiple content holes");s=p}}}return{dom:a,contentDOM:s}}const li=Math.pow(2,16);function ai(o){return 65535&o}class to{constructor(e,t,n){this.pos=e,this.delInfo=t,this.recover=n}get deleted(){return(8&this.delInfo)>0}get deletedBefore(){return(5&this.delInfo)>0}get deletedAfter(){return(6&this.delInfo)>0}get deletedAcross(){return(4&this.delInfo)>0}}class _e{constructor(e,t=!1){if(this.ranges=e,this.inverted=t,!e.length&&_e.empty)return _e.empty}recover(e){let t=0,n=ai(e);if(!this.inverted)for(let r=0;r<n;r++)t+=this.ranges[3*r+2]-this.ranges[3*r+1];return this.ranges[3*n]+t+function(r){return(r-(65535&r))/li}(e)}mapResult(e,t=1){return this._map(e,t,!1)}map(e,t=1){return this._map(e,t,!0)}_map(e,t,n){let r=0,i=this.inverted?2:1,s=this.inverted?1:2;for(let l=0;l<this.ranges.length;l+=3){let a=this.ranges[l]-(this.inverted?r:0);if(a>e)break;let c=this.ranges[l+i],d=this.ranges[l+s],h=a+c;if(e<=h){let u=a+r+((c?e==a?-1:e==h?1:t:t)<0?0:d);if(n)return u;let f=e==(t<0?a:h)?null:l/3+(e-a)*li,p=e==a?2:e==h?1:4;return(t<0?e!=a:e!=h)&&(p|=8),new to(u,p,f)}r+=d-c}return n?e+r:new to(e+r,0,null)}touches(e,t){let n=0,r=ai(t),i=this.inverted?2:1,s=this.inverted?1:2;for(let l=0;l<this.ranges.length;l+=3){let a=this.ranges[l]-(this.inverted?n:0);if(a>e)break;let c=this.ranges[l+i];if(e<=a+c&&l==3*r)return!0;n+=this.ranges[l+s]-c}return!1}forEach(e){let t=this.inverted?2:1,n=this.inverted?1:2;for(let r=0,i=0;r<this.ranges.length;r+=3){let s=this.ranges[r],l=s-(this.inverted?i:0),a=s+(this.inverted?0:i),c=this.ranges[r+t],d=this.ranges[r+n];e(l,l+c,a,a+d),i+=d-c}}invert(){return new _e(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(e){return e==0?_e.empty:new _e(e<0?[0,-e,0]:[0,0,e])}}_e.empty=new _e([]);class an{constructor(e=[],t,n=0,r=e.length){this.maps=e,this.mirror=t,this.from=n,this.to=r}slice(e=0,t=this.maps.length){return new an(this.maps,this.mirror,e,t)}copy(){return new an(this.maps.slice(),this.mirror&&this.mirror.slice(),this.from,this.to)}appendMap(e,t){this.to=this.maps.push(e),t!=null&&this.setMirror(this.maps.length-1,t)}appendMapping(e){for(let t=0,n=this.maps.length;t<e.maps.length;t++){let r=e.getMirror(t);this.appendMap(e.maps[t],r!=null&&r<t?n+r:void 0)}}getMirror(e){if(this.mirror){for(let t=0;t<this.mirror.length;t++)if(this.mirror[t]==e)return this.mirror[t+(t%2?-1:1)]}}setMirror(e,t){this.mirror||(this.mirror=[]),this.mirror.push(e,t)}appendMappingInverted(e){for(let t=e.maps.length-1,n=this.maps.length+e.maps.length;t>=0;t--){let r=e.getMirror(t);this.appendMap(e.maps[t].invert(),r!=null&&r>t?n-r-1:void 0)}}invert(){let e=new an;return e.appendMappingInverted(this),e}map(e,t=1){if(this.mirror)return this._map(e,t,!0);for(let n=this.from;n<this.to;n++)e=this.maps[n].map(e,t);return e}mapResult(e,t=1){return this._map(e,t,!1)}_map(e,t,n){let r=0;for(let i=this.from;i<this.to;i++){let s=this.maps[i].mapResult(e,t);if(s.recover!=null){let l=this.getMirror(i);if(l!=null&&l>i&&l<this.to){i=l,e=this.maps[l].recover(s.recover);continue}}r|=s.delInfo,e=s.pos}return n?e:new to(e,r,null)}}const Or=Object.create(null);class xe{getMap(){return _e.empty}merge(e){return null}static fromJSON(e,t){if(!t||!t.stepType)throw new RangeError("Invalid input for Step.fromJSON");let n=Or[t.stepType];if(!n)throw new RangeError(`No step type ${t.stepType} defined`);return n.fromJSON(e,t)}static jsonID(e,t){if(e in Or)throw new RangeError("Duplicate use of step JSON ID "+e);return Or[e]=t,t.prototype.jsonID=e,t}}class he{constructor(e,t){this.doc=e,this.failed=t}static ok(e){return new he(e,null)}static fail(e){return new he(null,e)}static fromReplace(e,t,n,r){try{return he.ok(e.replace(t,n,r))}catch(i){if(i instanceof Xn)return he.fail(i.message);throw i}}}function Eo(o,e,t){let n=[];for(let r=0;r<o.childCount;r++){let i=o.child(r);i.content.size&&(i=i.copy(Eo(i.content,e,i))),i.isInline&&(i=e(i,t,r)),n.push(i)}return O.fromArray(n)}class wt extends xe{constructor(e,t,n){super(),this.from=e,this.to=t,this.mark=n}apply(e){let t=e.slice(this.from,this.to),n=e.resolve(this.from),r=n.node(n.sharedDepth(this.to)),i=new L(Eo(t.content,(s,l)=>s.isAtom&&l.type.allowsMarkType(this.mark.type)?s.mark(this.mark.addToSet(s.marks)):s,r),t.openStart,t.openEnd);return he.fromReplace(e,this.from,this.to,i)}invert(){return new et(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deleted&&n.deleted||t.pos>=n.pos?null:new wt(t.pos,n.pos,this.mark)}merge(e){return e instanceof wt&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new wt(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for AddMarkStep.fromJSON");return new wt(t.from,t.to,e.markFromJSON(t.mark))}}xe.jsonID("addMark",wt);class et extends xe{constructor(e,t,n){super(),this.from=e,this.to=t,this.mark=n}apply(e){let t=e.slice(this.from,this.to),n=new L(Eo(t.content,r=>r.mark(this.mark.removeFromSet(r.marks)),e),t.openStart,t.openEnd);return he.fromReplace(e,this.from,this.to,n)}invert(){return new wt(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deleted&&n.deleted||t.pos>=n.pos?null:new et(t.pos,n.pos,this.mark)}merge(e){return e instanceof et&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new et(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for RemoveMarkStep.fromJSON");return new et(t.from,t.to,e.markFromJSON(t.mark))}}xe.jsonID("removeMark",et);class vt extends xe{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return he.fail("No node at mark step's position");let n=t.type.create(t.attrs,null,this.mark.addToSet(t.marks));return he.fromReplace(e,this.pos,this.pos+1,new L(O.from(n),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);if(t){let n=this.mark.addToSet(t.marks);if(n.length==t.marks.length){for(let r=0;r<t.marks.length;r++)if(!t.marks[r].isInSet(n))return new vt(this.pos,t.marks[r]);return new vt(this.pos,this.mark)}}return new fn(this.pos,this.mark)}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new vt(t.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if(typeof t.pos!="number")throw new RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new vt(t.pos,e.markFromJSON(t.mark))}}xe.jsonID("addNodeMark",vt);class fn extends xe{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return he.fail("No node at mark step's position");let n=t.type.create(t.attrs,null,this.mark.removeFromSet(t.marks));return he.fromReplace(e,this.pos,this.pos+1,new L(O.from(n),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);return t&&this.mark.isInSet(t.marks)?new vt(this.pos,this.mark):this}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new fn(t.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if(typeof t.pos!="number")throw new RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new fn(t.pos,e.markFromJSON(t.mark))}}xe.jsonID("removeNodeMark",fn);class fe extends xe{constructor(e,t,n,r=!1){super(),this.from=e,this.to=t,this.slice=n,this.structure=r}apply(e){return this.structure&&no(e,this.from,this.to)?he.fail("Structure replace would overwrite content"):he.fromReplace(e,this.from,this.to,this.slice)}getMap(){return new _e([this.from,this.to-this.from,this.slice.size])}invert(e){return new fe(this.from,this.from+this.slice.size,e.slice(this.from,this.to))}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deletedAcross&&n.deletedAcross?null:new fe(t.pos,Math.max(t.pos,n.pos),this.slice)}merge(e){if(!(e instanceof fe)||e.structure||this.structure)return null;if(this.from+this.slice.size!=e.from||this.slice.openEnd||e.slice.openStart){if(e.to!=this.from||this.slice.openStart||e.slice.openEnd)return null;{let t=this.slice.size+e.slice.size==0?L.empty:new L(e.slice.content.append(this.slice.content),e.slice.openStart,this.slice.openEnd);return new fe(e.from,this.to,t,this.structure)}}{let t=this.slice.size+e.slice.size==0?L.empty:new L(this.slice.content.append(e.slice.content),this.slice.openStart,e.slice.openEnd);return new fe(this.from,this.to+(e.to-e.from),t,this.structure)}}toJSON(){let e={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for ReplaceStep.fromJSON");return new fe(t.from,t.to,L.fromJSON(e,t.slice),!!t.structure)}}xe.jsonID("replace",fe);class me extends xe{constructor(e,t,n,r,i,s,l=!1){super(),this.from=e,this.to=t,this.gapFrom=n,this.gapTo=r,this.slice=i,this.insert=s,this.structure=l}apply(e){if(this.structure&&(no(e,this.from,this.gapFrom)||no(e,this.gapTo,this.to)))return he.fail("Structure gap-replace would overwrite content");let t=e.slice(this.gapFrom,this.gapTo);if(t.openStart||t.openEnd)return he.fail("Gap is not a flat range");let n=this.slice.insertAt(this.insert,t.content);return n?he.fromReplace(e,this.from,this.to,n):he.fail("Content does not fit in gap")}getMap(){return new _e([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(e){let t=this.gapTo-this.gapFrom;return new me(this.from,this.from+this.slice.size+t,this.from+this.insert,this.from+this.insert+t,e.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1),r=this.from==this.gapFrom?t.pos:e.map(this.gapFrom,-1),i=this.to==this.gapTo?n.pos:e.map(this.gapTo,1);return t.deletedAcross&&n.deletedAcross||r<t.pos||i>n.pos?null:new me(t.pos,n.pos,r,i,this.slice,this.insert,this.structure)}toJSON(){let e={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number"||typeof t.gapFrom!="number"||typeof t.gapTo!="number"||typeof t.insert!="number")throw new RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new me(t.from,t.to,t.gapFrom,t.gapTo,L.fromJSON(e,t.slice),t.insert,!!t.structure)}}function no(o,e,t){let n=o.resolve(e),r=t-e,i=n.depth;for(;r>0&&i>0&&n.indexAfter(i)==n.node(i).childCount;)i--,r--;if(r>0){let s=n.node(i).maybeChild(n.indexAfter(i));for(;r>0;){if(!s||s.isLeaf)return!0;s=s.firstChild,r--}}return!1}function ci(o,e,t,n=t.contentMatch,r=!0){let i=o.doc.nodeAt(e),s=[],l=e+1;for(let a=0;a<i.childCount;a++){let c=i.child(a),d=l+c.nodeSize,h=n.matchType(c.type);if(h){n=h;for(let u=0;u<c.marks.length;u++)t.allowsMarkType(c.marks[u].type)||o.step(new et(l,d,c.marks[u]));if(r&&c.isText&&t.whitespace!="pre"){let u,f,p=/\r?\n|\r/g;for(;u=p.exec(c.text);)f||(f=new L(O.from(t.schema.text(" ",t.allowedMarks(c.marks))),0,0)),s.push(new fe(l+u.index,l+u.index+u[0].length,f))}}else s.push(new fe(l,d,L.empty));l=d}if(!n.validEnd){let a=n.fillBefore(O.empty,!0);o.replace(l,l,new L(a,0,0))}for(let a=s.length-1;a>=0;a--)o.step(s[a])}function Lc(o,e,t){return(e==0||o.canReplace(e,o.childCount))&&(t==o.childCount||o.canReplace(0,t))}function mn(o){let e=o.parent.content.cutByIndex(o.startIndex,o.endIndex);for(let t=o.depth;;--t){let n=o.$from.node(t),r=o.$from.index(t),i=o.$to.indexAfter(t);if(t<o.depth&&n.canReplace(r,i,e))return t;if(t==0||n.type.spec.isolating||!Lc(n,r,i))break}return null}function Al(o,e,t=null,n=o){let r=function(s,l){let{parent:a,startIndex:c,endIndex:d}=s,h=a.contentMatchAt(c).findWrapping(l);if(!h)return null;let u=h.length?h[0]:l;return a.canReplaceWith(c,d,u)?h:null}(o,e),i=r&&function(s,l){let{parent:a,startIndex:c,endIndex:d}=s,h=a.child(c),u=l.contentMatch.findWrapping(h.type);if(!u)return null;let f=(u.length?u[u.length-1]:l).contentMatch;for(let p=c;f&&p<d;p++)f=f.matchType(a.child(p).type);return f&&f.validEnd?u:null}(n,e);return i?r.map(di).concat({type:e,attrs:t}).concat(i.map(di)):null}function di(o){return{type:o,attrs:null}}function lt(o,e,t=1,n){let r=o.resolve(e),i=r.depth-t,s=n&&n[n.length-1]||r.parent;if(i<0||r.parent.type.spec.isolating||!r.parent.canReplace(r.index(),r.parent.childCount)||!s.type.validContent(r.parent.content.cutByIndex(r.index(),r.parent.childCount)))return!1;for(let c=r.depth-1,d=t-2;c>i;c--,d--){let h=r.node(c),u=r.index(c);if(h.type.spec.isolating)return!1;let f=h.content.cutByIndex(u,h.childCount),p=n&&n[d+1];p&&(f=f.replaceChild(0,p.type.create(p.attrs)));let m=n&&n[d]||h;if(!h.canReplace(u+1,h.childCount)||!m.type.validContent(f))return!1}let l=r.indexAfter(i),a=n&&n[0];return r.node(i).canReplaceWith(l,l,a?a.type:r.node(i+1).type)}function Wt(o,e){let t=o.resolve(e),n=t.index();return Il(t.nodeBefore,t.nodeAfter)&&t.parent.canReplace(n,n+1)}function Il(o,e){return!(!o||!e||o.isLeaf||!o.canAppend(e))}function jn(o,e,t=-1){let n=o.resolve(e);for(let r=n.depth;;r--){let i,s,l=n.index(r);if(r==n.depth?(i=n.nodeBefore,s=n.nodeAfter):t>0?(i=n.node(r+1),l++,s=n.node(r).maybeChild(l)):(i=n.node(r).maybeChild(l-1),s=n.node(r+1)),i&&!i.isTextblock&&Il(i,s)&&n.node(r).canReplace(l,l+1))return e;if(r==0)break;e=t<0?n.before(r):n.after(r)}}function mr(o,e,t=e,n=L.empty){if(e==t&&!n.size)return null;let r=o.resolve(e),i=o.resolve(t);return Dl(r,i,n)?new fe(e,t,n):new Ac(r,i,n).fit()}function Dl(o,e,t){return!t.openStart&&!t.openEnd&&o.start()==e.start()&&o.parent.canReplace(o.index(),e.index(),t.content)}xe.jsonID("replaceAround",me);class Ac{constructor(e,t,n){this.$from=e,this.$to=t,this.unplaced=n,this.frontier=[],this.placed=O.empty;for(let r=0;r<=e.depth;r++){let i=e.node(r);this.frontier.push({type:i.type,match:i.contentMatchAt(e.indexAfter(r))})}for(let r=e.depth;r>0;r--)this.placed=O.from(e.node(r).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let c=this.findFittable();c?this.placeNodes(c):this.openMore()||this.dropNode()}let e=this.mustMoveInline(),t=this.placed.size-this.depth-this.$from.depth,n=this.$from,r=this.close(e<0?this.$to:n.doc.resolve(e));if(!r)return null;let i=this.placed,s=n.depth,l=r.depth;for(;s&&l&&i.childCount==1;)i=i.firstChild.content,s--,l--;let a=new L(i,s,l);return e>-1?new me(n.pos,e,this.$to.pos,this.$to.end(),a,t):a.size||n.pos!=this.$to.pos?new fe(n.pos,r.pos,a):null}findFittable(){let e=this.unplaced.openStart;for(let t=this.unplaced.content,n=0,r=this.unplaced.openEnd;n<e;n++){let i=t.firstChild;if(t.childCount>1&&(r=0),i.type.spec.isolating&&r<=n){e=n;break}t=i.content}for(let t=1;t<=2;t++)for(let n=t==1?e:this.unplaced.openStart;n>=0;n--){let r,i=null;n?(i=Er(this.unplaced.content,n-1).firstChild,r=i.content):r=this.unplaced.content;let s=r.firstChild;for(let l=this.depth;l>=0;l--){let a,{type:c,match:d}=this.frontier[l],h=null;if(t==1&&(s?d.matchType(s.type)||(h=d.fillBefore(O.from(s),!1)):i&&c.compatibleContent(i.type)))return{sliceDepth:n,frontierDepth:l,parent:i,inject:h};if(t==2&&s&&(a=d.findWrapping(s.type)))return{sliceDepth:n,frontierDepth:l,parent:i,wrap:a};if(i&&d.matchType(i.type))break}}}openMore(){let{content:e,openStart:t,openEnd:n}=this.unplaced,r=Er(e,t);return!(!r.childCount||r.firstChild.isLeaf)&&(this.unplaced=new L(e,t+1,Math.max(n,r.size+t>=e.size-n?t+1:0)),!0)}dropNode(){let{content:e,openStart:t,openEnd:n}=this.unplaced,r=Er(e,t);if(r.childCount<=1&&t>0){let i=e.size-t<=t+r.size;this.unplaced=new L(bn(e,t-1,1),t-1,i?t-1:n)}else this.unplaced=new L(bn(e,t,1),t,n)}placeNodes({sliceDepth:e,frontierDepth:t,parent:n,inject:r,wrap:i}){for(;this.depth>t;)this.closeFrontierNode();if(i)for(let m=0;m<i.length;m++)this.openFrontierNode(i[m]);let s=this.unplaced,l=n?n.content:s.content,a=s.openStart-e,c=0,d=[],{match:h,type:u}=this.frontier[t];if(r){for(let m=0;m<r.childCount;m++)d.push(r.child(m));h=h.matchFragment(r)}let f=l.size+e-(s.content.size-s.openEnd);for(;c<l.childCount;){let m=l.child(c),g=h.matchType(m.type);if(!g)break;c++,(c>1||a==0||m.content.size)&&(h=g,d.push(Rl(m.mark(u.allowedMarks(m.marks)),c==1?a:0,c==l.childCount?f:-1)))}let p=c==l.childCount;p||(f=-1),this.placed=$n(this.placed,t,O.from(d)),this.frontier[t].match=h,p&&f<0&&n&&n.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let m=0,g=l;m<f;m++){let y=g.lastChild;this.frontier.push({type:y.type,match:y.contentMatchAt(y.childCount)}),g=y.content}this.unplaced=p?e==0?L.empty:new L(bn(s.content,e-1,1),e-1,f<0?s.openEnd:e-1):new L(bn(s.content,e,c),s.openStart,s.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return-1;let e,t=this.frontier[this.depth];if(!t.type.isTextblock||!Tr(this.$to,this.$to.depth,t.type,t.match,!1)||this.$to.depth==this.depth&&(e=this.findCloseLevel(this.$to))&&e.depth==this.depth)return-1;let{depth:n}=this.$to,r=this.$to.after(n);for(;n>1&&r==this.$to.end(--n);)++r;return r}findCloseLevel(e){e:for(let t=Math.min(this.depth,e.depth);t>=0;t--){let{match:n,type:r}=this.frontier[t],i=t<e.depth&&e.end(t+1)==e.pos+(e.depth-(t+1)),s=Tr(e,t,r,n,i);if(s){for(let l=t-1;l>=0;l--){let{match:a,type:c}=this.frontier[l],d=Tr(e,l,c,a,!0);if(!d||d.childCount)continue e}return{depth:t,fit:s,move:i?e.doc.resolve(e.after(t+1)):e}}}}close(e){let t=this.findCloseLevel(e);if(!t)return null;for(;this.depth>t.depth;)this.closeFrontierNode();t.fit.childCount&&(this.placed=$n(this.placed,t.depth,t.fit)),e=t.move;for(let n=t.depth+1;n<=e.depth;n++){let r=e.node(n),i=r.type.contentMatch.fillBefore(r.content,!0,e.index(n));this.openFrontierNode(r.type,r.attrs,i)}return e}openFrontierNode(e,t=null,n){let r=this.frontier[this.depth];r.match=r.match.matchType(e),this.placed=$n(this.placed,this.depth,O.from(e.create(t,n))),this.frontier.push({type:e,match:e.contentMatch})}closeFrontierNode(){let e=this.frontier.pop().match.fillBefore(O.empty,!0);e.childCount&&(this.placed=$n(this.placed,this.frontier.length,e))}}function bn(o,e,t){return e==0?o.cutByIndex(t,o.childCount):o.replaceChild(0,o.firstChild.copy(bn(o.firstChild.content,e-1,t)))}function $n(o,e,t){return e==0?o.append(t):o.replaceChild(o.childCount-1,o.lastChild.copy($n(o.lastChild.content,e-1,t)))}function Er(o,e){for(let t=0;t<e;t++)o=o.firstChild.content;return o}function Rl(o,e,t){if(e<=0)return o;let n=o.content;return e>1&&(n=n.replaceChild(0,Rl(n.firstChild,e-1,n.childCount==1?t-1:0))),e>0&&(n=o.type.contentMatch.fillBefore(n).append(n),t<=0&&(n=n.append(o.type.contentMatch.matchFragment(n).fillBefore(O.empty,!0)))),o.copy(n)}function Tr(o,e,t,n,r){let i=o.node(e),s=r?o.indexAfter(e):o.index(e);if(s==i.childCount&&!t.compatibleContent(i.type))return null;let l=n.fillBefore(i.content,!0,s);return l&&!function(a,c,d){for(let h=d;h<c.childCount;h++)if(!a.allowsMarks(c.child(h).marks))return!0;return!1}(t,i.content,s)?l:null}function _l(o,e,t,n,r){if(e<t){let i=o.firstChild;o=o.replaceChild(0,i.copy(_l(i.content,e+1,t,n,i)))}if(e>n){let i=r.contentMatchAt(0),s=i.fillBefore(o).append(o);o=s.append(i.matchFragment(s).fillBefore(O.empty,!0))}return o}function hi(o,e){let t=[];for(let n=Math.min(o.depth,e.depth);n>=0;n--){let r=o.start(n);if(r<o.pos-(o.depth-n)||e.end(n)>e.pos+(e.depth-n)||o.node(n).type.spec.isolating||e.node(n).type.spec.isolating)break;(r==e.start(n)||n==o.depth&&n==e.depth&&o.parent.inlineContent&&e.parent.inlineContent&&n&&e.start(n-1)==r-1)&&t.push(n)}return t}class cn extends xe{constructor(e,t,n){super(),this.pos=e,this.attr=t,this.value=n}apply(e){let t=e.nodeAt(this.pos);if(!t)return he.fail("No node at attribute step's position");let n=Object.create(null);for(let i in t.attrs)n[i]=t.attrs[i];n[this.attr]=this.value;let r=t.type.create(n,null,t.marks);return he.fromReplace(e,this.pos,this.pos+1,new L(O.from(r),0,t.isLeaf?0:1))}getMap(){return _e.empty}invert(e){return new cn(this.pos,this.attr,e.nodeAt(this.pos).attrs[this.attr])}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new cn(t.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(e,t){if(typeof t.pos!="number"||typeof t.attr!="string")throw new RangeError("Invalid input for AttrStep.fromJSON");return new cn(t.pos,t.attr,t.value)}}xe.jsonID("attr",cn);class In extends xe{constructor(e,t){super(),this.attr=e,this.value=t}apply(e){let t=Object.create(null);for(let r in e.attrs)t[r]=e.attrs[r];t[this.attr]=this.value;let n=e.type.create(t,e.content,e.marks);return he.ok(n)}getMap(){return _e.empty}invert(e){return new In(this.attr,e.attrs[this.attr])}map(e){return this}toJSON(){return{stepType:"docAttr",attr:this.attr,value:this.value}}static fromJSON(e,t){if(typeof t.attr!="string")throw new RangeError("Invalid input for DocAttrStep.fromJSON");return new In(t.attr,t.value)}}xe.jsonID("docAttr",In);let kn=class extends Error{};kn=function o(e){let t=Error.call(this,e);return t.__proto__=o.prototype,t},(kn.prototype=Object.create(Error.prototype)).constructor=kn,kn.prototype.name="TransformError";class Ic{constructor(e){this.doc=e,this.steps=[],this.docs=[],this.mapping=new an}get before(){return this.docs.length?this.docs[0]:this.doc}step(e){let t=this.maybeStep(e);if(t.failed)throw new kn(t.failed);return this}maybeStep(e){let t=e.apply(this.doc);return t.failed||this.addStep(e,t.doc),t}get docChanged(){return this.steps.length>0}addStep(e,t){this.docs.push(this.doc),this.steps.push(e),this.mapping.appendMap(e.getMap()),this.doc=t}replace(e,t=e,n=L.empty){let r=mr(this.doc,e,t,n);return r&&this.step(r),this}replaceWith(e,t,n){return this.replace(e,t,new L(O.from(n),0,0))}delete(e,t){return this.replace(e,t,L.empty)}insert(e,t){return this.replaceWith(e,e,t)}replaceRange(e,t,n){return function(r,i,s,l){if(!l.size)return r.deleteRange(i,s);let a=r.doc.resolve(i),c=r.doc.resolve(s);if(Dl(a,c,l))return r.step(new fe(i,s,l));let d=hi(a,r.doc.resolve(s));d[d.length-1]==0&&d.pop();let h=-(a.depth+1);d.unshift(h);for(let y=a.depth,C=a.pos-1;y>0;y--,C--){let v=a.node(y).type.spec;if(v.defining||v.definingAsContext||v.isolating)break;d.indexOf(y)>-1?h=y:a.before(y)==C&&d.splice(1,0,-y)}let u=d.indexOf(h),f=[],p=l.openStart;for(let y=l.content,C=0;;C++){let v=y.firstChild;if(f.push(v),C==l.openStart)break;y=v.content}for(let y=p-1;y>=0;y--){let C=f[y],v=(m=C.type).spec.defining||m.spec.definingForContent;if(v&&!C.sameMarkup(a.node(Math.abs(h)-1)))p=y;else if(v||!C.type.isTextblock)break}var m;for(let y=l.openStart;y>=0;y--){let C=(y+p+1)%(l.openStart+1),v=f[C];if(v)for(let b=0;b<d.length;b++){let k=d[(b+u)%d.length],I=!0;k<0&&(I=!1,k=-k);let T=a.node(k-1),E=a.index(k-1);if(T.canReplaceWith(E,E,v.type,v.marks))return r.replace(a.before(k),I?c.after(k):s,new L(_l(l.content,0,l.openStart,C),C,l.openEnd))}}let g=r.steps.length;for(let y=d.length-1;y>=0&&(r.replace(i,s,l),!(r.steps.length>g));y--){let C=d[y];C<0||(i=a.before(C),s=c.after(C))}}(this,e,t,n),this}replaceRangeWith(e,t,n){return function(r,i,s,l){if(!l.isInline&&i==s&&r.doc.resolve(i).parent.content.size){let a=function(c,d,h){let u=c.resolve(d);if(u.parent.canReplaceWith(u.index(),u.index(),h))return d;if(u.parentOffset==0)for(let f=u.depth-1;f>=0;f--){let p=u.index(f);if(u.node(f).canReplaceWith(p,p,h))return u.before(f+1);if(p>0)return null}if(u.parentOffset==u.parent.content.size)for(let f=u.depth-1;f>=0;f--){let p=u.indexAfter(f);if(u.node(f).canReplaceWith(p,p,h))return u.after(f+1);if(p<u.node(f).childCount)return null}return null}(r.doc,i,l.type);a!=null&&(i=s=a)}r.replaceRange(i,s,new L(O.from(l),0,0))}(this,e,t,n),this}deleteRange(e,t){return function(n,r,i){let s=n.doc.resolve(r),l=n.doc.resolve(i),a=hi(s,l);for(let c=0;c<a.length;c++){let d=a[c],h=c==a.length-1;if(h&&d==0||s.node(d).type.contentMatch.validEnd)return n.delete(s.start(d),l.end(d));if(d>0&&(h||s.node(d-1).canReplace(s.index(d-1),l.indexAfter(d-1))))return n.delete(s.before(d),l.after(d))}for(let c=1;c<=s.depth&&c<=l.depth;c++)if(r-s.start(c)==s.depth-c&&i>s.end(c)&&l.end(c)-i!=l.depth-c)return n.delete(s.before(c),i);n.delete(r,i)}(this,e,t),this}lift(e,t){return function(n,r,i){let{$from:s,$to:l,depth:a}=r,c=s.before(a+1),d=l.after(a+1),h=c,u=d,f=O.empty,p=0;for(let y=a,C=!1;y>i;y--)C||s.index(y)>0?(C=!0,f=O.from(s.node(y).copy(f)),p++):h--;let m=O.empty,g=0;for(let y=a,C=!1;y>i;y--)C||l.after(y+1)<l.end(y)?(C=!0,m=O.from(l.node(y).copy(m)),g++):u++;n.step(new me(h,u,c,d,new L(f.append(m),p,g),f.size-p,!0))}(this,e,t),this}join(e,t=1){return function(n,r,i){let s=new fe(r-i,r+i,L.empty,!0);n.step(s)}(this,e,t),this}wrap(e,t){return function(n,r,i){let s=O.empty;for(let c=i.length-1;c>=0;c--){if(s.size){let d=i[c].type.contentMatch.matchFragment(s);if(!d||!d.validEnd)throw new RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}s=O.from(i[c].type.create(i[c].attrs,s))}let l=r.start,a=r.end;n.step(new me(l,a,l,a,new L(s,0,0),i.length,!0))}(this,e,t),this}setBlockType(e,t=e,n,r=null){return function(i,s,l,a,c){if(!a.isTextblock)throw new RangeError("Type given to setBlockType should be a textblock");let d=i.steps.length;i.doc.nodesBetween(s,l,(h,u)=>{if(h.isTextblock&&!h.hasMarkup(a,c)&&function(f,p,m){let g=f.resolve(p),y=g.index();return g.parent.canReplaceWith(y,y+1,m)}(i.doc,i.mapping.slice(d).map(u),a)){let f=null;if(a.schema.linebreakReplacement){let y=a.whitespace=="pre",C=!!a.contentMatch.matchType(a.schema.linebreakReplacement);y&&!C?f=!1:!y&&C&&(f=!0)}f===!1&&function(y,C,v,b){C.forEach((k,I)=>{if(k.type==k.type.schema.linebreakReplacement){let T=y.mapping.slice(b).map(v+1+I);y.replaceWith(T,T+1,C.type.schema.text(`
`))}})}(i,h,u,d),ci(i,i.mapping.slice(d).map(u,1),a,void 0,f===null);let p=i.mapping.slice(d),m=p.map(u,1),g=p.map(u+h.nodeSize,1);return i.step(new me(m,g,m+1,g-1,new L(O.from(a.create(c,null,h.marks)),0,0),1,!0)),f===!0&&function(y,C,v,b){C.forEach((k,I)=>{if(k.isText){let T,E=/\r?\n|\r/g;for(;T=E.exec(k.text);){let S=y.mapping.slice(b).map(v+1+I+T.index);y.replaceWith(S,S+1,C.type.schema.linebreakReplacement.create())}}})}(i,h,u,d),!1}})}(this,e,t,n,r),this}setNodeMarkup(e,t,n=null,r){return function(i,s,l,a,c){let d=i.doc.nodeAt(s);if(!d)throw new RangeError("No node at given position");l||(l=d.type);let h=l.create(a,null,c||d.marks);if(d.isLeaf)return i.replaceWith(s,s+d.nodeSize,h);if(!l.validContent(d.content))throw new RangeError("Invalid content for node type "+l.name);i.step(new me(s,s+d.nodeSize,s+1,s+d.nodeSize-1,new L(O.from(h),0,0),1,!0))}(this,e,t,n,r),this}setNodeAttribute(e,t,n){return this.step(new cn(e,t,n)),this}setDocAttribute(e,t){return this.step(new In(e,t)),this}addNodeMark(e,t){return this.step(new vt(e,t)),this}removeNodeMark(e,t){if(!(t instanceof ee)){let n=this.doc.nodeAt(e);if(!n)throw new RangeError("No node at position "+e);if(!(t=t.isInSet(n.marks)))return this}return this.step(new fn(e,t)),this}split(e,t=1,n){return function(r,i,s=1,l){let a=r.doc.resolve(i),c=O.empty,d=O.empty;for(let h=a.depth,u=a.depth-s,f=s-1;h>u;h--,f--){c=O.from(a.node(h).copy(c));let p=l&&l[f];d=O.from(p?p.type.create(p.attrs,d):a.node(h).copy(d))}r.step(new fe(i,i,new L(c.append(d),s,s),!0))}(this,e,t,n),this}addMark(e,t,n){return function(r,i,s,l){let a,c,d=[],h=[];r.doc.nodesBetween(i,s,(u,f,p)=>{if(!u.isInline)return;let m=u.marks;if(!l.isInSet(m)&&p.type.allowsMarkType(l.type)){let g=Math.max(f,i),y=Math.min(f+u.nodeSize,s),C=l.addToSet(m);for(let v=0;v<m.length;v++)m[v].isInSet(C)||(a&&a.to==g&&a.mark.eq(m[v])?a.to=y:d.push(a=new et(g,y,m[v])));c&&c.to==g?c.to=y:h.push(c=new wt(g,y,l))}}),d.forEach(u=>r.step(u)),h.forEach(u=>r.step(u))}(this,e,t,n),this}removeMark(e,t,n){return function(r,i,s,l){let a=[],c=0;r.doc.nodesBetween(i,s,(d,h)=>{if(!d.isInline)return;c++;let u=null;if(l instanceof fr){let f,p=d.marks;for(;f=l.isInSet(p);)(u||(u=[])).push(f),p=f.removeFromSet(p)}else l?l.isInSet(d.marks)&&(u=[l]):u=d.marks;if(u&&u.length){let f=Math.min(h+d.nodeSize,s);for(let p=0;p<u.length;p++){let m,g=u[p];for(let y=0;y<a.length;y++){let C=a[y];C.step==c-1&&g.eq(a[y].style)&&(m=C)}m?(m.to=f,m.step=c):a.push({style:g,from:Math.max(h,i),to:f,step:c})}}}),a.forEach(d=>r.step(new et(d.from,d.to,d.style)))}(this,e,t,n),this}clearIncompatible(e,t,n){return ci(this,e,t,n),this}}const Nr=Object.create(null);class Z{constructor(e,t,n){this.$anchor=e,this.$head=t,this.ranges=n||[new Dc(e.min(t),e.max(t))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let e=this.ranges;for(let t=0;t<e.length;t++)if(e[t].$from.pos!=e[t].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(e,t=L.empty){let n=t.content.lastChild,r=null;for(let l=0;l<t.openEnd;l++)r=n,n=n.lastChild;let i=e.steps.length,s=this.ranges;for(let l=0;l<s.length;l++){let{$from:a,$to:c}=s[l],d=e.mapping.slice(i);e.replaceRange(d.map(a.pos),d.map(c.pos),l?L.empty:t),l==0&&fi(e,i,(n?n.isInline:r&&r.isTextblock)?-1:1)}}replaceWith(e,t){let n=e.steps.length,r=this.ranges;for(let i=0;i<r.length;i++){let{$from:s,$to:l}=r[i],a=e.mapping.slice(n),c=a.map(s.pos),d=a.map(l.pos);i?e.deleteRange(c,d):(e.replaceRangeWith(c,d,t),fi(e,n,t.isInline?-1:1))}}static findFrom(e,t,n=!1){let r=e.parent.inlineContent?new G(e):en(e.node(0),e.parent,e.pos,e.index(),t,n);if(r)return r;for(let i=e.depth-1;i>=0;i--){let s=t<0?en(e.node(0),e.node(i),e.before(i+1),e.index(i),t,n):en(e.node(0),e.node(i),e.after(i+1),e.index(i)+1,t,n);if(s)return s}return null}static near(e,t=1){return this.findFrom(e,t)||this.findFrom(e,-t)||new Ve(e.node(0))}static atStart(e){return en(e,e,0,0,1)||new Ve(e)}static atEnd(e){return en(e,e,e.content.size,e.childCount,-1)||new Ve(e)}static fromJSON(e,t){if(!t||!t.type)throw new RangeError("Invalid input for Selection.fromJSON");let n=Nr[t.type];if(!n)throw new RangeError(`No selection type ${t.type} defined`);return n.fromJSON(e,t)}static jsonID(e,t){if(e in Nr)throw new RangeError("Duplicate use of selection JSON ID "+e);return Nr[e]=t,t.prototype.jsonID=e,t}getBookmark(){return G.between(this.$anchor,this.$head).getBookmark()}}Z.prototype.visible=!0;class Dc{constructor(e,t){this.$from=e,this.$to=t}}let ui=!1;function pi(o){ui||o.parent.inlineContent||(ui=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+o.parent.type.name+")"))}class G extends Z{constructor(e,t=e){pi(e),pi(t),super(e,t)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(e,t){let n=e.resolve(t.map(this.head));if(!n.parent.inlineContent)return Z.near(n);let r=e.resolve(t.map(this.anchor));return new G(r.parent.inlineContent?r:n,n)}replace(e,t=L.empty){if(super.replace(e,t),t==L.empty){let n=this.$from.marksAcross(this.$to);n&&e.ensureMarks(n)}}eq(e){return e instanceof G&&e.anchor==this.anchor&&e.head==this.head}getBookmark(){return new gr(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(e,t){if(typeof t.anchor!="number"||typeof t.head!="number")throw new RangeError("Invalid input for TextSelection.fromJSON");return new G(e.resolve(t.anchor),e.resolve(t.head))}static create(e,t,n=t){let r=e.resolve(t);return new this(r,n==t?r:e.resolve(n))}static between(e,t,n){let r=e.pos-t.pos;if(n&&!r||(n=r>=0?1:-1),!t.parent.inlineContent){let i=Z.findFrom(t,n,!0)||Z.findFrom(t,-n,!0);if(!i)return Z.near(t,n);t=i.$head}return e.parent.inlineContent||(r==0||(e=(Z.findFrom(e,-n,!0)||Z.findFrom(e,n,!0)).$anchor).pos<t.pos!=r<0)&&(e=t),new G(e,t)}}Z.jsonID("text",G);class gr{constructor(e,t){this.anchor=e,this.head=t}map(e){return new gr(e.map(this.anchor),e.map(this.head))}resolve(e){return G.between(e.resolve(this.anchor),e.resolve(this.head))}}class j extends Z{constructor(e){let t=e.nodeAfter,n=e.node(0).resolve(e.pos+t.nodeSize);super(e,n),this.node=t}map(e,t){let{deleted:n,pos:r}=t.mapResult(this.anchor),i=e.resolve(r);return n?Z.near(i):new j(i)}content(){return new L(O.from(this.node),0,0)}eq(e){return e instanceof j&&e.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new To(this.anchor)}static fromJSON(e,t){if(typeof t.anchor!="number")throw new RangeError("Invalid input for NodeSelection.fromJSON");return new j(e.resolve(t.anchor))}static create(e,t){return new j(e.resolve(t))}static isSelectable(e){return!e.isText&&e.type.spec.selectable!==!1}}j.prototype.visible=!1,Z.jsonID("node",j);class To{constructor(e){this.anchor=e}map(e){let{deleted:t,pos:n}=e.mapResult(this.anchor);return t?new gr(n,n):new To(n)}resolve(e){let t=e.resolve(this.anchor),n=t.nodeAfter;return n&&j.isSelectable(n)?new j(t):Z.near(t)}}class Ve extends Z{constructor(e){super(e.resolve(0),e.resolve(e.content.size))}replace(e,t=L.empty){if(t==L.empty){e.delete(0,e.doc.content.size);let n=Z.atStart(e.doc);n.eq(e.selection)||e.setSelection(n)}else super.replace(e,t)}toJSON(){return{type:"all"}}static fromJSON(e){return new Ve(e)}map(e){return new Ve(e)}eq(e){return e instanceof Ve}getBookmark(){return Rc}}Z.jsonID("all",Ve);const Rc={map(){return this},resolve:o=>new Ve(o)};function en(o,e,t,n,r,i=!1){if(e.inlineContent)return G.create(o,t);for(let s=n-(r>0?0:1);r>0?s<e.childCount:s>=0;s+=r){let l=e.child(s);if(l.isAtom){if(!i&&j.isSelectable(l))return j.create(o,t-(r<0?l.nodeSize:0))}else{let a=en(o,l,t+r,r<0?l.childCount:0,r,i);if(a)return a}t+=l.nodeSize*r}return null}function fi(o,e,t){let n=o.steps.length-1;if(n<e)return;let r,i=o.steps[n];(i instanceof fe||i instanceof me)&&(o.mapping.maps[n].forEach((s,l,a,c)=>{r==null&&(r=c)}),o.setSelection(Z.near(o.doc.resolve(r),t)))}class _c extends Ic{constructor(e){super(e.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=e.selection,this.storedMarks=e.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(e){if(e.$from.doc!=this.doc)throw new RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=e,this.curSelectionFor=this.steps.length,this.updated=-3&this.updated|1,this.storedMarks=null,this}get selectionSet(){return(1&this.updated)>0}setStoredMarks(e){return this.storedMarks=e,this.updated|=2,this}ensureMarks(e){return ee.sameSet(this.storedMarks||this.selection.$from.marks(),e)||this.setStoredMarks(e),this}addStoredMark(e){return this.ensureMarks(e.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(e){return this.ensureMarks(e.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(2&this.updated)>0}addStep(e,t){super.addStep(e,t),this.updated=-3&this.updated,this.storedMarks=null}setTime(e){return this.time=e,this}replaceSelection(e){return this.selection.replace(this,e),this}replaceSelectionWith(e,t=!0){let n=this.selection;return t&&(e=e.mark(this.storedMarks||(n.empty?n.$from.marks():n.$from.marksAcross(n.$to)||ee.none))),n.replaceWith(this,e),this}deleteSelection(){return this.selection.replace(this),this}insertText(e,t,n){let r=this.doc.type.schema;if(t==null)return e?this.replaceSelectionWith(r.text(e),!0):this.deleteSelection();{if(n==null&&(n=t),n=n??t,!e)return this.deleteRange(t,n);let i=this.storedMarks;if(!i){let s=this.doc.resolve(t);i=n==t?s.marks():s.marksAcross(this.doc.resolve(n))}return this.replaceRangeWith(t,n,r.text(e,i)),this.selection.empty||this.setSelection(Z.near(this.selection.$to)),this}}setMeta(e,t){return this.meta[typeof e=="string"?e:e.key]=t,this}getMeta(e){return this.meta[typeof e=="string"?e:e.key]}get isGeneric(){for(let e in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=4,this}get scrolledIntoView(){return(4&this.updated)>0}}function mi(o,e){return e&&o?o.bind(e):o}class Mn{constructor(e,t,n){this.name=e,this.init=mi(t.init,n),this.apply=mi(t.apply,n)}}const Pc=[new Mn("doc",{init:o=>o.doc||o.schema.topNodeType.createAndFill(),apply:o=>o.doc}),new Mn("selection",{init:(o,e)=>o.selection||Z.atStart(e.doc),apply:o=>o.selection}),new Mn("storedMarks",{init:o=>o.storedMarks||null,apply:(o,e,t,n)=>n.selection.$cursor?o.storedMarks:null}),new Mn("scrollToSelection",{init:()=>0,apply:(o,e)=>o.scrolledIntoView?e+1:e})];class Lr{constructor(e,t){this.schema=e,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=Pc.slice(),t&&t.forEach(n=>{if(this.pluginsByKey[n.key])throw new RangeError("Adding different instances of a keyed plugin ("+n.key+")");this.plugins.push(n),this.pluginsByKey[n.key]=n,n.spec.state&&this.fields.push(new Mn(n.key,n.spec.state,n))})}}class rn{constructor(e){this.config=e}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(e){return this.applyTransaction(e).state}filterTransaction(e,t=-1){for(let n=0;n<this.config.plugins.length;n++)if(n!=t){let r=this.config.plugins[n];if(r.spec.filterTransaction&&!r.spec.filterTransaction.call(r,e,this))return!1}return!0}applyTransaction(e){if(!this.filterTransaction(e))return{state:this,transactions:[]};let t=[e],n=this.applyInner(e),r=null;for(;;){let i=!1;for(let s=0;s<this.config.plugins.length;s++){let l=this.config.plugins[s];if(l.spec.appendTransaction){let a=r?r[s].n:0,c=r?r[s].state:this,d=a<t.length&&l.spec.appendTransaction.call(l,a?t.slice(a):t,c,n);if(d&&n.filterTransaction(d,s)){if(d.setMeta("appendedTransaction",e),!r){r=[];for(let h=0;h<this.config.plugins.length;h++)r.push(h<s?{state:n,n:t.length}:{state:this,n:0})}t.push(d),n=n.applyInner(d),i=!0}r&&(r[s]={state:n,n:t.length})}}if(!i)return{state:n,transactions:t}}}applyInner(e){if(!e.before.eq(this.doc))throw new RangeError("Applying a mismatched transaction");let t=new rn(this.config),n=this.config.fields;for(let r=0;r<n.length;r++){let i=n[r];t[i.name]=i.apply(e,this[i.name],this,t)}return t}get tr(){return new _c(this)}static create(e){let t=new Lr(e.doc?e.doc.type.schema:e.schema,e.plugins),n=new rn(t);for(let r=0;r<t.fields.length;r++)n[t.fields[r].name]=t.fields[r].init(e,n);return n}reconfigure(e){let t=new Lr(this.schema,e.plugins),n=t.fields,r=new rn(t);for(let i=0;i<n.length;i++){let s=n[i].name;r[s]=this.hasOwnProperty(s)?this[s]:n[i].init(e,r)}return r}toJSON(e){let t={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(t.storedMarks=this.storedMarks.map(n=>n.toJSON())),e&&typeof e=="object")for(let n in e){if(n=="doc"||n=="selection")throw new RangeError("The JSON fields `doc` and `selection` are reserved");let r=e[n],i=r.spec.state;i&&i.toJSON&&(t[n]=i.toJSON.call(r,this[r.key]))}return t}static fromJSON(e,t,n){if(!t)throw new RangeError("Invalid input for EditorState.fromJSON");if(!e.schema)throw new RangeError("Required config field 'schema' missing");let r=new Lr(e.schema,e.plugins),i=new rn(r);return r.fields.forEach(s=>{if(s.name=="doc")i.doc=Ht.fromJSON(e.schema,t.doc);else if(s.name=="selection")i.selection=Z.fromJSON(i.doc,t.selection);else if(s.name=="storedMarks")t.storedMarks&&(i.storedMarks=t.storedMarks.map(e.schema.markFromJSON));else{if(n)for(let l in n){let a=n[l],c=a.spec.state;if(a.key==s.name&&c&&c.fromJSON&&Object.prototype.hasOwnProperty.call(t,l))return void(i[s.name]=c.fromJSON.call(a,e,t[l],i))}i[s.name]=s.init(e,i)}}),i}}function Pl(o,e,t){for(let n in o){let r=o[n];r instanceof Function?r=r.bind(e):n=="handleDOMEvents"&&(r=Pl(r,e,{})),t[n]=r}return t}class Fe{constructor(e){this.spec=e,this.props={},e.props&&Pl(e.props,this,this.props),this.key=e.key?e.key.key:zl("plugin")}getState(e){return e[this.key]}}const Ar=Object.create(null);function zl(o){return o in Ar?o+"$"+ ++Ar[o]:(Ar[o]=0,o+"$")}class ze{constructor(e="key"){this.key=zl(e)}get(e){return e.config.pluginsByKey[this.key]}getState(e){return e[this.key]}}const Ce=function(o){for(var e=0;;e++)if(!(o=o.previousSibling))return e},Dn=function(o){let e=o.assignedSlot||o.parentNode;return e&&e.nodeType==11?e.host:e};let ro=null;const it=function(o,e,t){let n=ro||(ro=document.createRange());return n.setEnd(o,t??o.nodeValue.length),n.setStart(o,e||0),n},Jt=function(o,e,t,n){return t&&(gi(o,e,t,n,-1)||gi(o,e,t,n,1))},zc=/^(img|br|input|textarea|hr)$/i;function gi(o,e,t,n,r){for(;;){if(o==t&&e==n)return!0;if(e==(r<0?0:Qe(o))){let i=o.parentNode;if(!i||i.nodeType!=1||Rn(o)||zc.test(o.nodeName)||o.contentEditable=="false")return!1;e=Ce(o)+(r<0?0:1),o=i}else{if(o.nodeType!=1||(o=o.childNodes[e+(r<0?-1:0)]).contentEditable=="false")return!1;e=r<0?Qe(o):0}}}function Qe(o){return o.nodeType==3?o.nodeValue.length:o.childNodes.length}function Rn(o){let e;for(let t=o;t&&!(e=t.pmViewDesc);t=t.parentNode);return e&&e.node&&e.node.isBlock&&(e.dom==o||e.contentDOM==o)}const yr=function(o){return o.focusNode&&Jt(o.focusNode,o.focusOffset,o.anchorNode,o.anchorOffset)};function Lt(o,e){let t=document.createEvent("Event");return t.initEvent("keydown",!0,!0),t.keyCode=o,t.key=t.code=e,t}const rt=typeof navigator<"u"?navigator:null,yi=typeof document<"u"?document:null,Et=rt&&rt.userAgent||"",oo=/Edge\/(\d+)/.exec(Et),Fl=/MSIE \d/.exec(Et),io=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(Et),Ee=!!(Fl||io||oo),bt=Fl?document.documentMode:io?+io[1]:oo?+oo[1]:0,Ue=!Ee&&/gecko\/(\d+)/i.test(Et);Ue&&(/Firefox\/(\d+)/.exec(Et)||[0,0])[1];const so=!Ee&&/Chrome\/(\d+)/.exec(Et),be=!!so,Fc=so?+so[1]:0,Se=!Ee&&!!rt&&/Apple Computer/.test(rt.vendor),gn=Se&&(/Mobile\/\w+/.test(Et)||!!rt&&rt.maxTouchPoints>2),Be=gn||!!rt&&/Mac/.test(rt.platform),Bc=!!rt&&/Win/.test(rt.platform),We=/Android \d/.test(Et),zn=!!yi&&"webkitFontSmoothing"in yi.documentElement.style,Vc=zn?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0;function Hc(o){let e=o.defaultView&&o.defaultView.visualViewport;return e?{left:0,right:e.width,top:0,bottom:e.height}:{left:0,right:o.documentElement.clientWidth,top:0,bottom:o.documentElement.clientHeight}}function ot(o,e){return typeof o=="number"?o:o[e]}function jc(o){let e=o.getBoundingClientRect(),t=e.width/o.offsetWidth||1,n=e.height/o.offsetHeight||1;return{left:e.left,right:e.left+o.clientWidth*t,top:e.top,bottom:e.top+o.clientHeight*n}}function Ci(o,e,t){let n=o.someProp("scrollThreshold")||0,r=o.someProp("scrollMargin")||5,i=o.dom.ownerDocument;for(let s=t||o.dom;s;s=Dn(s)){if(s.nodeType!=1)continue;let l=s,a=l==i.body,c=a?Hc(i):jc(l),d=0,h=0;if(e.top<c.top+ot(n,"top")?h=-(c.top-e.top+ot(r,"top")):e.bottom>c.bottom-ot(n,"bottom")&&(h=e.bottom-e.top>c.bottom-c.top?e.top+ot(r,"top")-c.top:e.bottom-c.bottom+ot(r,"bottom")),e.left<c.left+ot(n,"left")?d=-(c.left-e.left+ot(r,"left")):e.right>c.right-ot(n,"right")&&(d=e.right-c.right+ot(r,"right")),d||h)if(a)i.defaultView.scrollBy(d,h);else{let u=l.scrollLeft,f=l.scrollTop;h&&(l.scrollTop+=h),d&&(l.scrollLeft+=d);let p=l.scrollLeft-u,m=l.scrollTop-f;e={left:e.left-p,top:e.top-m,right:e.right-p,bottom:e.bottom-m}}if(a||/^(fixed|sticky)$/.test(getComputedStyle(s).position))break}}function wi(o){let e=[],t=o.ownerDocument;for(let n=o;n&&(e.push({dom:n,top:n.scrollTop,left:n.scrollLeft}),o!=t);n=Dn(n));return e}function vi(o,e){for(let t=0;t<o.length;t++){let{dom:n,top:r,left:i}=o[t];n.scrollTop!=r+e&&(n.scrollTop=r+e),n.scrollLeft!=i&&(n.scrollLeft=i)}}let Xt=null;function Bl(o,e){let t,n,r,i,s=2e8,l=0,a=e.top,c=e.top;for(let d=o.firstChild,h=0;d;d=d.nextSibling,h++){let u;if(d.nodeType==1)u=d.getClientRects();else{if(d.nodeType!=3)continue;u=it(d).getClientRects()}for(let f=0;f<u.length;f++){let p=u[f];if(p.top<=a&&p.bottom>=c){a=Math.max(p.bottom,a),c=Math.min(p.top,c);let m=p.left>e.left?p.left-e.left:p.right<e.left?e.left-p.right:0;if(m<s){t=d,s=m,n=m&&t.nodeType==3?{left:p.right<e.left?p.right:p.left,top:e.top}:e,d.nodeType==1&&m&&(l=h+(e.left>=(p.left+p.right)/2?1:0));continue}}else p.top>e.top&&!r&&p.left<=e.left&&p.right>=e.left&&(r=d,i={left:Math.max(p.left,Math.min(p.right,e.left)),top:p.top});!t&&(e.left>=p.right&&e.top>=p.top||e.left>=p.left&&e.top>=p.bottom)&&(l=h+1)}}return!t&&r&&(t=r,n=i,s=0),t&&t.nodeType==3?function(d,h){let u=d.nodeValue.length,f=document.createRange();for(let p=0;p<u;p++){f.setEnd(d,p+1),f.setStart(d,p);let m=ht(f,1);if(m.top!=m.bottom&&No(h,m))return{node:d,offset:p+(h.left>=(m.left+m.right)/2?1:0)}}return{node:d,offset:0}}(t,n):!t||s&&t.nodeType==1?{node:o,offset:l}:Bl(t,n)}function No(o,e){return o.left>=e.left-1&&o.left<=e.right+1&&o.top>=e.top-1&&o.top<=e.bottom+1}function Vl(o,e,t){let n=o.childNodes.length;if(n&&t.top<t.bottom)for(let r=Math.max(0,Math.min(n-1,Math.floor(n*(e.top-t.top)/(t.bottom-t.top))-2)),i=r;;){let s=o.childNodes[i];if(s.nodeType==1){let l=s.getClientRects();for(let a=0;a<l.length;a++){let c=l[a];if(No(e,c))return Vl(s,e,c)}}if((i=(i+1)%n)==r)break}return o}function qc(o,e){let t,n=o.dom.ownerDocument,r=0,i=function(c,d,h){if(c.caretPositionFromPoint)try{let u=c.caretPositionFromPoint(d,h);if(u)return{node:u.offsetNode,offset:u.offset}}catch{}if(c.caretRangeFromPoint){let u=c.caretRangeFromPoint(d,h);if(u)return{node:u.startContainer,offset:u.startOffset}}}(n,e.left,e.top);i&&({node:t,offset:r}=i);let s,l=(o.root.elementFromPoint?o.root:n).elementFromPoint(e.left,e.top);if(!l||!o.dom.contains(l.nodeType!=1?l.parentNode:l)){let c=o.dom.getBoundingClientRect();if(!No(e,c)||(l=Vl(o.dom,e,c),!l))return null}if(Se)for(let c=l;t&&c;c=Dn(c))c.draggable&&(t=void 0);if(l=function(c,d){let h=c.parentNode;return h&&/^li$/i.test(h.nodeName)&&d.left<c.getBoundingClientRect().left?h:c}(l,e),t){if(Ue&&t.nodeType==1&&(r=Math.min(r,t.childNodes.length),r<t.childNodes.length)){let d,h=t.childNodes[r];h.nodeName=="IMG"&&(d=h.getBoundingClientRect()).right<=e.left&&d.bottom>e.top&&r++}let c;zn&&r&&t.nodeType==1&&(c=t.childNodes[r-1]).nodeType==1&&c.contentEditable=="false"&&c.getBoundingClientRect().top>=e.top&&r--,t==o.dom&&r==t.childNodes.length-1&&t.lastChild.nodeType==1&&e.top>t.lastChild.getBoundingClientRect().bottom?s=o.state.doc.content.size:r!=0&&t.nodeType==1&&t.childNodes[r-1].nodeName=="BR"||(s=function(d,h,u,f){let p=-1;for(let m=h,g=!1;m!=d.dom;){let y=d.docView.nearestDesc(m,!0);if(!y)return null;if(y.dom.nodeType==1&&(y.node.isBlock&&y.parent&&!g||!y.contentDOM)){let C=y.dom.getBoundingClientRect();if(y.node.isBlock&&y.parent&&!g&&(g=!0,C.left>f.left||C.top>f.top?p=y.posBefore:(C.right<f.left||C.bottom<f.top)&&(p=y.posAfter)),!y.contentDOM&&p<0&&!y.node.isText)return(y.node.isBlock?f.top<(C.top+C.bottom)/2:f.left<(C.left+C.right)/2)?y.posBefore:y.posAfter}m=y.dom.parentNode}return p>-1?p:d.docView.posFromDOM(h,u,-1)}(o,t,r,e))}s==null&&(s=function(c,d,h){let{node:u,offset:f}=Bl(d,h),p=-1;if(u.nodeType==1&&!u.firstChild){let m=u.getBoundingClientRect();p=m.left!=m.right&&h.left>(m.left+m.right)/2?1:-1}return c.docView.posFromDOM(u,f,p)}(o,l,e));let a=o.docView.nearestDesc(l,!0);return{pos:s,inside:a?a.posAtStart-a.border:-1}}function xi(o){return o.top<o.bottom||o.left<o.right}function ht(o,e){let t=o.getClientRects();if(t.length){let n=t[e<0?0:t.length-1];if(xi(n))return n}return Array.prototype.find.call(t,xi)||o.getBoundingClientRect()}const Kc=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/;function Hl(o,e,t){let{node:n,offset:r,atom:i}=o.docView.domFromPos(e,t<0?-1:1),s=zn||Ue;if(n.nodeType==3){if(!s||!Kc.test(n.nodeValue)&&(t<0?r:r!=n.nodeValue.length)){let l=r,a=r,c=t<0?1:-1;return t<0&&!r?(a++,c=-1):t>=0&&r==n.nodeValue.length?(l--,c=1):t<0?l--:a++,vn(ht(it(n,l,a),c),c<0)}{let l=ht(it(n,r,r),t);if(Ue&&r&&/\s/.test(n.nodeValue[r-1])&&r<n.nodeValue.length){let a=ht(it(n,r-1,r-1),-1);if(a.top==l.top){let c=ht(it(n,r,r+1),-1);if(c.top!=l.top)return vn(c,c.left<a.left)}}return l}}if(!o.state.doc.resolve(e-(i||0)).parent.inlineContent){if(i==null&&r&&(t<0||r==Qe(n))){let l=n.childNodes[r-1];if(l.nodeType==1)return Ir(l.getBoundingClientRect(),!1)}if(i==null&&r<Qe(n)){let l=n.childNodes[r];if(l.nodeType==1)return Ir(l.getBoundingClientRect(),!0)}return Ir(n.getBoundingClientRect(),t>=0)}if(i==null&&r&&(t<0||r==Qe(n))){let l=n.childNodes[r-1],a=l.nodeType==3?it(l,Qe(l)-(s?0:1)):l.nodeType!=1||l.nodeName=="BR"&&l.nextSibling?null:l;if(a)return vn(ht(a,1),!1)}if(i==null&&r<Qe(n)){let l=n.childNodes[r];for(;l.pmViewDesc&&l.pmViewDesc.ignoreForCoords;)l=l.nextSibling;let a=l?l.nodeType==3?it(l,0,s?0:1):l.nodeType==1?l:null:null;if(a)return vn(ht(a,-1),!0)}return vn(ht(n.nodeType==3?it(n):n,-t),t>=0)}function vn(o,e){if(o.width==0)return o;let t=e?o.left:o.right;return{top:o.top,bottom:o.bottom,left:t,right:t}}function Ir(o,e){if(o.height==0)return o;let t=e?o.top:o.bottom;return{top:t,bottom:t,left:o.left,right:o.right}}function bi(o,e,t){let n=o.state,r=o.root.activeElement;n!=e&&o.updateState(e),r!=o.dom&&o.focus();try{return t()}finally{n!=e&&o.updateState(n),r!=o.dom&&r&&r.focus()}}const Wc=/[\u0590-\u08ac]/;let $i=null,ki=null,Mi=!1;function Jc(o,e,t){return $i==e&&ki==t?Mi:($i=e,ki=t,Mi=t=="up"||t=="down"?function(n,r,i){let s=r.selection,l=i=="up"?s.$from:s.$to;return bi(n,r,()=>{let{node:a}=n.docView.domFromPos(l.pos,i=="up"?-1:1);for(;;){let d=n.docView.nearestDesc(a,!0);if(!d)break;if(d.node.isBlock){a=d.contentDOM||d.dom;break}a=d.dom.parentNode}let c=Hl(n,l.pos,1);for(let d=a.firstChild;d;d=d.nextSibling){let h;if(d.nodeType==1)h=d.getClientRects();else{if(d.nodeType!=3)continue;h=it(d,0,d.nodeValue.length).getClientRects()}for(let u=0;u<h.length;u++){let f=h[u];if(f.bottom>f.top+1&&(i=="up"?c.top-f.top>2*(f.bottom-c.top):f.bottom-c.bottom>2*(c.bottom-f.top)))return!1}}return!0})}(o,e,t):function(n,r,i){let{$head:s}=r.selection;if(!s.parent.isTextblock)return!1;let l=s.parentOffset,a=!l,c=l==s.parent.content.size,d=n.domSelection();return Wc.test(s.parent.textContent)&&d.modify?bi(n,r,()=>{let{focusNode:h,focusOffset:u,anchorNode:f,anchorOffset:p}=n.domSelectionRange(),m=d.caretBidiLevel;d.modify("move",i,"character");let g=s.depth?n.docView.domAfterPos(s.before()):n.dom,{focusNode:y,focusOffset:C}=n.domSelectionRange(),v=y&&!g.contains(y.nodeType==1?y:y.parentNode)||h==y&&u==C;try{d.collapse(f,p),h&&(h!=f||u!=p)&&d.extend&&d.extend(h,u)}catch{}return m!=null&&(d.caretBidiLevel=m),v}):i=="left"||i=="backward"?a:c}(o,e,t))}class Fn{constructor(e,t,n,r){this.parent=e,this.children=t,this.dom=n,this.contentDOM=r,this.dirty=0,n.pmViewDesc=this}matchesWidget(e){return!1}matchesMark(e){return!1}matchesNode(e,t,n){return!1}matchesHack(e){return!1}parseRule(){return null}stopEvent(e){return!1}get size(){let e=0;for(let t=0;t<this.children.length;t++)e+=this.children[t].size;return e}get border(){return 0}destroy(){this.parent=void 0,this.dom.pmViewDesc==this&&(this.dom.pmViewDesc=void 0);for(let e=0;e<this.children.length;e++)this.children[e].destroy()}posBeforeChild(e){for(let t=0,n=this.posAtStart;;t++){let r=this.children[t];if(r==e)return n;n+=r.size}}get posBefore(){return this.parent.posBeforeChild(this)}get posAtStart(){return this.parent?this.parent.posBeforeChild(this)+this.border:0}get posAfter(){return this.posBefore+this.size}get posAtEnd(){return this.posAtStart+this.size-2*this.border}localPosFromDOM(e,t,n){if(this.contentDOM&&this.contentDOM.contains(e.nodeType==1?e:e.parentNode)){if(n<0){let i,s;if(e==this.contentDOM)i=e.childNodes[t-1];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;i=e.previousSibling}for(;i&&(!(s=i.pmViewDesc)||s.parent!=this);)i=i.previousSibling;return i?this.posBeforeChild(s)+s.size:this.posAtStart}{let i,s;if(e==this.contentDOM)i=e.childNodes[t];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;i=e.nextSibling}for(;i&&(!(s=i.pmViewDesc)||s.parent!=this);)i=i.nextSibling;return i?this.posBeforeChild(s):this.posAtEnd}}let r;if(e==this.dom&&this.contentDOM)r=t>Ce(this.contentDOM);else if(this.contentDOM&&this.contentDOM!=this.dom&&this.dom.contains(this.contentDOM))r=2&e.compareDocumentPosition(this.contentDOM);else if(this.dom.firstChild){if(t==0)for(let i=e;;i=i.parentNode){if(i==this.dom){r=!1;break}if(i.previousSibling)break}if(r==null&&t==e.childNodes.length)for(let i=e;;i=i.parentNode){if(i==this.dom){r=!0;break}if(i.nextSibling)break}}return r??n>0?this.posAtEnd:this.posAtStart}nearestDesc(e,t=!1){for(let n=!0,r=e;r;r=r.parentNode){let i,s=this.getDesc(r);if(s&&(!t||s.node)){if(!n||!(i=s.nodeDOM)||(i.nodeType==1?i.contains(e.nodeType==1?e:e.parentNode):i==e))return s;n=!1}}}getDesc(e){let t=e.pmViewDesc;for(let n=t;n;n=n.parent)if(n==this)return t}posFromDOM(e,t,n){for(let r=e;r;r=r.parentNode){let i=this.getDesc(r);if(i)return i.localPosFromDOM(e,t,n)}return-1}descAt(e){for(let t=0,n=0;t<this.children.length;t++){let r=this.children[t],i=n+r.size;if(n==e&&i!=n){for(;!r.border&&r.children.length;)r=r.children[0];return r}if(e<i)return r.descAt(e-n-r.border);n=i}}domFromPos(e,t){if(!this.contentDOM)return{node:this.dom,offset:0,atom:e+1};let n=0,r=0;for(let i=0;n<this.children.length;n++){let s=this.children[n],l=i+s.size;if(l>e||s instanceof ql){r=e-i;break}i=l}if(r)return this.children[n].domFromPos(r-this.children[n].border,t);for(let i;n&&!(i=this.children[n-1]).size&&i instanceof jl&&i.side>=0;n--);if(t<=0){let i,s=!0;for(;i=n?this.children[n-1]:null,i&&i.dom.parentNode!=this.contentDOM;n--,s=!1);return i&&t&&s&&!i.border&&!i.domAtom?i.domFromPos(i.size,t):{node:this.contentDOM,offset:i?Ce(i.dom)+1:0}}{let i,s=!0;for(;i=n<this.children.length?this.children[n]:null,i&&i.dom.parentNode!=this.contentDOM;n++,s=!1);return i&&s&&!i.border&&!i.domAtom?i.domFromPos(0,t):{node:this.contentDOM,offset:i?Ce(i.dom):this.contentDOM.childNodes.length}}}parseRange(e,t,n=0){if(this.children.length==0)return{node:this.contentDOM,from:e,to:t,fromOffset:0,toOffset:this.contentDOM.childNodes.length};let r=-1,i=-1;for(let s=n,l=0;;l++){let a=this.children[l],c=s+a.size;if(r==-1&&e<=c){let d=s+a.border;if(e>=d&&t<=c-a.border&&a.node&&a.contentDOM&&this.contentDOM.contains(a.contentDOM))return a.parseRange(e,t,d);e=s;for(let h=l;h>0;h--){let u=this.children[h-1];if(u.size&&u.dom.parentNode==this.contentDOM&&!u.emptyChildAt(1)){r=Ce(u.dom)+1;break}e-=u.size}r==-1&&(r=0)}if(r>-1&&(c>t||l==this.children.length-1)){t=c;for(let d=l+1;d<this.children.length;d++){let h=this.children[d];if(h.size&&h.dom.parentNode==this.contentDOM&&!h.emptyChildAt(-1)){i=Ce(h.dom);break}t+=h.size}i==-1&&(i=this.contentDOM.childNodes.length);break}s=c}return{node:this.contentDOM,from:e,to:t,fromOffset:r,toOffset:i}}emptyChildAt(e){if(this.border||!this.contentDOM||!this.children.length)return!1;let t=this.children[e<0?0:this.children.length-1];return t.size==0||t.emptyChildAt(e)}domAfterPos(e){let{node:t,offset:n}=this.domFromPos(e,0);if(t.nodeType!=1||n==t.childNodes.length)throw new RangeError("No node after pos "+e);return t.childNodes[n]}setSelection(e,t,n,r=!1){let i=Math.min(e,t),s=Math.max(e,t);for(let u=0,f=0;u<this.children.length;u++){let p=this.children[u],m=f+p.size;if(i>f&&s<m)return p.setSelection(e-f-p.border,t-f-p.border,n,r);f=m}let l=this.domFromPos(e,e?-1:1),a=t==e?l:this.domFromPos(t,t?-1:1),c=n.getSelection(),d=!1;if((Ue||Se)&&e==t){let{node:u,offset:f}=l;if(u.nodeType==3){if(d=!(!f||u.nodeValue[f-1]!=`
`),d&&f==u.nodeValue.length)for(let p,m=u;m;m=m.parentNode){if(p=m.nextSibling){p.nodeName=="BR"&&(l=a={node:p.parentNode,offset:Ce(p)+1});break}let g=m.pmViewDesc;if(g&&g.node&&g.node.isBlock)break}}else{let p=u.childNodes[f-1];d=p&&(p.nodeName=="BR"||p.contentEditable=="false")}}if(Ue&&c.focusNode&&c.focusNode!=a.node&&c.focusNode.nodeType==1){let u=c.focusNode.childNodes[c.focusOffset];u&&u.contentEditable=="false"&&(r=!0)}if(!(r||d&&Se)&&Jt(l.node,l.offset,c.anchorNode,c.anchorOffset)&&Jt(a.node,a.offset,c.focusNode,c.focusOffset))return;let h=!1;if((c.extend||e==t)&&!d){c.collapse(l.node,l.offset);try{e!=t&&c.extend(a.node,a.offset),h=!0}catch{}}if(!h){if(e>t){let f=l;l=a,a=f}let u=document.createRange();u.setEnd(a.node,a.offset),u.setStart(l.node,l.offset),c.removeAllRanges(),c.addRange(u)}}ignoreMutation(e){return!this.contentDOM&&e.type!="selection"}get contentLost(){return this.contentDOM&&this.contentDOM!=this.dom&&!this.dom.contains(this.contentDOM)}markDirty(e,t){for(let n=0,r=0;r<this.children.length;r++){let i=this.children[r],s=n+i.size;if(n==s?e<=s&&t>=n:e<s&&t>n){let l=n+i.border,a=s-i.border;if(e>=l&&t<=a)return this.dirty=e==n||t==s?2:1,void(e!=l||t!=a||!i.contentLost&&i.dom.parentNode==this.contentDOM?i.markDirty(e-l,t-l):i.dirty=3);i.dirty=i.dom!=i.contentDOM||i.dom.parentNode!=this.contentDOM||i.children.length?3:2}n=s}this.dirty=2}markParentsDirty(){let e=1;for(let t=this.parent;t;t=t.parent,e++){let n=e==1?2:1;t.dirty<n&&(t.dirty=n)}}get domAtom(){return!1}get ignoreForCoords(){return!1}isText(e){return!1}}class jl extends Fn{constructor(e,t,n,r){let i,s=t.type.toDOM;if(typeof s=="function"&&(s=s(n,()=>i?i.parent?i.parent.posBeforeChild(i):void 0:r)),!t.type.spec.raw){if(s.nodeType!=1){let l=document.createElement("span");l.appendChild(s),s=l}s.contentEditable="false",s.classList.add("ProseMirror-widget")}super(e,[],s,null),this.widget=t,this.widget=t,i=this}matchesWidget(e){return this.dirty==0&&e.type.eq(this.widget.type)}parseRule(){return{ignore:!0}}stopEvent(e){let t=this.widget.spec.stopEvent;return!!t&&t(e)}ignoreMutation(e){return e.type!="selection"||this.widget.spec.ignoreSelection}destroy(){this.widget.type.destroy(this.dom),super.destroy()}get domAtom(){return!0}get side(){return this.widget.type.side}}class Uc extends Fn{constructor(e,t,n,r){super(e,[],t,null),this.textDOM=n,this.text=r}get size(){return this.text.length}localPosFromDOM(e,t){return e!=this.textDOM?this.posAtStart+(t?this.size:0):this.posAtStart+t}domFromPos(e){return{node:this.textDOM,offset:e}}ignoreMutation(e){return e.type==="characterData"&&e.target.nodeValue==e.oldValue}}class jt extends Fn{constructor(e,t,n,r){super(e,[],n,r),this.mark=t}static create(e,t,n,r){let i=r.nodeViews[t.type.name],s=i&&i(t,r,n);return s&&s.dom||(s=Ut.renderSpec(document,t.type.spec.toDOM(t,n))),new jt(e,t,s.dom,s.contentDOM||s.dom)}parseRule(){return 3&this.dirty||this.mark.type.spec.reparseInView?null:{mark:this.mark.type.name,attrs:this.mark.attrs,contentElement:this.contentDOM}}matchesMark(e){return this.dirty!=3&&this.mark.eq(e)}markDirty(e,t){if(super.markDirty(e,t),this.dirty!=0){let n=this.parent;for(;!n.node;)n=n.parent;n.dirty<this.dirty&&(n.dirty=this.dirty),this.dirty=0}}slice(e,t,n){let r=jt.create(this.parent,this.mark,!0,n),i=this.children,s=this.size;t<s&&(i=co(i,t,s,n)),e>0&&(i=co(i,0,e,n));for(let l=0;l<i.length;l++)i[l].parent=r;return r.children=i,r}}class $t extends Fn{constructor(e,t,n,r,i,s,l,a,c){super(e,[],i,s),this.node=t,this.outerDeco=n,this.innerDeco=r,this.nodeDOM=l}static create(e,t,n,r,i,s){let l,a=i.nodeViews[t.type.name],c=a&&a(t,i,()=>l?l.parent?l.parent.posBeforeChild(l):void 0:s,n,r),d=c&&c.dom,h=c&&c.contentDOM;if(t.isText)if(d){if(d.nodeType!=3)throw new RangeError("Text must be rendered as a DOM text node")}else d=document.createTextNode(t.text);else d||({dom:d,contentDOM:h}=Ut.renderSpec(document,t.type.spec.toDOM(t)));h||t.isText||d.nodeName=="BR"||(d.hasAttribute("contenteditable")||(d.contentEditable="false"),t.type.spec.draggable&&(d.draggable=!0));let u=d;return d=Jl(d,n,t),c?l=new Gc(e,t,n,r,d,h||null,u,c,i,s+1):t.isText?new Cr(e,t,n,r,d,u,i):new $t(e,t,n,r,d,h||null,u,i,s+1)}parseRule(){if(this.node.type.spec.reparseInView)return null;let e={node:this.node.type.name,attrs:this.node.attrs};if(this.node.type.whitespace=="pre"&&(e.preserveWhitespace="full"),this.contentDOM)if(this.contentLost){for(let t=this.children.length-1;t>=0;t--){let n=this.children[t];if(this.dom.contains(n.dom.parentNode)){e.contentElement=n.dom.parentNode;break}}e.contentElement||(e.getContent=()=>O.empty)}else e.contentElement=this.contentDOM;else e.getContent=()=>this.node.content;return e}matchesNode(e,t,n){return this.dirty==0&&e.eq(this.node)&&ao(t,this.outerDeco)&&n.eq(this.innerDeco)}get size(){return this.node.nodeSize}get border(){return this.node.isLeaf?0:1}updateChildren(e,t){let n=this.node.inlineContent,r=t,i=e.composing?this.localCompositionInfo(e,t):null,s=i&&i.pos>-1?i:null,l=i&&i.pos<0,a=new Yc(this,s&&s.node,e);(function(c,d,h,u){let f=d.locals(c),p=0;if(f.length==0){for(let C=0;C<c.childCount;C++){let v=c.child(C);u(v,f,d.forChild(p,v),C),p+=v.nodeSize}return}let m=0,g=[],y=null;for(let C=0;;){let v,b,k,I;for(;m<f.length&&f[m].to==p;){let E=f[m++];E.widget&&(v?(b||(b=[v])).push(E):v=E)}if(v)if(b){b.sort(Xc);for(let E=0;E<b.length;E++)h(b[E],C,!!y)}else h(v,C,!!y);if(y)I=-1,k=y,y=null;else{if(!(C<c.childCount))break;I=C,k=c.child(C++)}for(let E=0;E<g.length;E++)g[E].to<=p&&g.splice(E--,1);for(;m<f.length&&f[m].from<=p&&f[m].to>p;)g.push(f[m++]);let T=p+k.nodeSize;if(k.isText){let E=T;m<f.length&&f[m].from<E&&(E=f[m].from);for(let S=0;S<g.length;S++)g[S].to<E&&(E=g[S].to);E<T&&(y=k.cut(E-p),k=k.cut(0,E-p),T=E,I=-1)}else for(;m<f.length&&f[m].to<T;)m++;u(k,k.isInline&&!k.isLeaf?g.filter(E=>!E.inline):g.slice(),d.forChild(p,k),I),p=T}})(this.node,this.innerDeco,(c,d,h)=>{c.spec.marks?a.syncToMarks(c.spec.marks,n,e):c.type.side>=0&&!h&&a.syncToMarks(d==this.node.childCount?ee.none:this.node.child(d).marks,n,e),a.placeWidget(c,e,r)},(c,d,h,u)=>{let f;a.syncToMarks(c.marks,n,e),a.findNodeMatch(c,d,h,u)||l&&e.state.selection.from>r&&e.state.selection.to<r+c.nodeSize&&(f=a.findIndexWithChild(i.node))>-1&&a.updateNodeAt(c,d,h,f,e)||a.updateNextNode(c,d,h,e,u,r)||a.addNode(c,d,h,e,r),r+=c.nodeSize}),a.syncToMarks([],n,e),this.node.isTextblock&&a.addTextblockHacks(),a.destroyRest(),(a.changed||this.dirty==2)&&(s&&this.protectLocalComposition(e,s),Kl(this.contentDOM,this.children,e),gn&&function(c){if(c.nodeName=="UL"||c.nodeName=="OL"){let d=c.style.cssText;c.style.cssText=d+"; list-style: square !important",window.getComputedStyle(c).listStyle,c.style.cssText=d}}(this.dom))}localCompositionInfo(e,t){let{from:n,to:r}=e.state.selection;if(!(e.state.selection instanceof G)||n<t||r>t+this.node.content.size)return null;let i=e.input.compositionNode;if(!i||!this.dom.contains(i.parentNode))return null;if(this.node.inlineContent){let s=i.nodeValue,l=function(a,c,d,h){for(let u=0,f=0;u<a.childCount&&f<=h;){let p=a.child(u++),m=f;if(f+=p.nodeSize,!p.isText)continue;let g=p.text;for(;u<a.childCount;){let y=a.child(u++);if(f+=y.nodeSize,!y.isText)break;g+=y.text}if(f>=d){if(f>=h&&g.slice(h-c.length-m,h-m)==c)return h-c.length;let y=m<h?g.lastIndexOf(c,h-m-1):-1;if(y>=0&&y+c.length+m>=d)return m+y;if(d==h&&g.length>=h+c.length-m&&g.slice(h-m,h-m+c.length)==c)return h}}return-1}(this.node.content,s,n-t,r-t);return l<0?null:{node:i,pos:l,text:s}}return{node:i,pos:-1,text:""}}protectLocalComposition(e,{node:t,pos:n,text:r}){if(this.getDesc(t))return;let i=t;for(;i.parentNode!=this.contentDOM;i=i.parentNode){for(;i.previousSibling;)i.parentNode.removeChild(i.previousSibling);for(;i.nextSibling;)i.parentNode.removeChild(i.nextSibling);i.pmViewDesc&&(i.pmViewDesc=void 0)}let s=new Uc(this,i,t,r);e.input.compositionNodes.push(s),this.children=co(this.children,n,n+r.length,e,s)}update(e,t,n,r){return!(this.dirty==3||!e.sameMarkup(this.node))&&(this.updateInner(e,t,n,r),!0)}updateInner(e,t,n,r){this.updateOuterDeco(t),this.node=e,this.innerDeco=n,this.contentDOM&&this.updateChildren(r,this.posAtStart),this.dirty=0}updateOuterDeco(e){if(ao(e,this.outerDeco))return;let t=this.nodeDOM.nodeType!=1,n=this.dom;this.dom=Wl(this.dom,this.nodeDOM,lo(this.outerDeco,this.node,t),lo(e,this.node,t)),this.dom!=n&&(n.pmViewDesc=void 0,this.dom.pmViewDesc=this),this.outerDeco=e}selectNode(){this.nodeDOM.nodeType==1&&this.nodeDOM.classList.add("ProseMirror-selectednode"),!this.contentDOM&&this.node.type.spec.draggable||(this.dom.draggable=!0)}deselectNode(){this.nodeDOM.nodeType==1&&this.nodeDOM.classList.remove("ProseMirror-selectednode"),!this.contentDOM&&this.node.type.spec.draggable||this.dom.removeAttribute("draggable")}get domAtom(){return this.node.isAtom}}function Si(o,e,t,n,r){Jl(n,e,o);let i=new $t(void 0,o,e,t,n,n,n,r,0);return i.contentDOM&&i.updateChildren(r,0),i}class Cr extends $t{constructor(e,t,n,r,i,s,l){super(e,t,n,r,i,null,s,l,0)}parseRule(){let e=this.nodeDOM.parentNode;for(;e&&e!=this.dom&&!e.pmIsDeco;)e=e.parentNode;return{skip:e||!0}}update(e,t,n,r){return!(this.dirty==3||this.dirty!=0&&!this.inParent()||!e.sameMarkup(this.node))&&(this.updateOuterDeco(t),this.dirty==0&&e.text==this.node.text||e.text==this.nodeDOM.nodeValue||(this.nodeDOM.nodeValue=e.text,r.trackWrites==this.nodeDOM&&(r.trackWrites=null)),this.node=e,this.dirty=0,!0)}inParent(){let e=this.parent.contentDOM;for(let t=this.nodeDOM;t;t=t.parentNode)if(t==e)return!0;return!1}domFromPos(e){return{node:this.nodeDOM,offset:e}}localPosFromDOM(e,t,n){return e==this.nodeDOM?this.posAtStart+Math.min(t,this.node.text.length):super.localPosFromDOM(e,t,n)}ignoreMutation(e){return e.type!="characterData"&&e.type!="selection"}slice(e,t,n){let r=this.node.cut(e,t),i=document.createTextNode(r.text);return new Cr(this.parent,r,this.outerDeco,this.innerDeco,i,i,n)}markDirty(e,t){super.markDirty(e,t),this.dom==this.nodeDOM||e!=0&&t!=this.nodeDOM.nodeValue.length||(this.dirty=3)}get domAtom(){return!1}isText(e){return this.node.text==e}}class ql extends Fn{parseRule(){return{ignore:!0}}matchesHack(e){return this.dirty==0&&this.dom.nodeName==e}get domAtom(){return!0}get ignoreForCoords(){return this.dom.nodeName=="IMG"}}class Gc extends $t{constructor(e,t,n,r,i,s,l,a,c,d){super(e,t,n,r,i,s,l,c,d),this.spec=a}update(e,t,n,r){if(this.dirty==3)return!1;if(this.spec.update){let i=this.spec.update(e,t,n);return i&&this.updateInner(e,t,n,r),i}return!(!this.contentDOM&&!e.isLeaf)&&super.update(e,t,n,r)}selectNode(){this.spec.selectNode?this.spec.selectNode():super.selectNode()}deselectNode(){this.spec.deselectNode?this.spec.deselectNode():super.deselectNode()}setSelection(e,t,n,r){this.spec.setSelection?this.spec.setSelection(e,t,n):super.setSelection(e,t,n,r)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}stopEvent(e){return!!this.spec.stopEvent&&this.spec.stopEvent(e)}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}}function Kl(o,e,t){let n=o.firstChild,r=!1;for(let i=0;i<e.length;i++){let s=e[i],l=s.dom;if(l.parentNode==o){for(;l!=n;)n=Oi(n),r=!0;n=n.nextSibling}else r=!0,o.insertBefore(l,n);if(s instanceof jt){let a=n?n.previousSibling:o.lastChild;Kl(s.contentDOM,s.children,t),n=a?a.nextSibling:o.firstChild}}for(;n;)n=Oi(n),r=!0;r&&t.trackWrites==o&&(t.trackWrites=null)}const On=function(o){o&&(this.nodeName=o)};On.prototype=Object.create(null);const Rt=[new On];function lo(o,e,t){if(o.length==0)return Rt;let n=t?Rt[0]:new On,r=[n];for(let i=0;i<o.length;i++){let s=o[i].type.attrs;if(s){s.nodeName&&r.push(n=new On(s.nodeName));for(let l in s){let a=s[l];a!=null&&(t&&r.length==1&&r.push(n=new On(e.isInline?"span":"div")),l=="class"?n.class=(n.class?n.class+" ":"")+a:l=="style"?n.style=(n.style?n.style+";":"")+a:l!="nodeName"&&(n[l]=a))}}}return r}function Wl(o,e,t,n){if(t==Rt&&n==Rt)return e;let r=e;for(let i=0;i<n.length;i++){let s=n[i],l=t[i];if(i){let a;l&&l.nodeName==s.nodeName&&r!=o&&(a=r.parentNode)&&a.nodeName.toLowerCase()==s.nodeName||(a=document.createElement(s.nodeName),a.pmIsDeco=!0,a.appendChild(r),l=Rt[0]),r=a}Zc(r,l||Rt[0],s)}return r}function Zc(o,e,t){for(let n in e)n=="class"||n=="style"||n=="nodeName"||n in t||o.removeAttribute(n);for(let n in t)n!="class"&&n!="style"&&n!="nodeName"&&t[n]!=e[n]&&o.setAttribute(n,t[n]);if(e.class!=t.class){let n=e.class?e.class.split(" ").filter(Boolean):[],r=t.class?t.class.split(" ").filter(Boolean):[];for(let i=0;i<n.length;i++)r.indexOf(n[i])==-1&&o.classList.remove(n[i]);for(let i=0;i<r.length;i++)n.indexOf(r[i])==-1&&o.classList.add(r[i]);o.classList.length==0&&o.removeAttribute("class")}if(e.style!=t.style){if(e.style){let n,r=/\s*([\w\-\xa1-\uffff]+)\s*:(?:"(?:\\.|[^"])*"|'(?:\\.|[^'])*'|\(.*?\)|[^;])*/g;for(;n=r.exec(e.style);)o.style.removeProperty(n[1])}t.style&&(o.style.cssText+=t.style)}}function Jl(o,e,t){return Wl(o,o,Rt,lo(e,t,o.nodeType!=1))}function ao(o,e){if(o.length!=e.length)return!1;for(let t=0;t<o.length;t++)if(!o[t].type.eq(e[t].type))return!1;return!0}function Oi(o){let e=o.nextSibling;return o.parentNode.removeChild(o),e}class Yc{constructor(e,t,n){this.lock=t,this.view=n,this.index=0,this.stack=[],this.changed=!1,this.top=e,this.preMatch=function(r,i){let s=i,l=s.children.length,a=r.childCount,c=new Map,d=[];e:for(;a>0;){let h;for(;;)if(l){let f=s.children[l-1];if(!(f instanceof jt)){h=f,l--;break}s=f,l=f.children.length}else{if(s==i)break e;l=s.parent.children.indexOf(s),s=s.parent}let u=h.node;if(u){if(u!=r.child(a-1))break;--a,c.set(h,a),d.push(h)}}return{index:a,matched:c,matches:d.reverse()}}(e.node.content,e)}destroyBetween(e,t){if(e!=t){for(let n=e;n<t;n++)this.top.children[n].destroy();this.top.children.splice(e,t-e),this.changed=!0}}destroyRest(){this.destroyBetween(this.index,this.top.children.length)}syncToMarks(e,t,n){let r=0,i=this.stack.length>>1,s=Math.min(i,e.length);for(;r<s&&(r==i-1?this.top:this.stack[r+1<<1]).matchesMark(e[r])&&e[r].type.spec.spanning!==!1;)r++;for(;r<i;)this.destroyRest(),this.top.dirty=0,this.index=this.stack.pop(),this.top=this.stack.pop(),i--;for(;i<e.length;){this.stack.push(this.top,this.index+1);let l=-1;for(let a=this.index;a<Math.min(this.index+3,this.top.children.length);a++){let c=this.top.children[a];if(c.matchesMark(e[i])&&!this.isLocked(c.dom)){l=a;break}}if(l>-1)l>this.index&&(this.changed=!0,this.destroyBetween(this.index,l)),this.top=this.top.children[this.index];else{let a=jt.create(this.top,e[i],t,n);this.top.children.splice(this.index,0,a),this.top=a,this.changed=!0}this.index=0,i++}}findNodeMatch(e,t,n,r){let i,s=-1;if(r>=this.preMatch.index&&(i=this.preMatch.matches[r-this.preMatch.index]).parent==this.top&&i.matchesNode(e,t,n))s=this.top.children.indexOf(i,this.index);else for(let l=this.index,a=Math.min(this.top.children.length,l+5);l<a;l++){let c=this.top.children[l];if(c.matchesNode(e,t,n)&&!this.preMatch.matched.has(c)){s=l;break}}return!(s<0)&&(this.destroyBetween(this.index,s),this.index++,!0)}updateNodeAt(e,t,n,r,i){let s=this.top.children[r];return s.dirty==3&&s.dom==s.contentDOM&&(s.dirty=2),!!s.update(e,t,n,i)&&(this.destroyBetween(this.index,r),this.index++,!0)}findIndexWithChild(e){for(;;){let t=e.parentNode;if(!t)return-1;if(t==this.top.contentDOM){let n=e.pmViewDesc;if(n){for(let r=this.index;r<this.top.children.length;r++)if(this.top.children[r]==n)return r}return-1}e=t}}updateNextNode(e,t,n,r,i,s){for(let l=this.index;l<this.top.children.length;l++){let a=this.top.children[l];if(a instanceof $t){let c=this.preMatch.matched.get(a);if(c!=null&&c!=i)return!1;let d,h=a.dom,u=this.isLocked(h)&&!(e.isText&&a.node&&a.node.isText&&a.nodeDOM.nodeValue==e.text&&a.dirty!=3&&ao(t,a.outerDeco));if(!u&&a.update(e,t,n,r))return this.destroyBetween(this.index,l),a.dom!=h&&(this.changed=!0),this.index++,!0;if(!u&&(d=this.recreateWrapper(a,e,t,n,r,s)))return this.top.children[this.index]=d,d.contentDOM&&(d.dirty=2,d.updateChildren(r,s+1),d.dirty=0),this.changed=!0,this.index++,!0;break}}return!1}recreateWrapper(e,t,n,r,i,s){if(e.dirty||t.isAtom||!e.children.length||!e.node.content.eq(t.content))return null;let l=$t.create(this.top,t,n,r,i,s);if(l.contentDOM){l.children=e.children,e.children=[];for(let a of l.children)a.parent=l}return e.destroy(),l}addNode(e,t,n,r,i){let s=$t.create(this.top,e,t,n,r,i);s.contentDOM&&s.updateChildren(r,i+1),this.top.children.splice(this.index++,0,s),this.changed=!0}placeWidget(e,t,n){let r=this.index<this.top.children.length?this.top.children[this.index]:null;if(!r||!r.matchesWidget(e)||e!=r.widget&&r.widget.type.toDOM.parentNode){let i=new jl(this.top,e,t,n);this.top.children.splice(this.index++,0,i),this.changed=!0}else this.index++}addTextblockHacks(){let e=this.top.children[this.index-1],t=this.top;for(;e instanceof jt;)t=e,e=t.children[t.children.length-1];(!e||!(e instanceof Cr)||/\n$/.test(e.node.text)||this.view.requiresGeckoHackNode&&/\s$/.test(e.node.text))&&((Se||be)&&e&&e.dom.contentEditable=="false"&&this.addHackNode("IMG",t),this.addHackNode("BR",this.top))}addHackNode(e,t){if(t==this.top&&this.index<t.children.length&&t.children[this.index].matchesHack(e))this.index++;else{let n=document.createElement(e);e=="IMG"&&(n.className="ProseMirror-separator",n.alt=""),e=="BR"&&(n.className="ProseMirror-trailingBreak");let r=new ql(this.top,[],n,null);t!=this.top?t.children.push(r):t.children.splice(this.index++,0,r),this.changed=!0}}isLocked(e){return this.lock&&(e==this.lock||e.nodeType==1&&e.contains(this.lock.parentNode))}}function Xc(o,e){return o.type.side-e.type.side}function co(o,e,t,n,r){let i=[];for(let s=0,l=0;s<o.length;s++){let a=o[s],c=l,d=l+=a.size;c>=t||d<=e?i.push(a):(c<e&&i.push(a.slice(0,e-c,n)),r&&(i.push(r),r=void 0),d>t&&i.push(a.slice(t-c,a.size,n)))}return i}function Lo(o,e=null){let t=o.domSelectionRange(),n=o.state.doc;if(!t.focusNode)return null;let r=o.docView.nearestDesc(t.focusNode),i=r&&r.size==0,s=o.docView.posFromDOM(t.focusNode,t.focusOffset,1);if(s<0)return null;let l,a,c=n.resolve(s);if(yr(t)){for(l=c;r&&!r.node;)r=r.parent;let d=r.node;if(r&&d.isAtom&&j.isSelectable(d)&&r.parent&&(!d.isInline||!function(h,u,f){for(let p=u==0,m=u==Qe(h);p||m;){if(h==f)return!0;let g=Ce(h);if(!(h=h.parentNode))return!1;p=p&&g==0,m=m&&g==Qe(h)}}(t.focusNode,t.focusOffset,r.dom))){let h=r.posBefore;a=new j(s==h?c:n.resolve(h))}}else{let d=o.docView.posFromDOM(t.anchorNode,t.anchorOffset,1);if(d<0)return null;l=n.resolve(d)}return a||(a=Ao(o,l,c,e=="pointer"||o.state.selection.head<c.pos&&!i?1:-1)),a}function Ei(o){return o.editable?o.hasFocus():Gl(o)&&document.activeElement&&document.activeElement.contains(o.dom)}function at(o,e=!1){let t=o.state.selection;if(Ul(o,t),Ei(o)){if(!e&&o.input.mouseDown&&o.input.mouseDown.allowDefault&&be){let n=o.domSelectionRange(),r=o.domObserver.currentSelection;if(n.anchorNode&&r.anchorNode&&Jt(n.anchorNode,n.anchorOffset,r.anchorNode,r.anchorOffset))return o.input.mouseDown.delayedSelectionSync=!0,void o.domObserver.setCurSelection()}if(o.domObserver.disconnectSelection(),o.cursorWrapper)(function(n){let r=n.domSelection(),i=document.createRange(),s=n.cursorWrapper.dom,l=s.nodeName=="IMG";l?i.setEnd(s.parentNode,Ce(s)+1):i.setEnd(s,0),i.collapse(!1),r.removeAllRanges(),r.addRange(i),!l&&!n.state.selection.visible&&Ee&&bt<=11&&(s.disabled=!0,s.disabled=!1)})(o);else{let n,r,{anchor:i,head:s}=t;!Ti||t instanceof G||(t.$from.parent.inlineContent||(n=Ni(o,t.from)),t.empty||t.$from.parent.inlineContent||(r=Ni(o,t.to))),o.docView.setSelection(i,s,o.root,e),Ti&&(n&&Li(n),r&&Li(r)),t.visible?o.dom.classList.remove("ProseMirror-hideselection"):(o.dom.classList.add("ProseMirror-hideselection"),"onselectionchange"in document&&function(l){let a=l.dom.ownerDocument;a.removeEventListener("selectionchange",l.input.hideSelectionGuard);let c=l.domSelectionRange(),d=c.anchorNode,h=c.anchorOffset;a.addEventListener("selectionchange",l.input.hideSelectionGuard=()=>{c.anchorNode==d&&c.anchorOffset==h||(a.removeEventListener("selectionchange",l.input.hideSelectionGuard),setTimeout(()=>{Ei(l)&&!l.state.selection.visible||l.dom.classList.remove("ProseMirror-hideselection")},20))})}(o))}o.domObserver.setCurSelection(),o.domObserver.connectSelection()}}const Ti=Se||be&&Fc<63;function Ni(o,e){let{node:t,offset:n}=o.docView.domFromPos(e,0),r=n<t.childNodes.length?t.childNodes[n]:null,i=n?t.childNodes[n-1]:null;if(Se&&r&&r.contentEditable=="false")return Dr(r);if(!(r&&r.contentEditable!="false"||i&&i.contentEditable!="false")){if(r)return Dr(r);if(i)return Dr(i)}}function Dr(o){return o.contentEditable="true",Se&&o.draggable&&(o.draggable=!1,o.wasDraggable=!0),o}function Li(o){o.contentEditable="false",o.wasDraggable&&(o.draggable=!0,o.wasDraggable=null)}function Ul(o,e){if(e instanceof j){let t=o.docView.descAt(e.from);t!=o.lastSelectedViewDesc&&(Ai(o),t&&t.selectNode(),o.lastSelectedViewDesc=t)}else Ai(o)}function Ai(o){o.lastSelectedViewDesc&&(o.lastSelectedViewDesc.parent&&o.lastSelectedViewDesc.deselectNode(),o.lastSelectedViewDesc=void 0)}function Ao(o,e,t,n){return o.someProp("createSelectionBetween",r=>r(o,e,t))||G.between(e,t,n)}function Ii(o){return!(o.editable&&!o.hasFocus())&&Gl(o)}function Gl(o){let e=o.domSelectionRange();if(!e.anchorNode)return!1;try{return o.dom.contains(e.anchorNode.nodeType==3?e.anchorNode.parentNode:e.anchorNode)&&(o.editable||o.dom.contains(e.focusNode.nodeType==3?e.focusNode.parentNode:e.focusNode))}catch{return!1}}function ho(o,e){let{$anchor:t,$head:n}=o.selection,r=e>0?t.max(n):t.min(n),i=r.parent.inlineContent?r.depth?o.doc.resolve(e>0?r.after():r.before()):null:r;return i&&Z.findFrom(i,e)}function pt(o,e){return o.dispatch(o.state.tr.setSelection(e).scrollIntoView()),!0}function Di(o,e,t){let n=o.state.selection;if(!(n instanceof G)){if(n instanceof j&&n.node.isInline)return pt(o,new G(e>0?n.$to:n.$from));{let r=ho(o.state,e);return!!r&&pt(o,r)}}if(t.indexOf("s")>-1){let{$head:r}=n,i=r.textOffset?null:e<0?r.nodeBefore:r.nodeAfter;if(!i||i.isText||!i.isLeaf)return!1;let s=o.state.doc.resolve(r.pos+i.nodeSize*(e<0?-1:1));return pt(o,new G(n.$anchor,s))}if(!n.empty)return!1;if(o.endOfTextblock(e>0?"forward":"backward")){let r=ho(o.state,e);return!!(r&&r instanceof j)&&pt(o,r)}if(!(Be&&t.indexOf("m")>-1)){let r,i=n.$head,s=i.textOffset?null:e<0?i.nodeBefore:i.nodeAfter;if(!s||s.isText)return!1;let l=e<0?i.pos-s.nodeSize:i.pos;return!!(s.isAtom||(r=o.docView.descAt(l))&&!r.contentDOM)&&(j.isSelectable(s)?pt(o,new j(e<0?o.state.doc.resolve(i.pos-s.nodeSize):i)):!!zn&&pt(o,new G(o.state.doc.resolve(e<0?l:l+s.nodeSize))))}}function qn(o){return o.nodeType==3?o.nodeValue.length:o.childNodes.length}function xn(o,e){let t=o.pmViewDesc;return t&&t.size==0&&(e<0||o.nextSibling||o.nodeName!="BR")}function Qt(o,e){return e<0?function(t){let n=t.domSelectionRange(),r=n.focusNode,i=n.focusOffset;if(!r)return;let s,l,a=!1;for(Ue&&r.nodeType==1&&i<qn(r)&&xn(r.childNodes[i],-1)&&(a=!0);;)if(i>0){if(r.nodeType!=1)break;{let c=r.childNodes[i-1];if(xn(c,-1))s=r,l=--i;else{if(c.nodeType!=3)break;r=c,i=r.nodeValue.length}}}else{if(Ri(r))break;{let c=r.previousSibling;for(;c&&xn(c,-1);)s=r.parentNode,l=Ce(c),c=c.previousSibling;if(c)r=c,i=qn(r);else{if(r=r.parentNode,r==t.dom)break;i=0}}}a?Rr(t,r,i):s&&Rr(t,s,l)}(o):function(t){let n=t.domSelectionRange(),r=n.focusNode,i=n.focusOffset;if(!r)return;let s,l,a=qn(r);for(;;)if(i<a){if(r.nodeType!=1||!xn(r.childNodes[i],1))break;s=r,l=++i}else{if(Ri(r))break;{let c=r.nextSibling;for(;c&&xn(c,1);)s=c.parentNode,l=Ce(c)+1,c=c.nextSibling;if(c)r=c,i=0,a=qn(r);else{if(r=r.parentNode,r==t.dom)break;i=a=0}}}s&&Rr(t,s,l)}(o)}function Ri(o){let e=o.pmViewDesc;return e&&e.node&&e.node.isBlock}function Rr(o,e,t){if(e.nodeType!=3){let i,s;(s=function(l,a){for(;l&&a==l.childNodes.length&&!Rn(l);)a=Ce(l)+1,l=l.parentNode;for(;l&&a<l.childNodes.length;){let c=l.childNodes[a];if(c.nodeType==3)return c;if(c.nodeType==1&&c.contentEditable=="false")break;l=c,a=0}}(e,t))?(e=s,t=0):(i=function(l,a){for(;l&&!a&&!Rn(l);)a=Ce(l),l=l.parentNode;for(;l&&a;){let c=l.childNodes[a-1];if(c.nodeType==3)return c;if(c.nodeType==1&&c.contentEditable=="false")break;a=(l=c).childNodes.length}}(e,t))&&(e=i,t=i.nodeValue.length)}let n=o.domSelection();if(yr(n)){let i=document.createRange();i.setEnd(e,t),i.setStart(e,t),n.removeAllRanges(),n.addRange(i)}else n.extend&&n.extend(e,t);o.domObserver.setCurSelection();let{state:r}=o;setTimeout(()=>{o.state==r&&at(o)},50)}function _i(o,e){let t=o.state.doc.resolve(e);if(!be&&!Bc&&t.parent.inlineContent){let n=o.coordsAtPos(e);if(e>t.start()){let r=o.coordsAtPos(e-1),i=(r.top+r.bottom)/2;if(i>n.top&&i<n.bottom&&Math.abs(r.left-n.left)>1)return r.left<n.left?"ltr":"rtl"}if(e<t.end()){let r=o.coordsAtPos(e+1),i=(r.top+r.bottom)/2;if(i>n.top&&i<n.bottom&&Math.abs(r.left-n.left)>1)return r.left>n.left?"ltr":"rtl"}}return getComputedStyle(o.dom).direction=="rtl"?"rtl":"ltr"}function Pi(o,e,t){let n=o.state.selection;if(n instanceof G&&!n.empty||t.indexOf("s")>-1||Be&&t.indexOf("m")>-1)return!1;let{$from:r,$to:i}=n;if(!r.parent.inlineContent||o.endOfTextblock(e<0?"up":"down")){let s=ho(o.state,e);if(s&&s instanceof j)return pt(o,s)}if(!r.parent.inlineContent){let s=e<0?r:i,l=n instanceof Ve?Z.near(s,e):Z.findFrom(s,e);return!!l&&pt(o,l)}return!1}function zi(o,e){if(!(o.state.selection instanceof G))return!0;let{$head:t,$anchor:n,empty:r}=o.state.selection;if(!t.sameParent(n))return!0;if(!r)return!1;if(o.endOfTextblock(e>0?"forward":"backward"))return!0;let i=!t.textOffset&&(e<0?t.nodeBefore:t.nodeAfter);if(i&&!i.isText){let s=o.state.tr;return e<0?s.delete(t.pos-i.nodeSize,t.pos):s.delete(t.pos,t.pos+i.nodeSize),o.dispatch(s),!0}return!1}function Fi(o,e,t){o.domObserver.stop(),e.contentEditable=t,o.domObserver.start()}function Qc(o,e){let t=e.keyCode,n=function(r){let i="";return r.ctrlKey&&(i+="c"),r.metaKey&&(i+="m"),r.altKey&&(i+="a"),r.shiftKey&&(i+="s"),i}(e);if(t==8||Be&&t==72&&n=="c")return zi(o,-1)||Qt(o,-1);if(t==46&&!e.shiftKey||Be&&t==68&&n=="c")return zi(o,1)||Qt(o,1);if(t==13||t==27)return!0;if(t==37||Be&&t==66&&n=="c"){let r=t==37?_i(o,o.state.selection.from)=="ltr"?-1:1:-1;return Di(o,r,n)||Qt(o,r)}if(t==39||Be&&t==70&&n=="c"){let r=t==39?_i(o,o.state.selection.from)=="ltr"?1:-1:1;return Di(o,r,n)||Qt(o,r)}return t==38||Be&&t==80&&n=="c"?Pi(o,-1,n)||Qt(o,-1):t==40||Be&&t==78&&n=="c"?function(r){if(!Se||r.state.selection.$head.parentOffset>0)return!1;let{focusNode:i,focusOffset:s}=r.domSelectionRange();if(i&&i.nodeType==1&&s==0&&i.firstChild&&i.firstChild.contentEditable=="false"){let l=i.firstChild;Fi(r,l,"true"),setTimeout(()=>Fi(r,l,"false"),20)}return!1}(o)||Pi(o,1,n)||Qt(o,1):n==(Be?"m":"c")&&(t==66||t==73||t==89||t==90)}function Zl(o,e){o.someProp("transformCopied",u=>{e=u(e,o)});let t=[],{content:n,openStart:r,openEnd:i}=e;for(;r>1&&i>1&&n.childCount==1&&n.firstChild.childCount==1;){r--,i--;let u=n.firstChild;t.push(u.type.name,u.attrs!=u.type.defaultAttrs?u.attrs:null),n=u.content}let s=o.someProp("clipboardSerializer")||Ut.fromSchema(o.state.schema),l=na(),a=l.createElement("div");a.appendChild(s.serializeFragment(n,{document:l}));let c,d=a.firstChild,h=0;for(;d&&d.nodeType==1&&(c=ta[d.nodeName.toLowerCase()]);){for(let u=c.length-1;u>=0;u--){let f=l.createElement(c[u]);for(;a.firstChild;)f.appendChild(a.firstChild);a.appendChild(f),h++}d=a.firstChild}return d&&d.nodeType==1&&d.setAttribute("data-pm-slice",`${r} ${i}${h?` -${h}`:""} ${JSON.stringify(t)}`),{dom:a,text:o.someProp("clipboardTextSerializer",u=>u(e,o))||e.content.textBetween(0,e.content.size,`

`),slice:e}}function Yl(o,e,t,n,r){let i,s,l=r.parent.type.spec.code;if(!t&&!e)return null;let a=e&&(n||l||!t);if(a){if(o.someProp("transformPastedText",u=>{e=u(e,l||n,o)}),l)return e?new L(O.from(o.state.schema.text(e.replace(/\r\n?/g,`
`))),0,0):L.empty;let h=o.someProp("clipboardTextParser",u=>u(e,r,n,o));if(h)s=h;else{let u=r.marks(),{schema:f}=o.state,p=Ut.fromSchema(f);i=document.createElement("div"),e.split(/(?:\r\n?|\n)+/).forEach(m=>{let g=i.appendChild(document.createElement("p"));m&&g.appendChild(p.serializeNode(f.text(m,u)))})}}else o.someProp("transformPastedHTML",h=>{t=h(t,o)}),i=function(h){let u=/^(\s*<meta [^>]*>)*/.exec(h);u&&(h=h.slice(u[0].length));let f,p=na().createElement("div"),m=/<([a-z][^>\s]+)/i.exec(h);if((f=m&&ta[m[1].toLowerCase()])&&(h=f.map(g=>"<"+g+">").join("")+h+f.map(g=>"</"+g+">").reverse().join("")),p.innerHTML=h,f)for(let g=0;g<f.length;g++)p=p.querySelector(f[g])||p;return p}(t),zn&&function(h){let u=h.querySelectorAll(be?"span:not([class]):not([style])":"span.Apple-converted-space");for(let f=0;f<u.length;f++){let p=u[f];p.childNodes.length==1&&p.textContent==" "&&p.parentNode&&p.parentNode.replaceChild(h.ownerDocument.createTextNode(" "),p)}}(i);let c=i&&i.querySelector("[data-pm-slice]"),d=c&&/^(\d+) (\d+)(?: -(\d+))? (.*)/.exec(c.getAttribute("data-pm-slice")||"");if(d&&d[3])for(let h=+d[3];h>0;h--){let u=i.firstChild;for(;u&&u.nodeType!=1;)u=u.nextSibling;if(!u)break;i=u}if(s||(s=(o.someProp("clipboardParser")||o.someProp("domParser")||pn.fromSchema(o.state.schema)).parseSlice(i,{preserveWhitespace:!(!a&&!d),context:r,ruleFromNode:u=>u.nodeName!="BR"||u.nextSibling||!u.parentNode||ed.test(u.parentNode.nodeName)?null:{ignore:!0}})),d)s=function(h,u){if(!h.size)return h;let f,p=h.content.firstChild.type.schema;try{f=JSON.parse(u)}catch{return h}let{content:m,openStart:g,openEnd:y}=h;for(let C=f.length-2;C>=0;C-=2){let v=p.nodes[f[C]];if(!v||v.hasRequiredAttrs())break;m=O.from(v.create(f[C+1],m)),g++,y++}return new L(m,g,y)}(Bi(s,+d[1],+d[2]),d[4]);else if(s=L.maxOpen(function(h,u){if(h.childCount<2)return h;for(let f=u.depth;f>=0;f--){let p,m=u.node(f).contentMatchAt(u.index(f)),g=[];if(h.forEach(y=>{if(!g)return;let C,v=m.findWrapping(y.type);if(!v)return g=null;if(C=g.length&&p.length&&Ql(v,p,y,g[g.length-1],0))g[g.length-1]=C;else{g.length&&(g[g.length-1]=ea(g[g.length-1],p.length));let b=Xl(y,v);g.push(b),m=m.matchType(b.type),p=v}}),g)return O.from(g)}return h}(s.content,r),!0),s.openStart||s.openEnd){let h=0,u=0;for(let f=s.content.firstChild;h<s.openStart&&!f.type.spec.isolating;h++,f=f.firstChild);for(let f=s.content.lastChild;u<s.openEnd&&!f.type.spec.isolating;u++,f=f.lastChild);s=Bi(s,h,u)}return o.someProp("transformPasted",h=>{s=h(s,o)}),s}const ed=/^(a|abbr|acronym|b|cite|code|del|em|i|ins|kbd|label|output|q|ruby|s|samp|span|strong|sub|sup|time|u|tt|var)$/i;function Xl(o,e,t=0){for(let n=e.length-1;n>=t;n--)o=e[n].create(null,O.from(o));return o}function Ql(o,e,t,n,r){if(r<o.length&&r<e.length&&o[r]==e[r]){let i=Ql(o,e,t,n.lastChild,r+1);if(i)return n.copy(n.content.replaceChild(n.childCount-1,i));if(n.contentMatchAt(n.childCount).matchType(r==o.length-1?t.type:o[r+1]))return n.copy(n.content.append(O.from(Xl(t,o,r+1))))}}function ea(o,e){if(e==0)return o;let t=o.content.replaceChild(o.childCount-1,ea(o.lastChild,e-1)),n=o.contentMatchAt(o.childCount).fillBefore(O.empty,!0);return o.copy(t.append(n))}function uo(o,e,t,n,r,i){let s=e<0?o.firstChild:o.lastChild,l=s.content;return o.childCount>1&&(i=0),r<n-1&&(l=uo(l,e,t,n,r+1,i)),r>=t&&(l=e<0?s.contentMatchAt(0).fillBefore(l,i<=r).append(l):l.append(s.contentMatchAt(s.childCount).fillBefore(O.empty,!0))),o.replaceChild(e<0?0:o.childCount-1,s.copy(l))}function Bi(o,e,t){return e<o.openStart&&(o=new L(uo(o.content,-1,e,o.openStart,0,o.openEnd),e,o.openEnd)),t<o.openEnd&&(o=new L(uo(o.content,1,t,o.openEnd,0,0),o.openStart,t)),o}const ta={thead:["table"],tbody:["table"],tfoot:["table"],caption:["table"],colgroup:["table"],col:["table","colgroup"],tr:["table","tbody"],td:["table","tbody","tr"],th:["table","tbody","tr"]};let Vi=null;function na(){return Vi||(Vi=document.implementation.createHTMLDocument("title"))}const $e={},Me={},td={touchstart:!0,touchmove:!0};class nd{constructor(){this.shiftKey=!1,this.mouseDown=null,this.lastKeyCode=null,this.lastKeyCodeTime=0,this.lastClick={time:0,x:0,y:0,type:""},this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastIOSEnter=0,this.lastIOSEnterFallbackTimeout=-1,this.lastFocus=0,this.lastTouch=0,this.lastAndroidDelete=0,this.composing=!1,this.compositionNode=null,this.composingTimeout=-1,this.compositionNodes=[],this.compositionEndedAt=-2e8,this.compositionID=1,this.compositionPendingChanges=0,this.domChangeCount=0,this.eventHandlers=Object.create(null),this.hideSelectionGuard=null}}function xt(o,e){o.input.lastSelectionOrigin=e,o.input.lastSelectionTime=Date.now()}function _r(o){o.someProp("handleDOMEvents",e=>{for(let t in e)o.input.eventHandlers[t]||o.dom.addEventListener(t,o.input.eventHandlers[t]=n=>po(o,n))})}function po(o,e){return o.someProp("handleDOMEvents",t=>{let n=t[e.type];return!!n&&(n(o,e)||e.defaultPrevented)})}function rd(o,e){if(!e.bubbles)return!0;if(e.defaultPrevented)return!1;for(let t=e.target;t!=o.dom;t=t.parentNode)if(!t||t.nodeType==11||t.pmViewDesc&&t.pmViewDesc.stopEvent(e))return!1;return!0}function nr(o){return{left:o.clientX,top:o.clientY}}function Io(o,e,t,n,r){if(n==-1)return!1;let i=o.state.doc.resolve(n);for(let s=i.depth+1;s>0;s--)if(o.someProp(e,l=>s>i.depth?l(o,t,i.nodeAfter,i.before(s),r,!0):l(o,t,i.node(s),i.before(s),r,!1)))return!0;return!1}function dn(o,e,t){o.focused||o.focus();let n=o.state.tr.setSelection(e);n.setMeta("pointer",!0),o.dispatch(n)}function od(o,e,t,n,r){return Io(o,"handleClickOn",e,t,n)||o.someProp("handleClick",i=>i(o,e,n))||(r?function(i,s){if(s==-1)return!1;let l,a,c=i.state.selection;c instanceof j&&(l=c.node);let d=i.state.doc.resolve(s);for(let h=d.depth+1;h>0;h--){let u=h>d.depth?d.nodeAfter:d.node(h);if(j.isSelectable(u)){a=l&&c.$from.depth>0&&h>=c.$from.depth&&d.before(c.$from.depth+1)==c.$from.pos?d.before(c.$from.depth):d.before(h);break}}return a!=null&&(dn(i,j.create(i.state.doc,a)),!0)}(o,t):function(i,s){if(s==-1)return!1;let l=i.state.doc.resolve(s),a=l.nodeAfter;return!!(a&&a.isAtom&&j.isSelectable(a))&&(dn(i,new j(l)),!0)}(o,t))}function id(o,e,t,n){return Io(o,"handleDoubleClickOn",e,t,n)||o.someProp("handleDoubleClick",r=>r(o,e,n))}function sd(o,e,t,n){return Io(o,"handleTripleClickOn",e,t,n)||o.someProp("handleTripleClick",r=>r(o,e,n))||function(r,i,s){if(s.button!=0)return!1;let l=r.state.doc;if(i==-1)return!!l.inlineContent&&(dn(r,G.create(l,0,l.content.size)),!0);let a=l.resolve(i);for(let c=a.depth+1;c>0;c--){let d=c>a.depth?a.nodeAfter:a.node(c),h=a.before(c);if(d.inlineContent)dn(r,G.create(l,h+1,h+1+d.content.size));else{if(!j.isSelectable(d))continue;dn(r,j.create(l,h))}return!0}}(o,t,n)}function fo(o){return rr(o)}Me.keydown=(o,e)=>{let t=e;if(o.input.shiftKey=t.keyCode==16||t.shiftKey,!Hi(o,t)&&(o.input.lastKeyCode=t.keyCode,o.input.lastKeyCodeTime=Date.now(),!We||!be||t.keyCode!=13))if(t.keyCode!=229&&o.domObserver.forceFlush(),!gn||t.keyCode!=13||t.ctrlKey||t.altKey||t.metaKey)o.someProp("handleKeyDown",n=>n(o,t))||Qc(o,t)?t.preventDefault():xt(o,"key");else{let n=Date.now();o.input.lastIOSEnter=n,o.input.lastIOSEnterFallbackTimeout=setTimeout(()=>{o.input.lastIOSEnter==n&&(o.someProp("handleKeyDown",r=>r(o,Lt(13,"Enter"))),o.input.lastIOSEnter=0)},200)}},Me.keyup=(o,e)=>{e.keyCode==16&&(o.input.shiftKey=!1)},Me.keypress=(o,e)=>{let t=e;if(Hi(o,t)||!t.charCode||t.ctrlKey&&!t.altKey||Be&&t.metaKey)return;if(o.someProp("handleKeyPress",r=>r(o,t)))return void t.preventDefault();let n=o.state.selection;if(!(n instanceof G&&n.$from.sameParent(n.$to))){let r=String.fromCharCode(t.charCode);/[\r\n]/.test(r)||o.someProp("handleTextInput",i=>i(o,n.$from.pos,n.$to.pos,r))||o.dispatch(o.state.tr.insertText(r).scrollIntoView()),t.preventDefault()}};const ra=Be?"metaKey":"ctrlKey";$e.mousedown=(o,e)=>{let t=e;o.input.shiftKey=t.shiftKey;let n=fo(o),r=Date.now(),i="singleClick";r-o.input.lastClick.time<500&&function(l,a){let c=a.x-l.clientX,d=a.y-l.clientY;return c*c+d*d<100}(t,o.input.lastClick)&&!t[ra]&&(o.input.lastClick.type=="singleClick"?i="doubleClick":o.input.lastClick.type=="doubleClick"&&(i="tripleClick")),o.input.lastClick={time:r,x:t.clientX,y:t.clientY,type:i};let s=o.posAtCoords(nr(t));s&&(i=="singleClick"?(o.input.mouseDown&&o.input.mouseDown.done(),o.input.mouseDown=new ld(o,s,t,!!n)):(i=="doubleClick"?id:sd)(o,s.pos,s.inside,t)?t.preventDefault():xt(o,"pointer"))};class ld{constructor(e,t,n,r){let i,s;if(this.view=e,this.pos=t,this.event=n,this.flushed=r,this.delayedSelectionSync=!1,this.mightDrag=null,this.startDoc=e.state.doc,this.selectNode=!!n[ra],this.allowDefault=n.shiftKey,t.inside>-1)i=e.state.doc.nodeAt(t.inside),s=t.inside;else{let d=e.state.doc.resolve(t.pos);i=d.parent,s=d.depth?d.before():0}const l=r?null:n.target,a=l?e.docView.nearestDesc(l,!0):null;this.target=a?a.dom:null;let{selection:c}=e.state;(n.button==0&&i.type.spec.draggable&&i.type.spec.selectable!==!1||c instanceof j&&c.from<=s&&c.to>s)&&(this.mightDrag={node:i,pos:s,addAttr:!(!this.target||this.target.draggable),setUneditable:!(!this.target||!Ue||this.target.hasAttribute("contentEditable"))}),this.target&&this.mightDrag&&(this.mightDrag.addAttr||this.mightDrag.setUneditable)&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&(this.target.draggable=!0),this.mightDrag.setUneditable&&setTimeout(()=>{this.view.input.mouseDown==this&&this.target.setAttribute("contentEditable","false")},20),this.view.domObserver.start()),e.root.addEventListener("mouseup",this.up=this.up.bind(this)),e.root.addEventListener("mousemove",this.move=this.move.bind(this)),xt(e,"pointer")}done(){this.view.root.removeEventListener("mouseup",this.up),this.view.root.removeEventListener("mousemove",this.move),this.mightDrag&&this.target&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&this.target.removeAttribute("draggable"),this.mightDrag.setUneditable&&this.target.removeAttribute("contentEditable"),this.view.domObserver.start()),this.delayedSelectionSync&&setTimeout(()=>at(this.view)),this.view.input.mouseDown=null}up(e){if(this.done(),!this.view.dom.contains(e.target))return;let t=this.pos;this.view.state.doc!=this.startDoc&&(t=this.view.posAtCoords(nr(e))),this.updateAllowDefault(e),this.allowDefault||!t?xt(this.view,"pointer"):od(this.view,t.pos,t.inside,e,this.selectNode)?e.preventDefault():e.button==0&&(this.flushed||Se&&this.mightDrag&&!this.mightDrag.node.isAtom||be&&!this.view.state.selection.visible&&Math.min(Math.abs(t.pos-this.view.state.selection.from),Math.abs(t.pos-this.view.state.selection.to))<=2)?(dn(this.view,Z.near(this.view.state.doc.resolve(t.pos))),e.preventDefault()):xt(this.view,"pointer")}move(e){this.updateAllowDefault(e),xt(this.view,"pointer"),e.buttons==0&&this.done()}updateAllowDefault(e){!this.allowDefault&&(Math.abs(this.event.x-e.clientX)>4||Math.abs(this.event.y-e.clientY)>4)&&(this.allowDefault=!0)}}function Hi(o,e){return!!o.composing||!!(Se&&Math.abs(e.timeStamp-o.input.compositionEndedAt)<500)&&(o.input.compositionEndedAt=-2e8,!0)}$e.touchstart=o=>{o.input.lastTouch=Date.now(),fo(o),xt(o,"pointer")},$e.touchmove=o=>{o.input.lastTouch=Date.now(),xt(o,"pointer")},$e.contextmenu=o=>fo(o);const ad=We?5e3:-1;function ji(o,e){clearTimeout(o.input.composingTimeout),e>-1&&(o.input.composingTimeout=setTimeout(()=>rr(o),e))}function oa(o){for(o.composing&&(o.input.composing=!1,o.input.compositionEndedAt=function(){let e=document.createEvent("Event");return e.initEvent("event",!0,!0),e.timeStamp}());o.input.compositionNodes.length>0;)o.input.compositionNodes.pop().markParentsDirty()}function cd(o){let e=o.domSelectionRange();if(!e.focusNode)return null;let t=function(r,i){for(;;){if(r.nodeType==3&&i)return r;if(r.nodeType==1&&i>0){if(r.contentEditable=="false")return null;i=Qe(r=r.childNodes[i-1])}else{if(!r.parentNode||Rn(r))return null;i=Ce(r),r=r.parentNode}}}(e.focusNode,e.focusOffset),n=function(r,i){for(;;){if(r.nodeType==3&&i<r.nodeValue.length)return r;if(r.nodeType==1&&i<r.childNodes.length){if(r.contentEditable=="false")return null;r=r.childNodes[i],i=0}else{if(!r.parentNode||Rn(r))return null;i=Ce(r)+1,r=r.parentNode}}}(e.focusNode,e.focusOffset);if(t&&n&&t!=n){let r=n.pmViewDesc;if(!r||!r.isText(n.nodeValue))return n;if(o.input.compositionNode==n){let i=t.pmViewDesc;if(i&&i.isText(t.nodeValue))return n}}return t||n}function rr(o,e=!1){if(!(We&&o.domObserver.flushingSoon>=0)){if(o.domObserver.forceFlush(),oa(o),e||o.docView&&o.docView.dirty){let t=Lo(o);return t&&!t.eq(o.state.selection)?o.dispatch(o.state.tr.setSelection(t)):o.updateState(o.state),!0}return!1}}Me.compositionstart=Me.compositionupdate=o=>{if(!o.composing){o.domObserver.flush();let{state:e}=o,t=e.selection.$from;if(e.selection.empty&&(e.storedMarks||!t.textOffset&&t.parentOffset&&t.nodeBefore.marks.some(n=>n.type.spec.inclusive===!1)))o.markCursor=o.state.storedMarks||t.marks(),rr(o,!0),o.markCursor=null;else if(rr(o),Ue&&e.selection.empty&&t.parentOffset&&!t.textOffset&&t.nodeBefore.marks.length){let n=o.domSelectionRange();for(let r=n.focusNode,i=n.focusOffset;r&&r.nodeType==1&&i!=0;){let s=i<0?r.lastChild:r.childNodes[i-1];if(!s)break;if(s.nodeType==3){o.domSelection().collapse(s,s.nodeValue.length);break}r=s,i=-1}}o.input.composing=!0}ji(o,ad)},Me.compositionend=(o,e)=>{o.composing&&(o.input.composing=!1,o.input.compositionEndedAt=e.timeStamp,o.input.compositionPendingChanges=o.domObserver.pendingRecords().length?o.input.compositionID:0,o.input.compositionNode=null,o.input.compositionPendingChanges&&Promise.resolve().then(()=>o.domObserver.flush()),o.input.compositionID++,ji(o,20))};const En=Ee&&bt<15||gn&&Vc<604;function Tn(o,e,t,n,r){let i=Yl(o,e,t,n,o.state.selection.$from);if(o.someProp("handlePaste",a=>a(o,r,i||L.empty)))return!0;if(!i)return!1;let s=function(a){return a.openStart==0&&a.openEnd==0&&a.content.childCount==1?a.content.firstChild:null}(i),l=s?o.state.tr.replaceSelectionWith(s,n):o.state.tr.replaceSelection(i);return o.dispatch(l.scrollIntoView().setMeta("paste",!0).setMeta("uiEvent","paste")),!0}function ia(o){let e=o.getData("text/plain")||o.getData("Text");if(e)return e;let t=o.getData("text/uri-list");return t?t.replace(/\r?\n/g," "):""}$e.copy=Me.cut=(o,e)=>{let t=e,n=o.state.selection,r=t.type=="cut";if(n.empty)return;let i=En?null:t.clipboardData,s=n.content(),{dom:l,text:a}=Zl(o,s);i?(t.preventDefault(),i.clearData(),i.setData("text/html",l.innerHTML),i.setData("text/plain",a)):function(c,d){if(!c.dom.parentNode)return;let h=c.dom.parentNode.appendChild(document.createElement("div"));h.appendChild(d),h.style.cssText="position: fixed; left: -10000px; top: 10px";let u=getSelection(),f=document.createRange();f.selectNodeContents(d),c.dom.blur(),u.removeAllRanges(),u.addRange(f),setTimeout(()=>{h.parentNode&&h.parentNode.removeChild(h),c.focus()},50)}(o,l),r&&o.dispatch(o.state.tr.deleteSelection().scrollIntoView().setMeta("uiEvent","cut"))},Me.paste=(o,e)=>{let t=e;if(o.composing&&!We)return;let n=En?null:t.clipboardData,r=o.input.shiftKey&&o.input.lastKeyCode!=45;n&&Tn(o,ia(n),n.getData("text/html"),r,t)?t.preventDefault():function(i,s){if(!i.dom.parentNode)return;let l=i.input.shiftKey||i.state.selection.$from.parent.type.spec.code,a=i.dom.parentNode.appendChild(document.createElement(l?"textarea":"div"));l||(a.contentEditable="true"),a.style.cssText="position: fixed; left: -10000px; top: 10px",a.focus();let c=i.input.shiftKey&&i.input.lastKeyCode!=45;setTimeout(()=>{i.focus(),a.parentNode&&a.parentNode.removeChild(a),l?Tn(i,a.value,null,c,s):Tn(i,a.textContent,a.innerHTML,c,s)},50)}(o,t)};class sa{constructor(e,t,n){this.slice=e,this.move=t,this.node=n}}const qi=Be?"altKey":"ctrlKey";$e.dragstart=(o,e)=>{let t=e,n=o.input.mouseDown;if(n&&n.done(),!t.dataTransfer)return;let r,i=o.state.selection,s=i.empty?null:o.posAtCoords(nr(t));if(!(s&&s.pos>=i.from&&s.pos<=(i instanceof j?i.to-1:i.to))){if(n&&n.mightDrag)r=j.create(o.state.doc,n.mightDrag.pos);else if(t.target&&t.target.nodeType==1){let h=o.docView.nearestDesc(t.target,!0);h&&h.node.type.spec.draggable&&h!=o.docView&&(r=j.create(o.state.doc,h.posBefore))}}let l=(r||o.state.selection).content(),{dom:a,text:c,slice:d}=Zl(o,l);t.dataTransfer.clearData(),t.dataTransfer.setData(En?"Text":"text/html",a.innerHTML),t.dataTransfer.effectAllowed="copyMove",En||t.dataTransfer.setData("text/plain",c),o.dragging=new sa(d,!t[qi],r)},$e.dragend=o=>{let e=o.dragging;window.setTimeout(()=>{o.dragging==e&&(o.dragging=null)},50)},Me.dragover=Me.dragenter=(o,e)=>e.preventDefault(),Me.drop=(o,e)=>{let t=e,n=o.dragging;if(o.dragging=null,!t.dataTransfer)return;let r=o.posAtCoords(nr(t));if(!r)return;let i=o.state.doc.resolve(r.pos),s=n&&n.slice;s?o.someProp("transformPasted",p=>{s=p(s,o)}):s=Yl(o,ia(t.dataTransfer),En?null:t.dataTransfer.getData("text/html"),!1,i);let l=!(!n||t[qi]);if(o.someProp("handleDrop",p=>p(o,t,s||L.empty,l)))return void t.preventDefault();if(!s)return;t.preventDefault();let a=s?function(p,m,g){let y=p.resolve(m);if(!g.content.size)return m;let C=g.content;for(let v=0;v<g.openStart;v++)C=C.firstChild.content;for(let v=1;v<=(g.openStart==0&&g.size?2:1);v++)for(let b=y.depth;b>=0;b--){let k=b==y.depth?0:y.pos<=(y.start(b+1)+y.end(b+1))/2?-1:1,I=y.index(b)+(k>0?1:0),T=y.node(b),E=!1;if(v==1)E=T.canReplace(I,I,C);else{let S=T.contentMatchAt(I).findWrapping(C.firstChild.type);E=S&&T.canReplaceWith(I,I,S[0])}if(E)return k==0?y.pos:k<0?y.before(b+1):y.after(b+1)}return null}(o.state.doc,i.pos,s):i.pos;a==null&&(a=i.pos);let c=o.state.tr;if(l){let{node:p}=n;p?p.replace(c):c.deleteSelection()}let d=c.mapping.map(a),h=s.openStart==0&&s.openEnd==0&&s.content.childCount==1,u=c.doc;if(h?c.replaceRangeWith(d,d,s.content.firstChild):c.replaceRange(d,d,s),c.doc.eq(u))return;let f=c.doc.resolve(d);if(h&&j.isSelectable(s.content.firstChild)&&f.nodeAfter&&f.nodeAfter.sameMarkup(s.content.firstChild))c.setSelection(new j(f));else{let p=c.mapping.map(a);c.mapping.maps[c.mapping.maps.length-1].forEach((m,g,y,C)=>p=C),c.setSelection(Ao(o,f,c.doc.resolve(p)))}o.focus(),o.dispatch(c.setMeta("uiEvent","drop"))},$e.focus=o=>{o.input.lastFocus=Date.now(),o.focused||(o.domObserver.stop(),o.dom.classList.add("ProseMirror-focused"),o.domObserver.start(),o.focused=!0,setTimeout(()=>{o.docView&&o.hasFocus()&&!o.domObserver.currentSelection.eq(o.domSelectionRange())&&at(o)},20))},$e.blur=(o,e)=>{let t=e;o.focused&&(o.domObserver.stop(),o.dom.classList.remove("ProseMirror-focused"),o.domObserver.start(),t.relatedTarget&&o.dom.contains(t.relatedTarget)&&o.domObserver.currentSelection.clear(),o.focused=!1)},$e.beforeinput=(o,e)=>{if(be&&We&&e.inputType=="deleteContentBackward"){o.domObserver.flushSoon();let{domChangeCount:t}=o.input;setTimeout(()=>{if(o.input.domChangeCount!=t||(o.dom.blur(),o.focus(),o.someProp("handleKeyDown",r=>r(o,Lt(8,"Backspace")))))return;let{$cursor:n}=o.state.selection;n&&n.pos>0&&o.dispatch(o.state.tr.delete(n.pos-1,n.pos).scrollIntoView())},50)}};for(let o in Me)$e[o]=Me[o];function _n(o,e){if(o==e)return!0;for(let t in o)if(o[t]!==e[t])return!1;for(let t in e)if(!(t in o))return!1;return!0}class or{constructor(e,t){this.toDOM=e,this.spec=t||qt,this.side=this.spec.side||0}map(e,t,n,r){let{pos:i,deleted:s}=e.mapResult(t.from+r,this.side<0?-1:1);return s?null:new Pe(i-n,i-n,this)}valid(){return!0}eq(e){return this==e||e instanceof or&&(this.spec.key&&this.spec.key==e.spec.key||this.toDOM==e.toDOM&&_n(this.spec,e.spec))}destroy(e){this.spec.destroy&&this.spec.destroy(e)}}class kt{constructor(e,t){this.attrs=e,this.spec=t||qt}map(e,t,n,r){let i=e.map(t.from+r,this.spec.inclusiveStart?-1:1)-n,s=e.map(t.to+r,this.spec.inclusiveEnd?1:-1)-n;return i>=s?null:new Pe(i,s,this)}valid(e,t){return t.from<t.to}eq(e){return this==e||e instanceof kt&&_n(this.attrs,e.attrs)&&_n(this.spec,e.spec)}static is(e){return e.type instanceof kt}destroy(){}}class Do{constructor(e,t){this.attrs=e,this.spec=t||qt}map(e,t,n,r){let i=e.mapResult(t.from+r,1);if(i.deleted)return null;let s=e.mapResult(t.to+r,-1);return s.deleted||s.pos<=i.pos?null:new Pe(i.pos-n,s.pos-n,this)}valid(e,t){let n,{index:r,offset:i}=e.content.findIndex(t.from);return i==t.from&&!(n=e.child(r)).isText&&i+n.nodeSize==t.to}eq(e){return this==e||e instanceof Do&&_n(this.attrs,e.attrs)&&_n(this.spec,e.spec)}destroy(){}}class Pe{constructor(e,t,n){this.from=e,this.to=t,this.type=n}copy(e,t){return new Pe(e,t,this.type)}eq(e,t=0){return this.type.eq(e.type)&&this.from+t==e.from&&this.to+t==e.to}map(e,t,n){return this.type.map(e,this,t,n)}static widget(e,t,n){return new Pe(e,e,new or(t,n))}static inline(e,t,n,r){return new Pe(e,t,new kt(n,r))}static node(e,t,n,r){return new Pe(e,t,new Do(n,r))}get spec(){return this.type.spec}get inline(){return this.type instanceof kt}get widget(){return this.type instanceof or}}const tn=[],qt={};class de{constructor(e,t){this.local=e.length?e:tn,this.children=t.length?t:tn}static create(e,t){return t.length?Gn(t,e,0,qt):we}find(e,t,n){let r=[];return this.findInner(e??0,t??1e9,r,0,n),r}findInner(e,t,n,r,i){for(let s=0;s<this.local.length;s++){let l=this.local[s];l.from<=t&&l.to>=e&&(!i||i(l.spec))&&n.push(l.copy(l.from+r,l.to+r))}for(let s=0;s<this.children.length;s+=3)if(this.children[s]<t&&this.children[s+1]>e){let l=this.children[s]+1;this.children[s+2].findInner(e-l,t-l,n,r+l,i)}}map(e,t,n){return this==we||e.maps.length==0?this:this.mapInner(e,t,0,0,n||qt)}mapInner(e,t,n,r,i){let s;for(let l=0;l<this.local.length;l++){let a=this.local[l].map(e,n,r);a&&a.type.valid(t,a)?(s||(s=[])).push(a):i.onRemove&&i.onRemove(this.local[l].spec)}return this.children.length?function(l,a,c,d,h,u,f){let p=l.slice();for(let g=0,y=u;g<c.maps.length;g++){let C=0;c.maps[g].forEach((v,b,k,I)=>{let T=I-k-(b-v);for(let E=0;E<p.length;E+=3){let S=p[E+1];if(S<0||v>S+y-C)continue;let A=p[E]+y-C;b>=A?p[E+1]=v<=A?-2:-1:v>=y&&T&&(p[E]+=T,p[E+1]+=T)}C+=T}),y=c.maps[g].map(y,-1)}let m=!1;for(let g=0;g<p.length;g+=3)if(p[g+1]<0){if(p[g+1]==-2){m=!0,p[g+1]=-1;continue}let y=c.map(l[g]+u),C=y-h;if(C<0||C>=d.content.size){m=!0;continue}let v=c.map(l[g+1]+u,-1)-h,{index:b,offset:k}=d.content.findIndex(C),I=d.maybeChild(b);if(I&&k==C&&k+I.nodeSize==v){let T=p[g+2].mapInner(c,I,y+1,l[g]+u+1,f);T!=we?(p[g]=C,p[g+1]=v,p[g+2]=T):(p[g+1]=-2,m=!0)}else m=!0}if(m){let g=function(C,v,b,k,I,T,E){function S(A,q){for(let H=0;H<A.local.length;H++){let J=A.local[H].map(k,I,q);J?b.push(J):E.onRemove&&E.onRemove(A.local[H].spec)}for(let H=0;H<A.children.length;H+=3)S(A.children[H+2],A.children[H]+q+1)}for(let A=0;A<C.length;A+=3)C[A+1]==-1&&S(C[A+2],v[A]+T+1);return b}(p,l,a,c,h,u,f),y=Gn(g,d,0,f);a=y.local;for(let C=0;C<p.length;C+=3)p[C+1]<0&&(p.splice(C,3),C-=3);for(let C=0,v=0;C<y.children.length;C+=3){let b=y.children[C];for(;v<p.length&&p[v]<b;)v+=3;p.splice(v,0,y.children[C],y.children[C+1],y.children[C+2])}}return new de(a.sort(_t),p)}(this.children,s||[],e,t,n,r,i):s?new de(s.sort(_t),tn):we}add(e,t){return t.length?this==we?de.create(e,t):this.addInner(e,t,0):this}addInner(e,t,n){let r,i=0;e.forEach((l,a)=>{let c,d=a+n;if(c=aa(t,l,d)){for(r||(r=this.children.slice());i<r.length&&r[i]<a;)i+=3;r[i]==a?r[i+2]=r[i+2].addInner(l,c,d+1):r.splice(i,0,a,a+l.nodeSize,Gn(c,l,d+1,qt)),i+=3}});let s=la(i?ca(t):t,-n);for(let l=0;l<s.length;l++)s[l].type.valid(e,s[l])||s.splice(l--,1);return new de(s.length?this.local.concat(s).sort(_t):this.local,r||this.children)}remove(e){return e.length==0||this==we?this:this.removeInner(e,0)}removeInner(e,t){let n=this.children,r=this.local;for(let i=0;i<n.length;i+=3){let s,l=n[i]+t,a=n[i+1]+t;for(let d,h=0;h<e.length;h++)(d=e[h])&&d.from>l&&d.to<a&&(e[h]=null,(s||(s=[])).push(d));if(!s)continue;n==this.children&&(n=this.children.slice());let c=n[i+2].removeInner(s,l+1);c!=we?n[i+2]=c:(n.splice(i,3),i-=3)}if(r.length){for(let i,s=0;s<e.length;s++)if(i=e[s])for(let l=0;l<r.length;l++)r[l].eq(i,t)&&(r==this.local&&(r=this.local.slice()),r.splice(l--,1))}return n==this.children&&r==this.local?this:r.length||n.length?new de(r,n):we}forChild(e,t){if(this==we)return this;if(t.isLeaf)return de.empty;let n,r;for(let l=0;l<this.children.length;l+=3)if(this.children[l]>=e){this.children[l]==e&&(n=this.children[l+2]);break}let i=e+1,s=i+t.content.size;for(let l=0;l<this.local.length;l++){let a=this.local[l];if(a.from<s&&a.to>i&&a.type instanceof kt){let c=Math.max(i,a.from)-i,d=Math.min(s,a.to)-i;c<d&&(r||(r=[])).push(a.copy(c,d))}}if(r){let l=new de(r.sort(_t),tn);return n?new yt([l,n]):l}return n||we}eq(e){if(this==e)return!0;if(!(e instanceof de)||this.local.length!=e.local.length||this.children.length!=e.children.length)return!1;for(let t=0;t<this.local.length;t++)if(!this.local[t].eq(e.local[t]))return!1;for(let t=0;t<this.children.length;t+=3)if(this.children[t]!=e.children[t]||this.children[t+1]!=e.children[t+1]||!this.children[t+2].eq(e.children[t+2]))return!1;return!0}locals(e){return Ro(this.localsInner(e))}localsInner(e){if(this==we)return tn;if(e.inlineContent||!this.local.some(kt.is))return this.local;let t=[];for(let n=0;n<this.local.length;n++)this.local[n].type instanceof kt||t.push(this.local[n]);return t}}de.empty=new de([],[]),de.removeOverlap=Ro;const we=de.empty;class yt{constructor(e){this.members=e}map(e,t){const n=this.members.map(r=>r.map(e,t,qt));return yt.from(n)}forChild(e,t){if(t.isLeaf)return de.empty;let n=[];for(let r=0;r<this.members.length;r++){let i=this.members[r].forChild(e,t);i!=we&&(i instanceof yt?n=n.concat(i.members):n.push(i))}return yt.from(n)}eq(e){if(!(e instanceof yt)||e.members.length!=this.members.length)return!1;for(let t=0;t<this.members.length;t++)if(!this.members[t].eq(e.members[t]))return!1;return!0}locals(e){let t,n=!0;for(let r=0;r<this.members.length;r++){let i=this.members[r].localsInner(e);if(i.length)if(t){n&&(t=t.slice(),n=!1);for(let s=0;s<i.length;s++)t.push(i[s])}else t=i}return t?Ro(n?t:t.sort(_t)):tn}static from(e){switch(e.length){case 0:return we;case 1:return e[0];default:return new yt(e.every(t=>t instanceof de)?e:e.reduce((t,n)=>t.concat(n instanceof de?n:n.members),[]))}}}function la(o,e){if(!e||!o.length)return o;let t=[];for(let n=0;n<o.length;n++){let r=o[n];t.push(new Pe(r.from+e,r.to+e,r.type))}return t}function aa(o,e,t){if(e.isLeaf)return null;let n=t+e.nodeSize,r=null;for(let i,s=0;s<o.length;s++)(i=o[s])&&i.from>t&&i.to<n&&((r||(r=[])).push(i),o[s]=null);return r}function ca(o){let e=[];for(let t=0;t<o.length;t++)o[t]!=null&&e.push(o[t]);return e}function Gn(o,e,t,n){let r=[],i=!1;e.forEach((l,a)=>{let c=aa(o,l,a+t);if(c){i=!0;let d=Gn(c,l,t+a+1,n);d!=we&&r.push(a,a+l.nodeSize,d)}});let s=la(i?ca(o):o,-t).sort(_t);for(let l=0;l<s.length;l++)s[l].type.valid(e,s[l])||(n.onRemove&&n.onRemove(s[l].spec),s.splice(l--,1));return s.length||r.length?new de(s,r):we}function _t(o,e){return o.from-e.from||o.to-e.to}function Ro(o){let e=o;for(let t=0;t<e.length-1;t++){let n=e[t];if(n.from!=n.to)for(let r=t+1;r<e.length;r++){let i=e[r];if(i.from!=n.from){i.from<n.to&&(e==o&&(e=o.slice()),e[t]=n.copy(n.from,i.from),Ki(e,r,n.copy(i.from,n.to)));break}i.to!=n.to&&(e==o&&(e=o.slice()),e[r]=i.copy(i.from,n.to),Ki(e,r+1,i.copy(n.to,i.to)))}}return e}function Ki(o,e,t){for(;e<o.length&&_t(t,o[e])>0;)e++;o.splice(e,0,t)}function Pr(o){let e=[];return o.someProp("decorations",t=>{let n=t(o.state);n&&n!=we&&e.push(n)}),o.cursorWrapper&&e.push(de.create(o.state.doc,[o.cursorWrapper.deco])),yt.from(e)}const dd={childList:!0,characterData:!0,characterDataOldValue:!0,attributes:!0,attributeOldValue:!0,subtree:!0},hd=Ee&&bt<=11;class ud{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}set(e){this.anchorNode=e.anchorNode,this.anchorOffset=e.anchorOffset,this.focusNode=e.focusNode,this.focusOffset=e.focusOffset}clear(){this.anchorNode=this.focusNode=null}eq(e){return e.anchorNode==this.anchorNode&&e.anchorOffset==this.anchorOffset&&e.focusNode==this.focusNode&&e.focusOffset==this.focusOffset}}class pd{constructor(e,t){this.view=e,this.handleDOMChange=t,this.queue=[],this.flushingSoon=-1,this.observer=null,this.currentSelection=new ud,this.onCharData=null,this.suppressingSelectionUpdates=!1,this.observer=window.MutationObserver&&new window.MutationObserver(n=>{for(let r=0;r<n.length;r++)this.queue.push(n[r]);Ee&&bt<=11&&n.some(r=>r.type=="childList"&&r.removedNodes.length||r.type=="characterData"&&r.oldValue.length>r.target.nodeValue.length)?this.flushSoon():this.flush()}),hd&&(this.onCharData=n=>{this.queue.push({target:n.target,type:"characterData",oldValue:n.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this)}flushSoon(){this.flushingSoon<0&&(this.flushingSoon=window.setTimeout(()=>{this.flushingSoon=-1,this.flush()},20))}forceFlush(){this.flushingSoon>-1&&(window.clearTimeout(this.flushingSoon),this.flushingSoon=-1,this.flush())}start(){this.observer&&(this.observer.takeRecords(),this.observer.observe(this.view.dom,dd)),this.onCharData&&this.view.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.connectSelection()}stop(){if(this.observer){let e=this.observer.takeRecords();if(e.length){for(let t=0;t<e.length;t++)this.queue.push(e[t]);window.setTimeout(()=>this.flush(),20)}this.observer.disconnect()}this.onCharData&&this.view.dom.removeEventListener("DOMCharacterDataModified",this.onCharData),this.disconnectSelection()}connectSelection(){this.view.dom.ownerDocument.addEventListener("selectionchange",this.onSelectionChange)}disconnectSelection(){this.view.dom.ownerDocument.removeEventListener("selectionchange",this.onSelectionChange)}suppressSelectionUpdates(){this.suppressingSelectionUpdates=!0,setTimeout(()=>this.suppressingSelectionUpdates=!1,50)}onSelectionChange(){if(Ii(this.view)){if(this.suppressingSelectionUpdates)return at(this.view);if(Ee&&bt<=11&&!this.view.state.selection.empty){let e=this.view.domSelectionRange();if(e.focusNode&&Jt(e.focusNode,e.focusOffset,e.anchorNode,e.anchorOffset))return this.flushSoon()}this.flush()}}setCurSelection(){this.currentSelection.set(this.view.domSelectionRange())}ignoreSelectionChange(e){if(!e.focusNode)return!0;let t,n=new Set;for(let i=e.focusNode;i;i=Dn(i))n.add(i);for(let i=e.anchorNode;i;i=Dn(i))if(n.has(i)){t=i;break}let r=t&&this.view.docView.nearestDesc(t);return r&&r.ignoreMutation({type:"selection",target:t.nodeType==3?t.parentNode:t})?(this.setCurSelection(),!0):void 0}pendingRecords(){if(this.observer)for(let e of this.observer.takeRecords())this.queue.push(e);return this.queue}flush(){let{view:e}=this;if(!e.docView||this.flushingSoon>-1)return;let t=this.pendingRecords();t.length&&(this.queue=[]);let n=e.domSelectionRange(),r=!this.suppressingSelectionUpdates&&!this.currentSelection.eq(n)&&Ii(e)&&!this.ignoreSelectionChange(n),i=-1,s=-1,l=!1,a=[];if(e.editable)for(let d=0;d<t.length;d++){let h=this.registerMutation(t[d],a);h&&(i=i<0?h.from:Math.min(h.from,i),s=s<0?h.to:Math.max(h.to,s),h.typeOver&&(l=!0))}if(Ue&&a.length>1){let d=a.filter(h=>h.nodeName=="BR");if(d.length==2){let h=d[0],u=d[1];h.parentNode&&h.parentNode.parentNode==u.parentNode?u.remove():h.remove()}}let c=null;i<0&&r&&e.input.lastFocus>Date.now()-200&&Math.max(e.input.lastTouch,e.input.lastClick.time)<Date.now()-300&&yr(n)&&(c=Lo(e))&&c.eq(Z.near(e.state.doc.resolve(0),1))?(e.input.lastFocus=0,at(e),this.currentSelection.set(n),e.scrollToSelection()):(i>-1||r)&&(i>-1&&(e.docView.markDirty(i,s),function(d){if(!Wi.has(d)&&(Wi.set(d,null),["normal","nowrap","pre-line"].indexOf(getComputedStyle(d.dom).whiteSpace)!==-1)){if(d.requiresGeckoHackNode=Ue,Ji)return;console.warn("ProseMirror expects the CSS white-space property to be set, preferably to 'pre-wrap'. It is recommended to load style/prosemirror.css from the prosemirror-view package."),Ji=!0}}(e)),this.handleDOMChange(i,s,l,a),e.docView&&e.docView.dirty?e.updateState(e.state):this.currentSelection.eq(n)||at(e),this.currentSelection.set(n))}registerMutation(e,t){if(t.indexOf(e.target)>-1)return null;let n=this.view.docView.nearestDesc(e.target);if(e.type=="attributes"&&(n==this.view.docView||e.attributeName=="contenteditable"||e.attributeName=="style"&&!e.oldValue&&!e.target.getAttribute("style"))||!n||n.ignoreMutation(e))return null;if(e.type=="childList"){for(let c=0;c<e.addedNodes.length;c++)t.push(e.addedNodes[c]);if(n.contentDOM&&n.contentDOM!=n.dom&&!n.contentDOM.contains(e.target))return{from:n.posBefore,to:n.posAfter};let r=e.previousSibling,i=e.nextSibling;if(Ee&&bt<=11&&e.addedNodes.length)for(let c=0;c<e.addedNodes.length;c++){let{previousSibling:d,nextSibling:h}=e.addedNodes[c];(!d||Array.prototype.indexOf.call(e.addedNodes,d)<0)&&(r=d),(!h||Array.prototype.indexOf.call(e.addedNodes,h)<0)&&(i=h)}let s=r&&r.parentNode==e.target?Ce(r)+1:0,l=n.localPosFromDOM(e.target,s,-1),a=i&&i.parentNode==e.target?Ce(i):e.target.childNodes.length;return{from:l,to:n.localPosFromDOM(e.target,a,1)}}return e.type=="attributes"?{from:n.posAtStart-n.border,to:n.posAtEnd+n.border}:{from:n.posAtStart,to:n.posAtEnd,typeOver:e.target.nodeValue==e.oldValue}}}let Wi=new WeakMap,Ji=!1;function Ui(o,e){let t=e.startContainer,n=e.startOffset,r=e.endContainer,i=e.endOffset,s=o.domAtPos(o.state.selection.anchor);return Jt(s.node,s.offset,r,i)&&([t,n,r,i]=[r,i,t,n]),{anchorNode:t,anchorOffset:n,focusNode:r,focusOffset:i}}function fd(o){let e=o.pmViewDesc;if(e)return e.parseRule();if(o.nodeName=="BR"&&o.parentNode){if(Se&&/^(ul|ol)$/i.test(o.parentNode.nodeName)){let t=document.createElement("div");return t.appendChild(document.createElement("li")),{skip:t}}if(o.parentNode.lastChild==o||Se&&/^(tr|table)$/i.test(o.parentNode.nodeName))return{ignore:!0}}else if(o.nodeName=="IMG"&&o.getAttribute("mark-placeholder"))return{ignore:!0};return null}const md=/^(a|abbr|acronym|b|bd[io]|big|br|button|cite|code|data(list)?|del|dfn|em|i|ins|kbd|label|map|mark|meter|output|q|ruby|s|samp|small|span|strong|su[bp]|time|u|tt|var)$/i;function gd(o,e,t,n,r){let i=o.input.compositionPendingChanges||(o.composing?o.input.compositionID:0);if(o.input.compositionPendingChanges=0,e<0){let S=o.input.lastSelectionTime>Date.now()-50?o.input.lastSelectionOrigin:null,A=Lo(o,S);if(A&&!o.state.selection.eq(A)){if(be&&We&&o.input.lastKeyCode===13&&Date.now()-100<o.input.lastKeyCodeTime&&o.someProp("handleKeyDown",H=>H(o,Lt(13,"Enter"))))return;let q=o.state.tr.setSelection(A);S=="pointer"?q.setMeta("pointer",!0):S=="key"&&q.scrollIntoView(),i&&q.setMeta("composition",i),o.dispatch(q)}return}let s=o.state.doc.resolve(e),l=s.sharedDepth(t);e=s.before(l+1),t=o.state.doc.resolve(t).after(l+1);let a,c,d=o.state.selection,h=function(S,A,q){let H,{node:J,fromOffset:z,toOffset:U,from:Q,to:ne}=S.docView.parseRange(A,q),De=S.domSelectionRange(),X=De.anchorNode;if(X&&S.dom.contains(X.nodeType==1?X:X.parentNode)&&(H=[{node:X,offset:De.anchorOffset}],yr(De)||H.push({node:De.focusNode,offset:De.focusOffset})),be&&S.input.lastKeyCode===8)for(let Nt=U;Nt>z;Nt--){let Yt=J.childNodes[Nt-1],br=Yt.pmViewDesc;if(Yt.nodeName=="BR"&&!br){U=Nt;break}if(!br||br.size)break}let Gt=S.state.doc,Zt=S.someProp("domParser")||pn.fromSchema(S.state.schema),Tt=Gt.resolve(Q),Bn=null,Wa=Zt.parse(J,{topNode:Tt.parent,topMatch:Tt.parent.contentMatchAt(Tt.index()),topOpen:!0,from:z,to:U,preserveWhitespace:Tt.parent.type.whitespace!="pre"||"full",findPositions:H,ruleFromNode:fd,context:Tt});if(H&&H[0].pos!=null){let Nt=H[0].pos,Yt=H[1]&&H[1].pos;Yt==null&&(Yt=Nt),Bn={anchor:Nt+Q,head:Yt+Q}}return{doc:Wa,sel:Bn,from:Q,to:ne}}(o,e,t),u=o.state.doc,f=u.slice(h.from,h.to);o.input.lastKeyCode===8&&Date.now()-100<o.input.lastKeyCodeTime?(a=o.state.selection.to,c="end"):(a=o.state.selection.from,c="start"),o.input.lastKeyCode=null;let p=function(S,A,q,H,J){let z=S.findDiffStart(A,q);if(z==null)return null;let{a:U,b:Q}=S.findDiffEnd(A,q+S.size,q+A.size);if(J=="end"&&(H-=U+Math.max(0,z-Math.min(U,Q))-z),U<z&&S.size<A.size){let ne=H<=z&&H>=U?z-H:0;z-=ne,z&&z<A.size&&Zi(A.textBetween(z-1,z+1))&&(z+=ne?1:-1),Q=z+(Q-U),U=z}else if(Q<z){let ne=H<=z&&H>=Q?z-H:0;z-=ne,z&&z<S.size&&Zi(S.textBetween(z-1,z+1))&&(z+=ne?1:-1),U=z+(U-Q),Q=z}return{start:z,endA:U,endB:Q}}(f.content,h.doc.content,h.from,a,c);if((gn&&o.input.lastIOSEnter>Date.now()-225||We)&&r.some(S=>S.nodeType==1&&!md.test(S.nodeName))&&(!p||p.endA>=p.endB)&&o.someProp("handleKeyDown",S=>S(o,Lt(13,"Enter"))))return void(o.input.lastIOSEnter=0);if(!p){if(!(n&&d instanceof G&&!d.empty&&d.$head.sameParent(d.$anchor))||o.composing||h.sel&&h.sel.anchor!=h.sel.head){if(h.sel){let S=Gi(o,o.state.doc,h.sel);if(S&&!S.eq(o.state.selection)){let A=o.state.tr.setSelection(S);i&&A.setMeta("composition",i),o.dispatch(A)}}return}p={start:d.from,endA:d.to,endB:d.to}}o.input.domChangeCount++,o.state.selection.from<o.state.selection.to&&p.start==p.endB&&o.state.selection instanceof G&&(p.start>o.state.selection.from&&p.start<=o.state.selection.from+2&&o.state.selection.from>=h.from?p.start=o.state.selection.from:p.endA<o.state.selection.to&&p.endA>=o.state.selection.to-2&&o.state.selection.to<=h.to&&(p.endB+=o.state.selection.to-p.endA,p.endA=o.state.selection.to)),Ee&&bt<=11&&p.endB==p.start+1&&p.endA==p.start&&p.start>h.from&&h.doc.textBetween(p.start-h.from-1,p.start-h.from+1)=="  "&&(p.start--,p.endA--,p.endB--);let m,g=h.doc.resolveNoCache(p.start-h.from),y=h.doc.resolveNoCache(p.endB-h.from),C=u.resolve(p.start),v=g.sameParent(y)&&g.parent.inlineContent&&C.end()>=p.endA;if((gn&&o.input.lastIOSEnter>Date.now()-225&&(!v||r.some(S=>S.nodeName=="DIV"||S.nodeName=="P"))||!v&&g.pos<h.doc.content.size&&!g.sameParent(y)&&(m=Z.findFrom(h.doc.resolve(g.pos+1),1,!0))&&m.head==y.pos)&&o.someProp("handleKeyDown",S=>S(o,Lt(13,"Enter"))))return void(o.input.lastIOSEnter=0);if(o.state.selection.anchor>p.start&&function(S,A,q,H,J){if(q-A<=J.pos-H.pos||zr(H,!0,!1)<J.pos)return!1;let z=S.resolve(A);if(!H.parent.isTextblock){let Q=z.nodeAfter;return Q!=null&&q==A+Q.nodeSize}if(z.parentOffset<z.parent.content.size||!z.parent.isTextblock)return!1;let U=S.resolve(zr(z,!0,!0));return!(!U.parent.isTextblock||U.pos>q||zr(U,!0,!1)<q)&&H.parent.content.cut(H.parentOffset).eq(U.parent.content)}(u,p.start,p.endA,g,y)&&o.someProp("handleKeyDown",S=>S(o,Lt(8,"Backspace"))))return void(We&&be&&o.domObserver.suppressSelectionUpdates());be&&We&&p.endB==p.start&&(o.input.lastAndroidDelete=Date.now()),We&&!v&&g.start()!=y.start()&&y.parentOffset==0&&g.depth==y.depth&&h.sel&&h.sel.anchor==h.sel.head&&h.sel.head==p.endA&&(p.endB-=2,y=h.doc.resolveNoCache(p.endB-h.from),setTimeout(()=>{o.someProp("handleKeyDown",function(S){return S(o,Lt(13,"Enter"))})},20));let b,k,I,T=p.start,E=p.endA;if(v){if(g.pos==y.pos)Ee&&bt<=11&&g.parentOffset==0&&(o.domObserver.suppressSelectionUpdates(),setTimeout(()=>at(o),20)),b=o.state.tr.delete(T,E),k=u.resolve(p.start).marksAcross(u.resolve(p.endA));else if(p.endA==p.endB&&(I=function(S,A){let q,H,J,z=S.firstChild.marks,U=A.firstChild.marks,Q=z,ne=U;for(let X=0;X<U.length;X++)Q=U[X].removeFromSet(Q);for(let X=0;X<z.length;X++)ne=z[X].removeFromSet(ne);if(Q.length==1&&ne.length==0)H=Q[0],q="add",J=X=>X.mark(H.addToSet(X.marks));else{if(Q.length!=0||ne.length!=1)return null;H=ne[0],q="remove",J=X=>X.mark(H.removeFromSet(X.marks))}let De=[];for(let X=0;X<A.childCount;X++)De.push(J(A.child(X)));if(O.from(De).eq(S))return{mark:H,type:q}}(g.parent.content.cut(g.parentOffset,y.parentOffset),C.parent.content.cut(C.parentOffset,p.endA-C.start()))))b=o.state.tr,I.type=="add"?b.addMark(T,E,I.mark):b.removeMark(T,E,I.mark);else if(g.parent.child(g.index()).isText&&g.index()==y.index()-(y.textOffset?0:1)){let S=g.parent.textBetween(g.parentOffset,y.parentOffset);if(o.someProp("handleTextInput",A=>A(o,T,E,S)))return;b=o.state.tr.insertText(S,T,E)}}if(b||(b=o.state.tr.replace(T,E,h.doc.slice(p.start-h.from,p.endB-h.from))),h.sel){let S=Gi(o,b.doc,h.sel);S&&!(be&&We&&o.composing&&S.empty&&(p.start!=p.endB||o.input.lastAndroidDelete<Date.now()-100)&&(S.head==T||S.head==b.mapping.map(E)-1)||Ee&&S.empty&&S.head==T)&&b.setSelection(S)}k&&b.ensureMarks(k),i&&b.setMeta("composition",i),o.dispatch(b.scrollIntoView())}function Gi(o,e,t){return Math.max(t.anchor,t.head)>e.content.size?null:Ao(o,e.resolve(t.anchor),e.resolve(t.head))}function zr(o,e,t){let n=o.depth,r=e?o.end():o.pos;for(;n>0&&(e||o.indexAfter(n)==o.node(n).childCount);)n--,r++,e=!1;if(t){let i=o.node(n).maybeChild(o.indexAfter(n));for(;i&&!i.isLeaf;)i=i.firstChild,r++}return r}function Zi(o){if(o.length!=2)return!1;let e=o.charCodeAt(0),t=o.charCodeAt(1);return e>=56320&&e<=57343&&t>=55296&&t<=56319}class yd{constructor(e,t){this._root=null,this.focused=!1,this.trackWrites=null,this.mounted=!1,this.markCursor=null,this.cursorWrapper=null,this.lastSelectedViewDesc=void 0,this.input=new nd,this.prevDirectPlugins=[],this.pluginViews=[],this.requiresGeckoHackNode=!1,this.dragging=null,this._props=t,this.state=t.state,this.directPlugins=t.plugins||[],this.directPlugins.forEach(ts),this.dispatch=this.dispatch.bind(this),this.dom=e&&e.mount||document.createElement("div"),e&&(e.appendChild?e.appendChild(this.dom):typeof e=="function"?e(this.dom):e.mount&&(this.mounted=!0)),this.editable=Qi(this),Xi(this),this.nodeViews=es(this),this.docView=Si(this.state.doc,Yi(this),Pr(this),this.dom,this),this.domObserver=new pd(this,(n,r,i,s)=>gd(this,n,r,i,s)),this.domObserver.start(),function(n){for(let r in $e){let i=$e[r];n.dom.addEventListener(r,n.input.eventHandlers[r]=s=>{!rd(n,s)||po(n,s)||!n.editable&&s.type in Me||i(n,s)},td[r]?{passive:!0}:void 0)}Se&&n.dom.addEventListener("input",()=>null),_r(n)}(this),this.updatePluginViews()}get composing(){return this.input.composing}get props(){if(this._props.state!=this.state){let e=this._props;this._props={};for(let t in e)this._props[t]=e[t];this._props.state=this.state}return this._props}update(e){e.handleDOMEvents!=this._props.handleDOMEvents&&_r(this);let t=this._props;this._props=e,e.plugins&&(e.plugins.forEach(ts),this.directPlugins=e.plugins),this.updateStateInner(e.state,t)}setProps(e){let t={};for(let n in this._props)t[n]=this._props[n];t.state=this.state;for(let n in e)t[n]=e[n];this.update(t)}updateState(e){this.updateStateInner(e,this._props)}updateStateInner(e,t){var n;let r=this.state,i=!1,s=!1;e.storedMarks&&this.composing&&(oa(this),s=!0),this.state=e;let l=r.plugins!=e.plugins||this._props.plugins!=t.plugins;if(l||this._props.plugins!=t.plugins||this._props.nodeViews!=t.nodeViews){let f=es(this);(function(p,m){let g=0,y=0;for(let C in p){if(p[C]!=m[C])return!0;g++}for(let C in m)y++;return g!=y})(f,this.nodeViews)&&(this.nodeViews=f,i=!0)}(l||t.handleDOMEvents!=this._props.handleDOMEvents)&&_r(this),this.editable=Qi(this),Xi(this);let a=Pr(this),c=Yi(this),d=r.plugins==e.plugins||r.doc.eq(e.doc)?e.scrollToSelection>r.scrollToSelection?"to selection":"preserve":"reset",h=i||!this.docView.matchesNode(e.doc,c,a);!h&&e.selection.eq(r.selection)||(s=!0);let u=d=="preserve"&&s&&this.dom.style.overflowAnchor==null&&function(f){let p,m,g=f.dom.getBoundingClientRect(),y=Math.max(0,g.top);for(let C=(g.left+g.right)/2,v=y+1;v<Math.min(innerHeight,g.bottom);v+=5){let b=f.root.elementFromPoint(C,v);if(!b||b==f.dom||!f.dom.contains(b))continue;let k=b.getBoundingClientRect();if(k.top>=y-20){p=b,m=k.top;break}}return{refDOM:p,refTop:m,stack:wi(f.dom)}}(this);if(s){this.domObserver.stop();let f=h&&(Ee||be)&&!this.composing&&!r.selection.empty&&!e.selection.empty&&function(p,m){let g=Math.min(p.$anchor.sharedDepth(p.head),m.$anchor.sharedDepth(m.head));return p.$anchor.start(g)!=m.$anchor.start(g)}(r.selection,e.selection);if(h){let p=be?this.trackWrites=this.domSelectionRange().focusNode:null;this.composing&&(this.input.compositionNode=cd(this)),!i&&this.docView.update(e.doc,c,a,this)||(this.docView.updateOuterDeco(c),this.docView.destroy(),this.docView=Si(e.doc,c,a,this.dom,this)),p&&!this.trackWrites&&(f=!0)}f||!(this.input.mouseDown&&this.domObserver.currentSelection.eq(this.domSelectionRange())&&function(p){let m=p.docView.domFromPos(p.state.selection.anchor,0),g=p.domSelectionRange();return Jt(m.node,m.offset,g.anchorNode,g.anchorOffset)}(this))?at(this,f):(Ul(this,e.selection),this.domObserver.setCurSelection()),this.domObserver.start()}this.updatePluginViews(r),!((n=this.dragging)===null||n===void 0)&&n.node&&!r.doc.eq(e.doc)&&this.updateDraggedNode(this.dragging,r),d=="reset"?this.dom.scrollTop=0:d=="to selection"?this.scrollToSelection():u&&function({refDOM:f,refTop:p,stack:m}){let g=f?f.getBoundingClientRect().top:0;vi(m,g==0?0:g-p)}(u)}scrollToSelection(){let e=this.domSelectionRange().focusNode;if(!this.someProp("handleScrollToSelection",t=>t(this)))if(this.state.selection instanceof j){let t=this.docView.domAfterPos(this.state.selection.from);t.nodeType==1&&Ci(this,t.getBoundingClientRect(),e)}else Ci(this,this.coordsAtPos(this.state.selection.head,1),e)}destroyPluginViews(){let e;for(;e=this.pluginViews.pop();)e.destroy&&e.destroy()}updatePluginViews(e){if(e&&e.plugins==this.state.plugins&&this.directPlugins==this.prevDirectPlugins)for(let t=0;t<this.pluginViews.length;t++){let n=this.pluginViews[t];n.update&&n.update(this,e)}else{this.prevDirectPlugins=this.directPlugins,this.destroyPluginViews();for(let t=0;t<this.directPlugins.length;t++){let n=this.directPlugins[t];n.spec.view&&this.pluginViews.push(n.spec.view(this))}for(let t=0;t<this.state.plugins.length;t++){let n=this.state.plugins[t];n.spec.view&&this.pluginViews.push(n.spec.view(this))}}}updateDraggedNode(e,t){let n=e.node,r=-1;if(this.state.doc.nodeAt(n.from)==n.node)r=n.from;else{let i=n.from+(this.state.doc.content.size-t.doc.content.size);(i>0&&this.state.doc.nodeAt(i))==n.node&&(r=i)}this.dragging=new sa(e.slice,e.move,r<0?void 0:j.create(this.state.doc,r))}someProp(e,t){let n,r=this._props&&this._props[e];if(r!=null&&(n=t?t(r):r))return n;for(let s=0;s<this.directPlugins.length;s++){let l=this.directPlugins[s].props[e];if(l!=null&&(n=t?t(l):l))return n}let i=this.state.plugins;if(i)for(let s=0;s<i.length;s++){let l=i[s].props[e];if(l!=null&&(n=t?t(l):l))return n}}hasFocus(){if(Ee){let e=this.root.activeElement;if(e==this.dom)return!0;if(!e||!this.dom.contains(e))return!1;for(;e&&this.dom!=e&&this.dom.contains(e);){if(e.contentEditable=="false")return!1;e=e.parentElement}return!0}return this.root.activeElement==this.dom}focus(){this.domObserver.stop(),this.editable&&function(e){if(e.setActive)return e.setActive();if(Xt)return e.focus(Xt);let t=wi(e);e.focus(Xt==null?{get preventScroll(){return Xt={preventScroll:!0},!0}}:void 0),Xt||(Xt=!1,vi(t,0))}(this.dom),at(this),this.domObserver.start()}get root(){let e=this._root;if(e==null){for(let t=this.dom.parentNode;t;t=t.parentNode)if(t.nodeType==9||t.nodeType==11&&t.host)return t.getSelection||(Object.getPrototypeOf(t).getSelection=()=>t.ownerDocument.getSelection()),this._root=t}return e||document}updateRoot(){this._root=null}posAtCoords(e){return qc(this,e)}coordsAtPos(e,t=1){return Hl(this,e,t)}domAtPos(e,t=0){return this.docView.domFromPos(e,t)}nodeDOM(e){let t=this.docView.descAt(e);return t?t.nodeDOM:null}posAtDOM(e,t,n=-1){let r=this.docView.posFromDOM(e,t,n);if(r==null)throw new RangeError("DOM position not inside the editor");return r}endOfTextblock(e,t){return Jc(this,t||this.state,e)}pasteHTML(e,t){return Tn(this,"",e,!1,t||new ClipboardEvent("paste"))}pasteText(e,t){return Tn(this,e,null,!0,t||new ClipboardEvent("paste"))}destroy(){this.docView&&(function(e){e.domObserver.stop();for(let t in e.input.eventHandlers)e.dom.removeEventListener(t,e.input.eventHandlers[t]);clearTimeout(e.input.composingTimeout),clearTimeout(e.input.lastIOSEnterFallbackTimeout)}(this),this.destroyPluginViews(),this.mounted?(this.docView.update(this.state.doc,[],Pr(this),this),this.dom.textContent=""):this.dom.parentNode&&this.dom.parentNode.removeChild(this.dom),this.docView.destroy(),this.docView=null,ro=null)}get isDestroyed(){return this.docView==null}dispatchEvent(e){return function(t,n){po(t,n)||!$e[n.type]||!t.editable&&n.type in Me||$e[n.type](t,n)}(this,e)}dispatch(e){let t=this._props.dispatchTransaction;t?t.call(this,e):this.updateState(this.state.apply(e))}domSelectionRange(){let e=this.domSelection();return Se&&this.root.nodeType===11&&function(t){let n=t.activeElement;for(;n&&n.shadowRoot;)n=n.shadowRoot.activeElement;return n}(this.dom.ownerDocument)==this.dom&&function(t,n){if(n.getComposedRanges){let s=n.getComposedRanges(t.root)[0];if(s)return Ui(t,s)}let r;function i(s){s.preventDefault(),s.stopImmediatePropagation(),r=s.getTargetRanges()[0]}return t.dom.addEventListener("beforeinput",i,!0),document.execCommand("indent"),t.dom.removeEventListener("beforeinput",i,!0),r?Ui(t,r):null}(this,e)||e}domSelection(){return this.root.getSelection()}}function Yi(o){let e=Object.create(null);return e.class="ProseMirror",e.contenteditable=String(o.editable),o.someProp("attributes",t=>{if(typeof t=="function"&&(t=t(o.state)),t)for(let n in t)n=="class"?e.class+=" "+t[n]:n=="style"?e.style=(e.style?e.style+";":"")+t[n]:e[n]||n=="contenteditable"||n=="nodeName"||(e[n]=String(t[n]))}),e.translate||(e.translate="no"),[Pe.node(0,o.state.doc.content.size,e)]}function Xi(o){if(o.markCursor){let e=document.createElement("img");e.className="ProseMirror-separator",e.setAttribute("mark-placeholder","true"),e.setAttribute("alt",""),o.cursorWrapper={dom:e,deco:Pe.widget(o.state.selection.head,e,{raw:!0,marks:o.markCursor})}}else o.cursorWrapper=null}function Qi(o){return!o.someProp("editable",e=>e(o.state)===!1)}function es(o){let e=Object.create(null);function t(n){for(let r in n)Object.prototype.hasOwnProperty.call(e,r)||(e[r]=n[r])}return o.someProp("nodeViews",t),o.someProp("markViews",t),e}function ts(o){if(o.spec.state||o.spec.filterTransaction||o.spec.appendTransaction)throw new RangeError("Plugins passed directly to the view must not have a state component")}for(var St={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},ir={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},Cd=typeof navigator<"u"&&/Mac/.test(navigator.platform),wd=typeof navigator<"u"&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),ye=0;ye<10;ye++)St[48+ye]=St[96+ye]=String(ye);for(ye=1;ye<=24;ye++)St[ye+111]="F"+ye;for(ye=65;ye<=90;ye++)St[ye]=String.fromCharCode(ye+32),ir[ye]=String.fromCharCode(ye);for(var Fr in St)ir.hasOwnProperty(Fr)||(ir[Fr]=St[Fr]);const vd=typeof navigator<"u"&&/Mac|iP(hone|[oa]d)/.test(navigator.platform);function xd(o){let e,t,n,r,i=o.split(/-(?!$)/),s=i[i.length-1];s=="Space"&&(s=" ");for(let l=0;l<i.length-1;l++){let a=i[l];if(/^(cmd|meta|m)$/i.test(a))r=!0;else if(/^a(lt)?$/i.test(a))e=!0;else if(/^(c|ctrl|control)$/i.test(a))t=!0;else if(/^s(hift)?$/i.test(a))n=!0;else{if(!/^mod$/i.test(a))throw new Error("Unrecognized modifier name: "+a);vd?r=!0:t=!0}}return e&&(s="Alt-"+s),t&&(s="Ctrl-"+s),r&&(s="Meta-"+s),n&&(s="Shift-"+s),s}function Br(o,e,t=!0){return e.altKey&&(o="Alt-"+o),e.ctrlKey&&(o="Ctrl-"+o),e.metaKey&&(o="Meta-"+o),t&&e.shiftKey&&(o="Shift-"+o),o}function da(o){let e=function(t){let n=Object.create(null);for(let r in t)n[xd(r)]=t[r];return n}(o);return function(t,n){let r,i=function(l){var a=!(Cd&&l.metaKey&&l.shiftKey&&!l.ctrlKey&&!l.altKey||wd&&l.shiftKey&&l.key&&l.key.length==1||l.key=="Unidentified")&&l.key||(l.shiftKey?ir:St)[l.keyCode]||l.key||"Unidentified";return a=="Esc"&&(a="Escape"),a=="Del"&&(a="Delete"),a=="Left"&&(a="ArrowLeft"),a=="Up"&&(a="ArrowUp"),a=="Right"&&(a="ArrowRight"),a=="Down"&&(a="ArrowDown"),a}(n),s=e[Br(i,n)];if(s&&s(t.state,t.dispatch,t))return!0;if(i.length==1&&i!=" "){if(n.shiftKey){let l=e[Br(i,n,!1)];if(l&&l(t.state,t.dispatch,t))return!0}if((n.shiftKey||n.altKey||n.metaKey||i.charCodeAt(0)>127)&&(r=St[n.keyCode])&&r!=i){let l=e[Br(r,n)];if(l&&l(t.state,t.dispatch,t))return!0}}return!1}}const mo=(o,e)=>!o.selection.empty&&(e&&e(o.tr.deleteSelection().scrollIntoView()),!0);function ha(o,e){let{$cursor:t}=o.selection;return!t||(e?!e.endOfTextblock("backward",o):t.parentOffset>0)?null:t}const ua=(o,e,t)=>{let n=ha(o,t);if(!n)return!1;let r=_o(n);if(!r){let s=n.blockRange(),l=s&&mn(s);return l!=null&&(e&&e(o.tr.lift(s,l).scrollIntoView()),!0)}let i=r.nodeBefore;if(!i.type.spec.isolating&&va(o,r,e))return!0;if(n.parent.content.size==0&&(yn(i,"end")||j.isSelectable(i))){let s=mr(o.doc,n.before(),n.after(),L.empty);if(s&&s.slice.size<s.to-s.from){if(e){let l=o.tr.step(s);l.setSelection(yn(i,"end")?Z.findFrom(l.doc.resolve(l.mapping.map(r.pos,-1)),-1):j.create(l.doc,r.pos-i.nodeSize)),e(l.scrollIntoView())}return!0}}return!(!i.isAtom||r.depth!=n.depth-1)&&(e&&e(o.tr.delete(r.pos-i.nodeSize,r.pos).scrollIntoView()),!0)};function ns(o,e,t){let n=e.nodeBefore,r=e.pos-1;for(;!n.isTextblock;r--){if(n.type.spec.isolating)return!1;let a=n.lastChild;if(!a)return!1;n=a}let i=e.nodeAfter,s=e.pos+1;for(;!i.isTextblock;s++){if(i.type.spec.isolating)return!1;let a=i.firstChild;if(!a)return!1;i=a}let l=mr(o.doc,r,s,L.empty);if(!l||l.from!=r||l instanceof fe&&l.slice.size>=s-r)return!1;if(t){let a=o.tr.step(l);a.setSelection(G.create(a.doc,r)),t(a.scrollIntoView())}return!0}function yn(o,e,t=!1){for(let n=o;n;n=e=="start"?n.firstChild:n.lastChild){if(n.isTextblock)return!0;if(t&&n.childCount!=1)return!1}return!1}const pa=(o,e,t)=>{let{$head:n,empty:r}=o.selection,i=n;if(!r)return!1;if(n.parent.isTextblock){if(t?!t.endOfTextblock("backward",o):n.parentOffset>0)return!1;i=_o(n)}let s=i&&i.nodeBefore;return!(!s||!j.isSelectable(s))&&(e&&e(o.tr.setSelection(j.create(o.doc,i.pos-s.nodeSize)).scrollIntoView()),!0)};function _o(o){if(!o.parent.type.spec.isolating)for(let e=o.depth-1;e>=0;e--){if(o.index(e)>0)return o.doc.resolve(o.before(e+1));if(o.node(e).type.spec.isolating)break}return null}function fa(o,e){let{$cursor:t}=o.selection;return!t||(e?!e.endOfTextblock("forward",o):t.parentOffset<t.parent.content.size)?null:t}const ma=(o,e,t)=>{let n=fa(o,t);if(!n)return!1;let r=Po(n);if(!r)return!1;let i=r.nodeAfter;if(va(o,r,e))return!0;if(n.parent.content.size==0&&(yn(i,"start")||j.isSelectable(i))){let s=mr(o.doc,n.before(),n.after(),L.empty);if(s&&s.slice.size<s.to-s.from){if(e){let l=o.tr.step(s);l.setSelection(yn(i,"start")?Z.findFrom(l.doc.resolve(l.mapping.map(r.pos)),1):j.create(l.doc,l.mapping.map(r.pos))),e(l.scrollIntoView())}return!0}}return!(!i.isAtom||r.depth!=n.depth-1)&&(e&&e(o.tr.delete(r.pos,r.pos+i.nodeSize).scrollIntoView()),!0)},ga=(o,e,t)=>{let{$head:n,empty:r}=o.selection,i=n;if(!r)return!1;if(n.parent.isTextblock){if(t?!t.endOfTextblock("forward",o):n.parentOffset<n.parent.content.size)return!1;i=Po(n)}let s=i&&i.nodeAfter;return!(!s||!j.isSelectable(s))&&(e&&e(o.tr.setSelection(j.create(o.doc,i.pos)).scrollIntoView()),!0)};function Po(o){if(!o.parent.type.spec.isolating)for(let e=o.depth-1;e>=0;e--){let t=o.node(e);if(o.index(e)+1<t.childCount)return o.doc.resolve(o.after(e+1));if(t.type.spec.isolating)break}return null}const ya=(o,e)=>{let{$head:t,$anchor:n}=o.selection;return!(!t.parent.type.spec.code||!t.sameParent(n))&&(e&&e(o.tr.insertText(`
`).scrollIntoView()),!0)};function zo(o){for(let e=0;e<o.edgeCount;e++){let{type:t}=o.edge(e);if(t.isTextblock&&!t.hasRequiredAttrs())return t}return null}const Ca=(o,e)=>{let t=o.selection,{$from:n,$to:r}=t;if(t instanceof Ve||n.parent.inlineContent||r.parent.inlineContent)return!1;let i=zo(r.parent.contentMatchAt(r.indexAfter()));if(!i||!i.isTextblock)return!1;if(e){let s=(!n.parentOffset&&r.index()<r.parent.childCount?n:r).pos,l=o.tr.insert(s,i.createAndFill());l.setSelection(G.create(l.doc,s+1)),e(l.scrollIntoView())}return!0},wa=(o,e)=>{let{$cursor:t}=o.selection;if(!t||t.parent.content.size)return!1;if(t.depth>1&&t.after()!=t.end(-1)){let i=t.before();if(lt(o.doc,i))return e&&e(o.tr.split(i).scrollIntoView()),!0}let n=t.blockRange(),r=n&&mn(n);return r!=null&&(e&&e(o.tr.lift(n,r).scrollIntoView()),!0)},bd=(o,e)=>{let{$from:t,$to:n}=o.selection;if(o.selection instanceof j&&o.selection.node.isBlock)return!(!t.parentOffset||!lt(o.doc,t.pos)||(e&&e(o.tr.split(t.pos).scrollIntoView()),0));if(!t.parent.isBlock)return!1;if(e){let r=n.parentOffset==n.parent.content.size,i=o.tr;(o.selection instanceof G||o.selection instanceof Ve)&&i.deleteSelection();let s=t.depth==0?null:zo(t.node(-1).contentMatchAt(t.indexAfter(-1))),l=r&&s?[{type:s}]:void 0,a=lt(i.doc,i.mapping.map(t.pos),1,l);if(l||a||!lt(i.doc,i.mapping.map(t.pos),1,s?[{type:s}]:void 0)||(s&&(l=[{type:s}]),a=!0),a&&(i.split(i.mapping.map(t.pos),1,l),!r&&!t.parentOffset&&t.parent.type!=s)){let c=i.mapping.map(t.before()),d=i.doc.resolve(c);s&&t.node(-1).canReplaceWith(d.index(),d.index()+1,s)&&i.setNodeMarkup(i.mapping.map(t.before()),s)}e(i.scrollIntoView())}return!0};function va(o,e,t){let n,r,i=e.nodeBefore,s=e.nodeAfter;if(i.type.spec.isolating||s.type.spec.isolating)return!1;if(function(h,u,f){let p=u.nodeBefore,m=u.nodeAfter,g=u.index();return!(!(p&&m&&p.type.compatibleContent(m.type))||(!p.content.size&&u.parent.canReplace(g-1,g)?(f&&f(h.tr.delete(u.pos-p.nodeSize,u.pos).scrollIntoView()),0):!u.parent.canReplace(g,g+1)||!m.isTextblock&&!Wt(h.doc,u.pos)||(f&&f(h.tr.clearIncompatible(u.pos,p.type,p.contentMatchAt(p.childCount)).join(u.pos).scrollIntoView()),0)))}(o,e,t))return!0;let l=e.parent.canReplace(e.index(),e.index()+1);if(l&&(n=(r=i.contentMatchAt(i.childCount)).findWrapping(s.type))&&r.matchType(n[0]||s.type).validEnd){if(t){let h=e.pos+s.nodeSize,u=O.empty;for(let m=n.length-1;m>=0;m--)u=O.from(n[m].create(null,u));u=O.from(i.copy(u));let f=o.tr.step(new me(e.pos-1,h,e.pos,h,new L(u,1,0),n.length,!0)),p=h+2*n.length;Wt(f.doc,p)&&f.join(p),t(f.scrollIntoView())}return!0}let a=Z.findFrom(e,1),c=a&&a.$from.blockRange(a.$to),d=c&&mn(c);if(d!=null&&d>=e.depth)return t&&t(o.tr.lift(c,d).scrollIntoView()),!0;if(l&&yn(s,"start",!0)&&yn(i,"end")){let h=i,u=[];for(;u.push(h),!h.isTextblock;)h=h.lastChild;let f=s,p=1;for(;!f.isTextblock;f=f.firstChild)p++;if(h.canReplace(h.childCount,h.childCount,f.content)){if(t){let m=O.empty;for(let g=u.length-1;g>=0;g--)m=O.from(u[g].copy(m));t(o.tr.step(new me(e.pos-u.length,e.pos+s.nodeSize,e.pos+p,e.pos+s.nodeSize-p,new L(m,u.length,0),0,!0)).scrollIntoView())}return!0}}return!1}function xa(o){return function(e,t){let n=e.selection,r=o<0?n.$from:n.$to,i=r.depth;for(;r.node(i).isInline;){if(!i)return!1;i--}return!!r.node(i).isTextblock&&(t&&t(e.tr.setSelection(G.create(e.doc,o<0?r.start(i):r.end(i)))),!0)}}const $d=xa(-1),kd=xa(1);function rs(o,e=null){return function(t,n){let r=!1;for(let i=0;i<t.selection.ranges.length&&!r;i++){let{$from:{pos:s},$to:{pos:l}}=t.selection.ranges[i];t.doc.nodesBetween(s,l,(a,c)=>{if(r)return!1;if(a.isTextblock&&!a.hasMarkup(o,e))if(a.type==o)r=!0;else{let d=t.doc.resolve(c),h=d.index();r=d.parent.canReplaceWith(h,h+1,o)}})}if(!r)return!1;if(n){let i=t.tr;for(let s=0;s<t.selection.ranges.length;s++){let{$from:{pos:l},$to:{pos:a}}=t.selection.ranges[s];i.setBlockType(l,a,o,e)}n(i.scrollIntoView())}return!0}}function Vr(...o){return function(e,t,n){for(let r=0;r<o.length;r++)if(o[r](e,t,n))return!0;return!1}}function Md(o,e=null){return function(t,n){let{$from:r,$to:i}=t.selection,s=r.blockRange(i),l=!1,a=s;if(!s)return!1;if(s.depth>=2&&r.node(s.depth-1).type.compatibleContent(o)&&s.startIndex==0){if(r.index(s.depth-1)==0)return!1;let d=t.doc.resolve(s.start-2);a=new er(d,d,s.depth),s.endIndex<s.parent.childCount&&(s=new er(r,t.doc.resolve(i.end(s.depth)),s.depth)),l=!0}let c=Al(a,o,e,s);return!!c&&(n&&n(function(d,h,u,f,p){let m=O.empty;for(let b=u.length-1;b>=0;b--)m=O.from(u[b].type.create(u[b].attrs,m));d.step(new me(h.start-(f?2:0),h.end,h.start,h.end,new L(m,0,0),u.length,!0));let g=0;for(let b=0;b<u.length;b++)u[b].type==p&&(g=b+1);let y=u.length-g,C=h.start+u.length-(f?2:0),v=h.parent;for(let b=h.startIndex,k=h.endIndex,I=!0;b<k;b++,I=!1)!I&&lt(d.doc,C,y)&&(d.split(C,y),C+=2*y),C+=v.child(b).nodeSize;return d}(t.tr,s,c,l,o).scrollIntoView()),!0)}}function Sd(o){return function(e,t){let{$from:n,$to:r}=e.selection,i=n.blockRange(r,s=>s.childCount>0&&s.firstChild.type==o);return!!i&&(!t||(n.node(i.depth-1).type==o?function(s,l,a,c){let d=s.tr,h=c.end,u=c.$to.end(c.depth);h<u&&(d.step(new me(h-1,u,h,u,new L(O.from(a.create(null,c.parent.copy())),1,0),1,!0)),c=new er(d.doc.resolve(c.$from.pos),d.doc.resolve(u),c.depth));const f=mn(c);if(f==null)return!1;d.lift(c,f);let p=d.mapping.map(h,-1)-1;return Wt(d.doc,p)&&d.join(p),l(d.scrollIntoView()),!0}(e,t,o,i):function(s,l,a){let c=s.tr,d=a.parent;for(let v=a.end,b=a.endIndex-1,k=a.startIndex;b>k;b--)v-=d.child(b).nodeSize,c.delete(v-1,v+1);let h=c.doc.resolve(a.start),u=h.nodeAfter;if(c.mapping.map(a.end)!=a.start+h.nodeAfter.nodeSize)return!1;let f=a.startIndex==0,p=a.endIndex==d.childCount,m=h.node(-1),g=h.index(-1);if(!m.canReplace(g+(f?0:1),g+1,u.content.append(p?O.empty:O.from(d))))return!1;let y=h.pos,C=y+u.nodeSize;return c.step(new me(y-(f?1:0),C+(p?1:0),y+1,C-1,new L((f?O.empty:O.from(d.copy(O.empty))).append(p?O.empty:O.from(d.copy(O.empty))),f?0:1,p?0:1),f?0:1)),l(c.scrollIntoView()),!0}(e,t,i)))}}function wr(o){const{state:e,transaction:t}=o;let{selection:n}=t,{doc:r}=t,{storedMarks:i}=t;return{...e,apply:e.apply.bind(e),applyTransaction:e.applyTransaction.bind(e),plugins:e.plugins,schema:e.schema,reconfigure:e.reconfigure.bind(e),toJSON:e.toJSON.bind(e),get storedMarks(){return i},get selection(){return n},get doc(){return r},get tr(){return n=t.selection,r=t.doc,i=t.storedMarks,t}}}Vr(mo,ua,pa),Vr(mo,ma,ga),Vr(ya,Ca,wa,bd),typeof navigator<"u"?/Mac|iP(hone|[oa]d)/.test(navigator.platform):typeof os<"u"&&os.platform&&os.platform();let vr=class{constructor(o){this.editor=o.editor,this.rawCommands=this.editor.extensionManager.commands,this.customState=o.state}get hasCustomState(){return!!this.customState}get state(){return this.customState||this.editor.state}get commands(){const{rawCommands:o,editor:e,state:t}=this,{view:n}=e,{tr:r}=t,i=this.buildProps(r);return Object.fromEntries(Object.entries(o).map(([s,l])=>[s,(...a)=>{const c=l(...a)(i);return r.getMeta("preventDispatch")||this.hasCustomState||n.dispatch(r),c}]))}get chain(){return()=>this.createChain()}get can(){return()=>this.createCan()}createChain(o,e=!0){const{rawCommands:t,editor:n,state:r}=this,{view:i}=n,s=[],l=!!o,a=o||r.tr,c={...Object.fromEntries(Object.entries(t).map(([d,h])=>[d,(...u)=>{const f=this.buildProps(a,e),p=h(...u)(f);return s.push(p),c}])),run:()=>(l||!e||a.getMeta("preventDispatch")||this.hasCustomState||i.dispatch(a),s.every(d=>d===!0))};return c}createCan(o){const{rawCommands:e,state:t}=this,n=!1,r=o||t.tr,i=this.buildProps(r,n);return{...Object.fromEntries(Object.entries(e).map(([l,a])=>[l,(...c)=>a(...c)({...i,dispatch:void 0})])),chain:()=>this.createChain(r,n)}}buildProps(o,e=!0){const{rawCommands:t,editor:n,state:r}=this,{view:i}=n,s={tr:o,editor:n,view:i,state:wr({state:r,transaction:o}),dispatch:e?()=>{}:void 0,chain:()=>this.createChain(o,e),can:()=>this.createCan(o),get commands(){return Object.fromEntries(Object.entries(t).map(([l,a])=>[l,(...c)=>a(...c)(s)]))}};return s}};class Od{constructor(){this.callbacks={}}on(e,t){return this.callbacks[e]||(this.callbacks[e]=[]),this.callbacks[e].push(t),this}emit(e,...t){const n=this.callbacks[e];return n&&n.forEach(r=>r.apply(this,t)),this}off(e,t){const n=this.callbacks[e];return n&&(t?this.callbacks[e]=n.filter(r=>r!==t):delete this.callbacks[e]),this}removeAllListeners(){this.callbacks={}}}function D(o,e,t){return o.config[e]===void 0&&o.parent?D(o.parent,e,t):typeof o.config[e]=="function"?o.config[e].bind({...t,parent:o.parent?D(o.parent,e,t):null}):o.config[e]}function sr(o){return{baseExtensions:o.filter(e=>e.type==="extension"),nodeExtensions:o.filter(e=>e.type==="node"),markExtensions:o.filter(e=>e.type==="mark")}}function is(o){const e=[],{nodeExtensions:t,markExtensions:n}=sr(o),r=[...t,...n],i={default:null,rendered:!0,renderHTML:null,parseHTML:null,keepOnSplit:!0,isRequired:!1};return o.forEach(s=>{const l=D(s,"addGlobalAttributes",{name:s.name,options:s.options,storage:s.storage});l&&l().forEach(a=>{a.types.forEach(c=>{Object.entries(a.attributes).forEach(([d,h])=>{e.push({type:c,name:d,attribute:{...i,...h}})})})})}),r.forEach(s=>{const l={name:s.name,options:s.options,storage:s.storage},a=D(s,"addAttributes",l);if(!a)return;const c=a();Object.entries(c).forEach(([d,h])=>{const u={...i,...h};typeof(u==null?void 0:u.default)=="function"&&(u.default=u.default()),u!=null&&u.isRequired&&(u==null?void 0:u.default)===void 0&&delete u.default,e.push({type:s.name,name:d,attribute:u})})}),e}function ue(o,e){if(typeof o=="string"){if(!e.nodes[o])throw Error(`There is no node type named '${o}'. Maybe you forgot to add the extension?`);return e.nodes[o]}return o}function Pt(...o){return o.filter(e=>!!e).reduce((e,t)=>{const n={...e};return Object.entries(t).forEach(([r,i])=>{if(n[r])if(r==="class"){const s=i?i.split(" "):[],l=n[r]?n[r].split(" "):[],a=s.filter(c=>!l.includes(c));n[r]=[...l,...a].join(" ")}else n[r]=r==="style"?[n[r],i].join("; "):i;else n[r]=i}),n},{})}function Hr(o,e){return e.filter(t=>t.attribute.rendered).map(t=>t.attribute.renderHTML?t.attribute.renderHTML(o.attrs)||{}:{[t.name]:o.attrs[t.name]}).reduce((t,n)=>Pt(t,n),{})}function ba(o){return typeof o=="function"}function Y(o,e=void 0,...t){return ba(o)?e?o.bind(e)(...t):o(...t):o}function ss(o,e){return o.style?o:{...o,getAttrs:t=>{const n=o.getAttrs?o.getAttrs(t):o.attrs;if(n===!1)return!1;const r=e.reduce((i,s)=>{const l=s.attribute.parseHTML?s.attribute.parseHTML(t):function(a){return typeof a!="string"?a:a.match(/^[+-]?(?:\d*\.)?\d+$/)?Number(a):a==="true"||a!=="false"&&a}(t.getAttribute(s.name));return l==null?i:{...i,[s.name]:l}},{});return{...n,...r}}}}function ls(o){return Object.fromEntries(Object.entries(o).filter(([e,t])=>(e!=="attrs"||!function(n={}){return Object.keys(n).length===0&&n.constructor===Object}(t))&&t!=null))}function jr(o,e){return e.nodes[o]||e.marks[o]||null}function as(o,e){return Array.isArray(e)?e.some(t=>(typeof t=="string"?t:t.name)===o.name):e}const Ed=(o,e=500)=>{let t="";const n=o.parentOffset;return o.parent.nodesBetween(Math.max(0,n-e),n,(r,i,s,l)=>{var a,c;const d=((c=(a=r.type.spec).toText)===null||c===void 0?void 0:c.call(a,{node:r,pos:i,parent:s,index:l}))||r.textContent||"%leaf%";t+=d.slice(0,Math.max(0,n-i))}),t};function Fo(o){return Object.prototype.toString.call(o)==="[object RegExp]"}class Td{constructor(e){this.find=e.find,this.handler=e.handler}}const Nd=(o,e)=>{if(Fo(e))return e.exec(o);const t=e(o);if(!t)return null;const n=[t.text];return n.index=t.index,n.input=o,n.data=t.data,t.replaceWith&&(t.text.includes(t.replaceWith)||console.warn('[tiptap warn]: "inputRuleMatch.replaceWith" must be part of "inputRuleMatch.text".'),n.push(t.replaceWith)),n};function Kn(o){var e;const{editor:t,from:n,to:r,text:i,rules:s,plugin:l}=o,{view:a}=t;if(a.composing)return!1;const c=a.state.doc.resolve(n);if(c.parent.type.spec.code||!((e=c.nodeBefore||c.nodeAfter)===null||e===void 0)&&e.marks.find(u=>u.type.spec.code))return!1;let d=!1;const h=Ed(c)+i;return s.forEach(u=>{if(d)return;const f=Nd(h,u.find);if(!f)return;const p=a.state.tr,m=wr({state:a.state,transaction:p}),g={from:n-(f[0].length-i.length),to:r},{commands:y,chain:C,can:v}=new vr({editor:t,state:m});u.handler({state:m,range:g,match:f,commands:y,chain:C,can:v})!==null&&p.steps.length&&(p.setMeta(l,{transform:p,from:n,to:r,text:i}),a.dispatch(p),d=!0)}),d}function Ld(o){const{editor:e,rules:t}=o,n=new Fe({state:{init:()=>null,apply(r,i){const s=r.getMeta(n);if(s)return s;const l=r.getMeta("applyInputRules");return l&&setTimeout(()=>{const{from:a,text:c}=l,d=a+c.length;Kn({editor:e,from:a,to:d,text:c,rules:t,plugin:n})}),r.selectionSet||r.docChanged?null:i}},props:{handleTextInput:(r,i,s,l)=>Kn({editor:e,from:i,to:s,text:l,rules:t,plugin:n}),handleDOMEvents:{compositionend:r=>(setTimeout(()=>{const{$cursor:i}=r.state.selection;i&&Kn({editor:e,from:i.pos,to:i.pos,text:"",rules:t,plugin:n})}),!1)},handleKeyDown(r,i){if(i.key!=="Enter")return!1;const{$cursor:s}=r.state.selection;return!!s&&Kn({editor:e,from:s.pos,to:s.pos,text:`
`,rules:t,plugin:n})}},isInputRules:!0});return n}const Ad=(o,e,t)=>{if(Fo(e))return[...o.matchAll(e)];const n=e(o,t);return n?n.map(r=>{const i=[r.text];return i.index=r.index,i.input=o,i.data=r.data,r.replaceWith&&(r.text.includes(r.replaceWith)||console.warn('[tiptap warn]: "pasteRuleMatch.replaceWith" must be part of "pasteRuleMatch.text".'),i.push(r.replaceWith)),i}):[]};function Id(o){const{editor:e,rules:t}=o;let n=null,r=!1,i=!1,s=typeof ClipboardEvent<"u"?new ClipboardEvent("paste"):null,l=typeof DragEvent<"u"?new DragEvent("drop"):null;const a=({state:d,from:h,to:u,rule:f,pasteEvt:p})=>{const m=d.tr,g=wr({state:d,transaction:m});if(function(C){const{editor:v,state:b,from:k,to:I,rule:T,pasteEvent:E,dropEvent:S}=C,{commands:A,chain:q,can:H}=new vr({editor:v,state:b}),J=[];return b.doc.nodesBetween(k,I,(z,U)=>{if(!z.isTextblock||z.type.spec.code)return;const Q=Math.max(k,U),ne=Math.min(I,U+z.content.size),De=z.textBetween(Q-U,ne-U,void 0,"￼");Ad(De,T.find,E).forEach(X=>{if(X.index===void 0)return;const Gt=Q+X.index+1,Zt=Gt+X[0].length,Tt={from:b.tr.mapping.map(Gt),to:b.tr.mapping.map(Zt)},Bn=T.handler({state:b,range:Tt,match:X,commands:A,chain:q,can:H,pasteEvent:E,dropEvent:S});J.push(Bn)})}),J.every(z=>z!==null)}({editor:e,state:g,from:Math.max(h-1,0),to:u.b-1,rule:f,pasteEvent:p,dropEvent:l})&&m.steps.length)return l=typeof DragEvent<"u"?new DragEvent("drop"):null,s=typeof ClipboardEvent<"u"?new ClipboardEvent("paste"):null,m};return t.map(d=>new Fe({view(h){const u=f=>{var p;n=!((p=h.dom.parentElement)===null||p===void 0)&&p.contains(f.target)?h.dom.parentElement:null};return window.addEventListener("dragstart",u),{destroy(){window.removeEventListener("dragstart",u)}}},props:{handleDOMEvents:{drop:(h,u)=>(i=n===h.dom.parentElement,l=u,!1),paste:(h,u)=>{var f;const p=(f=u.clipboardData)===null||f===void 0?void 0:f.getData("text/html");return s=u,r=!!(p!=null&&p.includes("data-pm-slice")),!1}}},appendTransaction:(h,u,f)=>{const p=h[0],m=p.getMeta("uiEvent")==="paste"&&!r,g=p.getMeta("uiEvent")==="drop"&&!i,y=p.getMeta("applyPasteRules"),C=!!y;if(!m&&!g&&!C)return;if(C){const{from:k,text:I}=y,T=k+I.length,E=(S=>{var A;const q=new ClipboardEvent("paste",{clipboardData:new DataTransfer});return(A=q.clipboardData)===null||A===void 0||A.setData("text/html",S),q})(I);return a({rule:d,state:f,from:k,to:{b:T},pasteEvt:E})}const v=u.doc.content.findDiffStart(f.doc.content),b=u.doc.content.findDiffEnd(f.doc.content);return typeof v=="number"&&b&&v!==b.b?a({rule:d,state:f,from:v,to:b,pasteEvt:s}):void 0}}))}class on{constructor(e,t){this.splittableMarks=[],this.editor=t,this.extensions=on.resolve(e),this.schema=function(n,r){var i;const s=is(n),{nodeExtensions:l,markExtensions:a}=sr(n),c=(i=l.find(u=>D(u,"topNode")))===null||i===void 0?void 0:i.name,d=Object.fromEntries(l.map(u=>{const f=s.filter(v=>v.type===u.name),p={name:u.name,options:u.options,storage:u.storage,editor:r},m=ls({...n.reduce((v,b)=>{const k=D(b,"extendNodeSchema",p);return{...v,...k?k(u):{}}},{}),content:Y(D(u,"content",p)),marks:Y(D(u,"marks",p)),group:Y(D(u,"group",p)),inline:Y(D(u,"inline",p)),atom:Y(D(u,"atom",p)),selectable:Y(D(u,"selectable",p)),draggable:Y(D(u,"draggable",p)),code:Y(D(u,"code",p)),defining:Y(D(u,"defining",p)),isolating:Y(D(u,"isolating",p)),attrs:Object.fromEntries(f.map(v=>{var b;return[v.name,{default:(b=v==null?void 0:v.attribute)===null||b===void 0?void 0:b.default}]}))}),g=Y(D(u,"parseHTML",p));g&&(m.parseDOM=g.map(v=>ss(v,f)));const y=D(u,"renderHTML",p);y&&(m.toDOM=v=>y({node:v,HTMLAttributes:Hr(v,f)}));const C=D(u,"renderText",p);return C&&(m.toText=C),[u.name,m]})),h=Object.fromEntries(a.map(u=>{const f=s.filter(C=>C.type===u.name),p={name:u.name,options:u.options,storage:u.storage,editor:r},m=ls({...n.reduce((C,v)=>{const b=D(v,"extendMarkSchema",p);return{...C,...b?b(u):{}}},{}),inclusive:Y(D(u,"inclusive",p)),excludes:Y(D(u,"excludes",p)),group:Y(D(u,"group",p)),spanning:Y(D(u,"spanning",p)),code:Y(D(u,"code",p)),attrs:Object.fromEntries(f.map(C=>{var v;return[C.name,{default:(v=C==null?void 0:C.attribute)===null||v===void 0?void 0:v.default}]}))}),g=Y(D(u,"parseHTML",p));g&&(m.parseDOM=g.map(C=>ss(C,f)));const y=D(u,"renderHTML",p);return y&&(m.toDOM=C=>y({mark:C,HTMLAttributes:Hr(C,f)})),[u.name,m]}));return new Oc({topNode:c,nodes:d,marks:h})}(this.extensions,t),this.setupExtensions()}static resolve(e){const t=on.sort(on.flatten(e)),n=function(r){const i=r.filter((s,l)=>r.indexOf(s)!==l);return[...new Set(i)]}(t.map(r=>r.name));return n.length&&console.warn(`[tiptap warn]: Duplicate extension names found: [${n.map(r=>`'${r}'`).join(", ")}]. This can lead to issues.`),t}static flatten(e){return e.map(t=>{const n=D(t,"addExtensions",{name:t.name,options:t.options,storage:t.storage});return n?[t,...this.flatten(n())]:t}).flat(10)}static sort(e){return e.sort((t,n)=>{const r=D(t,"priority")||100,i=D(n,"priority")||100;return r>i?-1:r<i?1:0})}get commands(){return this.extensions.reduce((e,t)=>{const n=D(t,"addCommands",{name:t.name,options:t.options,storage:t.storage,editor:this.editor,type:jr(t.name,this.schema)});return n?{...e,...n()}:e},{})}get plugins(){const{editor:e}=this,t=on.sort([...this.extensions].reverse()),n=[],r=[],i=t.map(s=>{const l={name:s.name,options:s.options,storage:s.storage,editor:e,type:jr(s.name,this.schema)},a=[],c=D(s,"addKeyboardShortcuts",l);let d={};if(s.type==="mark"&&s.config.exitable&&(d.ArrowRight=()=>dr.handleExit({editor:e,mark:s})),c){const m=Object.fromEntries(Object.entries(c()).map(([g,y])=>[g,()=>y({editor:e})]));d={...d,...m}}const h=new Fe({props:{handleKeyDown:da(d)}});a.push(h);const u=D(s,"addInputRules",l);as(s,e.options.enableInputRules)&&u&&n.push(...u());const f=D(s,"addPasteRules",l);as(s,e.options.enablePasteRules)&&f&&r.push(...f());const p=D(s,"addProseMirrorPlugins",l);if(p){const m=p();a.push(...m)}return a}).flat();return[Ld({editor:e,rules:n}),...Id({editor:e,rules:r}),...i]}get attributes(){return is(this.extensions)}get nodeViews(){const{editor:e}=this,{nodeExtensions:t}=sr(this.extensions);return Object.fromEntries(t.filter(n=>!!D(n,"addNodeView")).map(n=>{const r=this.attributes.filter(l=>l.type===n.name),i={name:n.name,options:n.options,storage:n.storage,editor:e,type:ue(n.name,this.schema)},s=D(n,"addNodeView",i);return s?[n.name,(l,a,c,d)=>{const h=Hr(l,r);return s()({editor:e,node:l,getPos:c,decorations:d,HTMLAttributes:h,extension:n})}]:[]}))}setupExtensions(){this.extensions.forEach(e=>{var t;this.editor.extensionStorage[e.name]=e.storage;const n={name:e.name,options:e.options,storage:e.storage,editor:this.editor,type:jr(e.name,this.schema)};e.type==="mark"&&((t=Y(D(e,"keepOnSplit",n)))===null||t===void 0||t)&&this.splittableMarks.push(e.name);const r=D(e,"onBeforeCreate",n),i=D(e,"onCreate",n),s=D(e,"onUpdate",n),l=D(e,"onSelectionUpdate",n),a=D(e,"onTransaction",n),c=D(e,"onFocus",n),d=D(e,"onBlur",n),h=D(e,"onDestroy",n);r&&this.editor.on("beforeCreate",r),i&&this.editor.on("create",i),s&&this.editor.on("update",s),l&&this.editor.on("selectionUpdate",l),a&&this.editor.on("transaction",a),c&&this.editor.on("focus",c),d&&this.editor.on("blur",d),h&&this.editor.on("destroy",h)})}}function qr(o){return function(e){return Object.prototype.toString.call(e).slice(8,-1)}(o)==="Object"&&o.constructor===Object&&Object.getPrototypeOf(o)===Object.prototype}function xr(o,e){const t={...o};return qr(o)&&qr(e)&&Object.keys(e).forEach(n=>{qr(e[n])?n in o?t[n]=xr(o[n],e[n]):Object.assign(t,{[n]:e[n]}):Object.assign(t,{[n]:e[n]})}),t}class He{constructor(e={}){this.type="extension",this.name="extension",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=Y(D(this,"addOptions",{name:this.name}))),this.storage=Y(D(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new He(e)}configure(e={}){const t=this.extend();return t.parent=this.parent,t.options=xr(this.options,e),t.storage=Y(D(t,"addStorage",{name:t.name,options:t.options})),t}extend(e={}){const t=new He({...this.config,...e});return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=Y(D(t,"addOptions",{name:t.name})),t.storage=Y(D(t,"addStorage",{name:t.name,options:t.options})),t}}function $a(o,e,t){const{from:n,to:r}=e,{blockSeparator:i=`

`,textSerializers:s={}}=t||{};let l="";return o.nodesBetween(n,r,(a,c,d,h)=>{var u;a.isBlock&&c>n&&(l+=i);const f=s==null?void 0:s[a.type.name];if(f)return d&&(l+=f({node:a,pos:c,parent:d,index:h,range:e})),!1;a.isText&&(l+=(u=a==null?void 0:a.text)===null||u===void 0?void 0:u.slice(Math.max(n,c)-c,r-c))}),l}function ka(o){return Object.fromEntries(Object.entries(o.nodes).filter(([,e])=>e.spec.toText).map(([e,t])=>[e,t.spec.toText]))}const Dd=He.create({name:"clipboardTextSerializer",addOptions:()=>({blockSeparator:void 0}),addProseMirrorPlugins(){return[new Fe({key:new ze("clipboardTextSerializer"),props:{clipboardTextSerializer:()=>{const{editor:o}=this,{state:e,schema:t}=o,{doc:n,selection:r}=e,{ranges:i}=r,s=Math.min(...i.map(c=>c.$from.pos)),l=Math.max(...i.map(c=>c.$to.pos)),a=ka(t);return $a(n,{from:s,to:l},{...this.options.blockSeparator!==void 0?{blockSeparator:this.options.blockSeparator}:{},textSerializers:a})}}})]}});function lr(o,e,t={strict:!0}){const n=Object.keys(e);return!n.length||n.every(r=>t.strict?e[r]===o[r]:Fo(e[r])?e[r].test(o[r]):e[r]===o[r])}function go(o,e,t={}){return o.find(n=>n.type===e&&lr(n.attrs,t))}function Rd(o,e,t={}){return!!go(o,e,t)}function cs(o,e,t={}){if(!o||!e)return;let n=o.parent.childAfter(o.parentOffset);if(o.parentOffset===n.offset&&n.offset!==0&&(n=o.parent.childBefore(o.parentOffset)),!n.node)return;const r=go([...n.node.marks],e,t);if(!r)return;let i=n.index,s=o.start()+n.offset,l=i+1,a=s+n.node.nodeSize;for(go([...n.node.marks],e,t);i>0&&r.isInSet(o.parent.child(i-1).marks);)i-=1,s-=o.parent.child(i).nodeSize;for(;l<o.parent.childCount&&Rd([...o.parent.child(l).marks],e,t);)a+=o.parent.child(l).nodeSize,l+=1;return{from:s,to:a}}function ft(o,e){if(typeof o=="string"){if(!e.marks[o])throw Error(`There is no mark type named '${o}'. Maybe you forgot to add the extension?`);return e.marks[o]}return o}function ds(o){return o instanceof G}function zt(o=0,e=0,t=0){return Math.min(Math.max(o,e),t)}function Ma(o,e=null){if(!e)return null;const t=Z.atStart(o),n=Z.atEnd(o);if(e==="start"||e===!0)return t;if(e==="end")return n;const r=t.from,i=n.to;return e==="all"?G.create(o,zt(0,r,i),zt(o.content.size,r,i)):G.create(o,zt(e,r,i),zt(e,r,i))}function yo(){return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document}const Sa=o=>{const e=o.childNodes;for(let t=e.length-1;t>=0;t-=1){const n=e[t];n.nodeType===3&&n.nodeValue&&/^(\n\s\s|\n)$/.test(n.nodeValue)?o.removeChild(n):n.nodeType===1&&Sa(n)}return o};function hs(o){const e=`<body>${o}</body>`,t=new window.DOMParser().parseFromString(e,"text/html").body;return Sa(t)}function ar(o,e,t){t={slice:!0,parseOptions:{},...t};const n=typeof o=="string";if(typeof o=="object"&&o!==null)try{return Array.isArray(o)&&o.length>0?O.fromArray(o.map(r=>e.nodeFromJSON(r))):e.nodeFromJSON(o)}catch(r){return console.warn("[tiptap warn]: Invalid content.","Passed value:",o,"Error:",r),ar("",e,t)}if(n){const r=pn.fromSchema(e);return t.slice?r.parseSlice(hs(o),t.parseOptions).content:r.parse(hs(o),t.parseOptions)}return ar("",e,t)}function Oa(){return typeof navigator<"u"&&/Mac/.test(navigator.platform)}function Nn(o,e,t={}){const{from:n,to:r,empty:i}=o.selection,s=e?ue(e,o.schema):null,l=[];o.doc.nodesBetween(n,r,(d,h)=>{if(d.isText)return;const u=Math.max(n,h),f=Math.min(r,h+d.nodeSize);l.push({node:d,from:u,to:f})});const a=r-n,c=l.filter(d=>!s||s.name===d.node.type.name).filter(d=>lr(d.node.attrs,t,{strict:!1}));return i?!!c.length:c.reduce((d,h)=>d+h.to-h.from,0)>=a}function cr(o,e){return e.nodes[o]?"node":e.marks[o]?"mark":null}function us(o,e){const t=typeof e=="string"?[e]:e;return Object.keys(o).reduce((n,r)=>(t.includes(r)||(n[r]=o[r]),n),{})}function Ea(o,e,t={}){return ar(o,e,{slice:!1,parseOptions:t})}function Ta(o,e){const t=ft(e,o.schema),{from:n,to:r,empty:i}=o.selection,s=[];i?(o.storedMarks&&s.push(...o.storedMarks),s.push(...o.selection.$head.marks())):o.doc.nodesBetween(n,r,a=>{s.push(...a.marks)});const l=s.find(a=>a.type.name===t.name);return l?{...l.attrs}:{}}function Bo(o){return e=>function(t,n){for(let r=t.depth;r>0;r-=1){const i=t.node(r);if(n(i))return{pos:r>0?t.before(r):0,start:t.start(r),depth:r,node:i}}}(e.$from,o)}function _d(o,e){const t=cr(typeof e=="string"?e:e.name,o.schema);return t==="node"?function(n,r){const i=ue(r,n.schema),{from:s,to:l}=n.selection,a=[];n.doc.nodesBetween(s,l,d=>{a.push(d)});const c=a.reverse().find(d=>d.type.name===i.name);return c?{...c.attrs}:{}}(o,e):t==="mark"?Ta(o,e):{}}function Wn(o,e,t){return Object.fromEntries(Object.entries(t).filter(([n])=>{const r=o.find(i=>i.type===e&&i.name===n);return!!r&&r.attribute.keepOnSplit}))}function Co(o,e,t={}){const{empty:n,ranges:r}=o.selection,i=e?ft(e,o.schema):null;if(n)return!!(o.storedMarks||o.selection.$from.marks()).filter(d=>!i||i.name===d.type.name).find(d=>lr(d.attrs,t,{strict:!1}));let s=0;const l=[];if(r.forEach(({$from:d,$to:h})=>{const u=d.pos,f=h.pos;o.doc.nodesBetween(u,f,(p,m)=>{if(!p.isText&&!p.marks.length)return;const g=Math.max(u,m),y=Math.min(f,m+p.nodeSize);s+=y-g,l.push(...p.marks.map(C=>({mark:C,from:g,to:y})))})}),s===0)return!1;const a=l.filter(d=>!i||i.name===d.mark.type.name).filter(d=>lr(d.mark.attrs,t,{strict:!1})).reduce((d,h)=>d+h.to-h.from,0),c=l.filter(d=>!i||d.mark.type!==i&&d.mark.type.excludes(i)).reduce((d,h)=>d+h.to-h.from,0);return(a>0?a+c:a)>=s}function ps(o,e){const{nodeExtensions:t}=sr(e),n=t.find(i=>i.name===o);if(!n)return!1;const r=Y(D(n,"group",{name:n.name,options:n.options,storage:n.storage}));return typeof r=="string"&&r.split(" ").includes("list")}function fs(o,e){const t=o.storedMarks||o.selection.$to.parentOffset&&o.selection.$from.marks();if(t){const n=t.filter(r=>e==null?void 0:e.includes(r.type.name));o.tr.ensureMarks(n)}}const Kr=(o,e)=>{const t=Bo(i=>i.type===e)(o.selection);if(!t)return!0;const n=o.doc.resolve(Math.max(0,t.pos-1)).before(t.depth);if(n===void 0)return!0;const r=o.doc.nodeAt(n);return t.node.type!==(r==null?void 0:r.type)||!Wt(o.doc,t.pos)||(o.join(t.pos),!0)},Wr=(o,e)=>{const t=Bo(i=>i.type===e)(o.selection);if(!t)return!0;const n=o.doc.resolve(t.start).after(t.depth);if(n===void 0)return!0;const r=o.doc.nodeAt(n);return t.node.type!==(r==null?void 0:r.type)||!Wt(o.doc,n)||(o.join(n),!0)};var Pd=Object.freeze({__proto__:null,blur:()=>({editor:o,view:e})=>(requestAnimationFrame(()=>{var t;o.isDestroyed||(e.dom.blur(),(t=window==null?void 0:window.getSelection())===null||t===void 0||t.removeAllRanges())}),!0),clearContent:(o=!1)=>({commands:e})=>e.setContent("",o),clearNodes:()=>({state:o,tr:e,dispatch:t})=>{const{selection:n}=e,{ranges:r}=n;return!t||(r.forEach(({$from:i,$to:s})=>{o.doc.nodesBetween(i.pos,s.pos,(l,a)=>{if(l.type.isText)return;const{doc:c,mapping:d}=e,h=c.resolve(d.map(a)),u=c.resolve(d.map(a+l.nodeSize)),f=h.blockRange(u);if(!f)return;const p=mn(f);if(l.type.isTextblock){const{defaultType:m}=h.parent.contentMatchAt(h.index());e.setNodeMarkup(f.start,m)}(p||p===0)&&e.lift(f,p)})}),!0)},command:o=>e=>o(e),createParagraphNear:()=>({state:o,dispatch:e})=>Ca(o,e),cut:(o,e)=>({editor:t,tr:n})=>{const{state:r}=t,i=r.doc.slice(o.from,o.to);n.deleteRange(o.from,o.to);const s=n.mapping.map(e);return n.insert(s,i.content),n.setSelection(new G(n.doc.resolve(s-1))),!0},deleteCurrentNode:()=>({tr:o,dispatch:e})=>{const{selection:t}=o,n=t.$anchor.node();if(n.content.size>0)return!1;const r=o.selection.$anchor;for(let i=r.depth;i>0;i-=1)if(r.node(i).type===n.type){if(e){const s=r.before(i),l=r.after(i);o.delete(s,l).scrollIntoView()}return!0}return!1},deleteNode:o=>({tr:e,state:t,dispatch:n})=>{const r=ue(o,t.schema),i=e.selection.$anchor;for(let s=i.depth;s>0;s-=1)if(i.node(s).type===r){if(n){const l=i.before(s),a=i.after(s);e.delete(l,a).scrollIntoView()}return!0}return!1},deleteRange:o=>({tr:e,dispatch:t})=>{const{from:n,to:r}=o;return t&&e.delete(n,r),!0},deleteSelection:()=>({state:o,dispatch:e})=>mo(o,e),enter:()=>({commands:o})=>o.keyboardShortcut("Enter"),exitCode:()=>({state:o,dispatch:e})=>((t,n)=>{let{$head:r,$anchor:i}=t.selection;if(!r.parent.type.spec.code||!r.sameParent(i))return!1;let s=r.node(-1),l=r.indexAfter(-1),a=zo(s.contentMatchAt(l));if(!a||!s.canReplaceWith(l,l,a))return!1;if(n){let c=r.after(),d=t.tr.replaceWith(c,c,a.createAndFill());d.setSelection(Z.near(d.doc.resolve(c),1)),n(d.scrollIntoView())}return!0})(o,e),extendMarkRange:(o,e={})=>({tr:t,state:n,dispatch:r})=>{const i=ft(o,n.schema),{doc:s,selection:l}=t,{$from:a,from:c,to:d}=l;if(r){const h=cs(a,i,e);if(h&&h.from<=c&&h.to>=d){const u=G.create(s,h.from,h.to);t.setSelection(u)}}return!0},first:o=>e=>{const t=typeof o=="function"?o(e):o;for(let n=0;n<t.length;n+=1)if(t[n](e))return!0;return!1},focus:(o=null,e={})=>({editor:t,view:n,tr:r,dispatch:i})=>{e={scrollIntoView:!0,...e};const s=()=>{yo()&&n.dom.focus(),requestAnimationFrame(()=>{t.isDestroyed||(n.focus(),e!=null&&e.scrollIntoView&&t.commands.scrollIntoView())})};if(n.hasFocus()&&o===null||o===!1)return!0;if(i&&o===null&&!ds(t.state.selection))return s(),!0;const l=Ma(r.doc,o)||t.state.selection,a=t.state.selection.eq(l);return i&&(a||r.setSelection(l),a&&r.storedMarks&&r.setStoredMarks(r.storedMarks),s()),!0},forEach:(o,e)=>t=>o.every((n,r)=>e(n,{...t,index:r})),insertContent:(o,e)=>({tr:t,commands:n})=>n.insertContentAt({from:t.selection.from,to:t.selection.to},o,e),insertContentAt:(o,e,t)=>({tr:n,dispatch:r,editor:i})=>{if(r){t={parseOptions:{},updateSelection:!0,applyInputRules:!1,applyPasteRules:!1,...t};const s=ar(e,i.schema,{parseOptions:{preserveWhitespace:"full",...t.parseOptions}});if(s.toString()==="<>")return!0;let{from:l,to:a}=typeof o=="number"?{from:o,to:o}:{from:o.from,to:o.to},c=!0,d=!0;if((s.toString().startsWith("<")?s:[s]).forEach(u=>{u.check(),c=!!c&&u.isText&&u.marks.length===0,d=!!d&&u.isBlock}),l===a&&d){const{parent:u}=n.doc.resolve(l);u.isTextblock&&!u.type.spec.code&&!u.childCount&&(l-=1,a+=1)}let h;c?(h=Array.isArray(e)?e.map(u=>u.text||"").join(""):typeof e=="object"&&e&&e.text?e.text:e,n.insertText(h,l,a)):(h=s,n.replaceWith(l,a,h)),t.updateSelection&&function(u,f,p){const m=u.steps.length-1;if(m<f)return;const g=u.steps[m];if(!(g instanceof fe||g instanceof me))return;const y=u.mapping.maps[m];let C=0;y.forEach((v,b,k,I)=>{C===0&&(C=I)}),u.setSelection(Z.near(u.doc.resolve(C),p))}(n,n.steps.length-1,-1),t.applyInputRules&&n.setMeta("applyInputRules",{from:l,text:h}),t.applyPasteRules&&n.setMeta("applyPasteRules",{from:l,text:h})}return!0},joinUp:()=>({state:o,dispatch:e})=>((t,n)=>{let r,i=t.selection,s=i instanceof j;if(s){if(i.node.isTextblock||!Wt(t.doc,i.from))return!1;r=i.from}else if(r=jn(t.doc,i.from,-1),r==null)return!1;if(n){let l=t.tr.join(r);s&&l.setSelection(j.create(l.doc,r-t.doc.resolve(r).nodeBefore.nodeSize)),n(l.scrollIntoView())}return!0})(o,e),joinDown:()=>({state:o,dispatch:e})=>((t,n)=>{let r,i=t.selection;if(i instanceof j){if(i.node.isTextblock||!Wt(t.doc,i.to))return!1;r=i.to}else if(r=jn(t.doc,i.to,1),r==null)return!1;return n&&n(t.tr.join(r).scrollIntoView()),!0})(o,e),joinBackward:()=>({state:o,dispatch:e})=>ua(o,e),joinForward:()=>({state:o,dispatch:e})=>ma(o,e),joinItemBackward:()=>({tr:o,state:e,dispatch:t})=>{try{const n=jn(e.doc,e.selection.$from.pos,-1);return n!=null&&(o.join(n,2),t&&t(o),!0)}catch{return!1}},joinItemForward:()=>({state:o,dispatch:e,tr:t})=>{try{const n=jn(o.doc,o.selection.$from.pos,1);return n!=null&&(t.join(n,2),e&&e(t),!0)}catch{return!1}},joinTextblockBackward:()=>({state:o,dispatch:e})=>((t,n,r)=>{let i=ha(t,r);if(!i)return!1;let s=_o(i);return!!s&&ns(t,s,n)})(o,e),joinTextblockForward:()=>({state:o,dispatch:e})=>((t,n,r)=>{let i=fa(t,r);if(!i)return!1;let s=Po(i);return!!s&&ns(t,s,n)})(o,e),keyboardShortcut:o=>({editor:e,view:t,tr:n,dispatch:r})=>{const i=function(c){const d=c.split(/-(?!$)/);let h,u,f,p,m=d[d.length-1];m==="Space"&&(m=" ");for(let g=0;g<d.length-1;g+=1){const y=d[g];if(/^(cmd|meta|m)$/i.test(y))p=!0;else if(/^a(lt)?$/i.test(y))h=!0;else if(/^(c|ctrl|control)$/i.test(y))u=!0;else if(/^s(hift)?$/i.test(y))f=!0;else{if(!/^mod$/i.test(y))throw new Error(`Unrecognized modifier name: ${y}`);yo()||Oa()?p=!0:u=!0}}return h&&(m=`Alt-${m}`),u&&(m=`Ctrl-${m}`),p&&(m=`Meta-${m}`),f&&(m=`Shift-${m}`),m}(o).split(/-(?!$)/),s=i.find(c=>!["Alt","Ctrl","Meta","Shift"].includes(c)),l=new KeyboardEvent("keydown",{key:s==="Space"?" ":s,altKey:i.includes("Alt"),ctrlKey:i.includes("Ctrl"),metaKey:i.includes("Meta"),shiftKey:i.includes("Shift"),bubbles:!0,cancelable:!0}),a=e.captureTransaction(()=>{t.someProp("handleKeyDown",c=>c(t,l))});return a==null||a.steps.forEach(c=>{const d=c.map(n.mapping);d&&r&&n.maybeStep(d)}),!0},lift:(o,e={})=>({state:t,dispatch:n})=>!!Nn(t,ue(o,t.schema),e)&&((r,i)=>{let{$from:s,$to:l}=r.selection,a=s.blockRange(l),c=a&&mn(a);return c!=null&&(i&&i(r.tr.lift(a,c).scrollIntoView()),!0)})(t,n),liftEmptyBlock:()=>({state:o,dispatch:e})=>wa(o,e),liftListItem:o=>({state:e,dispatch:t})=>Sd(ue(o,e.schema))(e,t),newlineInCode:()=>({state:o,dispatch:e})=>ya(o,e),resetAttributes:(o,e)=>({tr:t,state:n,dispatch:r})=>{let i=null,s=null;const l=cr(typeof o=="string"?o:o.name,n.schema);return!!l&&(l==="node"&&(i=ue(o,n.schema)),l==="mark"&&(s=ft(o,n.schema)),r&&t.selection.ranges.forEach(a=>{n.doc.nodesBetween(a.$from.pos,a.$to.pos,(c,d)=>{i&&i===c.type&&t.setNodeMarkup(d,void 0,us(c.attrs,e)),s&&c.marks.length&&c.marks.forEach(h=>{s===h.type&&t.addMark(d,d+c.nodeSize,s.create(us(h.attrs,e)))})})}),!0)},scrollIntoView:()=>({tr:o,dispatch:e})=>(e&&o.scrollIntoView(),!0),selectAll:()=>({tr:o,commands:e})=>e.setTextSelection({from:0,to:o.doc.content.size}),selectNodeBackward:()=>({state:o,dispatch:e})=>pa(o,e),selectNodeForward:()=>({state:o,dispatch:e})=>ga(o,e),selectParentNode:()=>({state:o,dispatch:e})=>((t,n)=>{let r,{$from:i,to:s}=t.selection,l=i.sharedDepth(s);return l!=0&&(r=i.before(l),n&&n(t.tr.setSelection(j.create(t.doc,r))),!0)})(o,e),selectTextblockEnd:()=>({state:o,dispatch:e})=>kd(o,e),selectTextblockStart:()=>({state:o,dispatch:e})=>$d(o,e),setContent:(o,e=!1,t={})=>({tr:n,editor:r,dispatch:i})=>{const{doc:s}=n,l=Ea(o,r.schema,t);return i&&n.replaceWith(0,s.content.size,l).setMeta("preventUpdate",!e),!0},setMark:(o,e={})=>({tr:t,state:n,dispatch:r})=>{const{selection:i}=t,{empty:s,ranges:l}=i,a=ft(o,n.schema);if(r)if(s){const c=Ta(n,a);t.addStoredMark(a.create({...c,...e}))}else l.forEach(c=>{const d=c.$from.pos,h=c.$to.pos;n.doc.nodesBetween(d,h,(u,f)=>{const p=Math.max(f,d),m=Math.min(f+u.nodeSize,h);u.marks.find(g=>g.type===a)?u.marks.forEach(g=>{a===g.type&&t.addMark(p,m,a.create({...g.attrs,...e}))}):t.addMark(p,m,a.create(e))})});return function(c,d,h){var u;const{selection:f}=d;let p=null;if(ds(f)&&(p=f.$cursor),p){const g=(u=c.storedMarks)!==null&&u!==void 0?u:p.marks();return!!h.isInSet(g)||!g.some(y=>y.type.excludes(h))}const{ranges:m}=f;return m.some(({$from:g,$to:y})=>{let C=g.depth===0&&c.doc.inlineContent&&c.doc.type.allowsMarkType(h);return c.doc.nodesBetween(g.pos,y.pos,(v,b,k)=>{if(C)return!1;if(v.isInline){const I=!k||k.type.allowsMarkType(h),T=!!h.isInSet(v.marks)||!v.marks.some(E=>E.type.excludes(h));C=I&&T}return!C}),C})}(n,t,a)},setMeta:(o,e)=>({tr:t})=>(t.setMeta(o,e),!0),setNode:(o,e={})=>({state:t,dispatch:n,chain:r})=>{const i=ue(o,t.schema);return i.isTextblock?r().command(({commands:s})=>!!rs(i,e)(t)||s.clearNodes()).command(({state:s})=>rs(i,e)(s,n)).run():(console.warn('[tiptap warn]: Currently "setNode()" only supports text block nodes.'),!1)},setNodeSelection:o=>({tr:e,dispatch:t})=>{if(t){const{doc:n}=e,r=zt(o,0,n.content.size),i=j.create(n,r);e.setSelection(i)}return!0},setTextSelection:o=>({tr:e,dispatch:t})=>{if(t){const{doc:n}=e,{from:r,to:i}=typeof o=="number"?{from:o,to:o}:o,s=G.atStart(n).from,l=G.atEnd(n).to,a=zt(r,s,l),c=zt(i,s,l),d=G.create(n,a,c);e.setSelection(d)}return!0},sinkListItem:o=>({state:e,dispatch:t})=>{return(r=ue(o,e.schema),function(i,s){let{$from:l,$to:a}=i.selection,c=l.blockRange(a,f=>f.childCount>0&&f.firstChild.type==r);if(!c)return!1;let d=c.startIndex;if(d==0)return!1;let h=c.parent,u=h.child(d-1);if(u.type!=r)return!1;if(s){let f=u.lastChild&&u.lastChild.type==h.type,p=O.from(f?r.create():null),m=new L(O.from(r.create(null,O.from(h.type.create(null,p)))),f?3:1,0),g=c.start,y=c.end;s(i.tr.step(new me(g-(f?3:1),y,g,y,m,1,!0)).scrollIntoView())}return!0})(e,t);var r},splitBlock:({keepMarks:o=!0}={})=>({tr:e,state:t,dispatch:n,editor:r})=>{const{selection:i,doc:s}=e,{$from:l,$to:a}=i,c=Wn(r.extensionManager.attributes,l.node().type.name,l.node().attrs);if(i instanceof j&&i.node.isBlock)return!(!l.parentOffset||!lt(s,l.pos))&&(n&&(o&&fs(t,r.extensionManager.splittableMarks),e.split(l.pos).scrollIntoView()),!0);if(!l.parent.isBlock)return!1;if(n){const d=a.parentOffset===a.parent.content.size;i instanceof G&&e.deleteSelection();const h=l.depth===0?void 0:function(p){for(let m=0;m<p.edgeCount;m+=1){const{type:g}=p.edge(m);if(g.isTextblock&&!g.hasRequiredAttrs())return g}return null}(l.node(-1).contentMatchAt(l.indexAfter(-1)));let u=d&&h?[{type:h,attrs:c}]:void 0,f=lt(e.doc,e.mapping.map(l.pos),1,u);if(u||f||!lt(e.doc,e.mapping.map(l.pos),1,h?[{type:h}]:void 0)||(f=!0,u=h?[{type:h,attrs:c}]:void 0),f&&(e.split(e.mapping.map(l.pos),1,u),h&&!d&&!l.parentOffset&&l.parent.type!==h)){const p=e.mapping.map(l.before()),m=e.doc.resolve(p);l.node(-1).canReplaceWith(m.index(),m.index()+1,h)&&e.setNodeMarkup(e.mapping.map(l.before()),h)}o&&fs(t,r.extensionManager.splittableMarks),e.scrollIntoView()}return!0},splitListItem:o=>({tr:e,state:t,dispatch:n,editor:r})=>{var i;const s=ue(o,t.schema),{$from:l,$to:a}=t.selection,c=t.selection.node;if(c&&c.isBlock||l.depth<2||!l.sameParent(a))return!1;const d=l.node(-1);if(d.type!==s)return!1;const h=r.extensionManager.attributes;if(l.parent.content.size===0&&l.node(-1).childCount===l.indexAfter(-1)){if(l.depth===2||l.node(-3).type!==s||l.index(-2)!==l.node(-2).childCount-1)return!1;if(n){let g=O.empty;const y=l.index(-1)?1:l.index(-2)?2:3;for(let T=l.depth-y;T>=l.depth-3;T-=1)g=O.from(l.node(T).copy(g));const C=l.indexAfter(-1)<l.node(-2).childCount?1:l.indexAfter(-2)<l.node(-3).childCount?2:3,v=Wn(h,l.node().type.name,l.node().attrs),b=((i=s.contentMatch.defaultType)===null||i===void 0?void 0:i.createAndFill(v))||void 0;g=g.append(O.from(s.createAndFill(null,b)||void 0));const k=l.before(l.depth-(y-1));e.replace(k,l.after(-C),new L(g,4-y,0));let I=-1;e.doc.nodesBetween(k,e.doc.content.size,(T,E)=>{if(I>-1)return!1;T.isTextblock&&T.content.size===0&&(I=E+1)}),I>-1&&e.setSelection(G.near(e.doc.resolve(I))),e.scrollIntoView()}return!0}const u=a.pos===l.end()?d.contentMatchAt(0).defaultType:null,f=Wn(h,d.type.name,d.attrs),p=Wn(h,l.node().type.name,l.node().attrs);e.delete(l.pos,a.pos);const m=u?[{type:s,attrs:f},{type:u,attrs:p}]:[{type:s,attrs:f}];if(!lt(e.doc,l.pos,2))return!1;if(n){const{selection:g,storedMarks:y}=t,{splittableMarks:C}=r.extensionManager,v=y||g.$to.parentOffset&&g.$from.marks();if(e.split(l.pos,2,m).scrollIntoView(),!v||!n)return!0;const b=v.filter(k=>C.includes(k.type.name));e.ensureMarks(b)}return!0},toggleList:(o,e,t,n={})=>({editor:r,tr:i,state:s,dispatch:l,chain:a,commands:c,can:d})=>{const{extensions:h,splittableMarks:u}=r.extensionManager,f=ue(o,s.schema),p=ue(e,s.schema),{selection:m,storedMarks:g}=s,{$from:y,$to:C}=m,v=y.blockRange(C),b=g||m.$to.parentOffset&&m.$from.marks();if(!v)return!1;const k=Bo(I=>ps(I.type.name,h))(m);if(v.depth>=1&&k&&v.depth-k.depth<=1){if(k.node.type===f)return c.liftListItem(p);if(ps(k.node.type.name,h)&&f.validContent(k.node.content)&&l)return a().command(()=>(i.setNodeMarkup(k.pos,f),!0)).command(()=>Kr(i,f)).command(()=>Wr(i,f)).run()}return t&&b&&l?a().command(()=>{const I=d().wrapInList(f,n),T=b.filter(E=>u.includes(E.type.name));return i.ensureMarks(T),!!I||c.clearNodes()}).wrapInList(f,n).command(()=>Kr(i,f)).command(()=>Wr(i,f)).run():a().command(()=>!!d().wrapInList(f,n)||c.clearNodes()).wrapInList(f,n).command(()=>Kr(i,f)).command(()=>Wr(i,f)).run()},toggleMark:(o,e={},t={})=>({state:n,commands:r})=>{const{extendEmptyMarkRange:i=!1}=t,s=ft(o,n.schema);return Co(n,s,e)?r.unsetMark(s,{extendEmptyMarkRange:i}):r.setMark(s,e)},toggleNode:(o,e,t={})=>({state:n,commands:r})=>{const i=ue(o,n.schema),s=ue(e,n.schema);return Nn(n,i,t)?r.setNode(s):r.setNode(i,t)},toggleWrap:(o,e={})=>({state:t,commands:n})=>{const r=ue(o,t.schema);return Nn(t,r,e)?n.lift(r):n.wrapIn(r,e)},undoInputRule:()=>({state:o,dispatch:e})=>{const t=o.plugins;for(let n=0;n<t.length;n+=1){const r=t[n];let i;if(r.spec.isInputRules&&(i=r.getState(o))){if(e){const s=o.tr,l=i.transform;for(let a=l.steps.length-1;a>=0;a-=1)s.step(l.steps[a].invert(l.docs[a]));if(i.text){const a=s.doc.resolve(i.from).marks();s.replaceWith(i.from,i.to,o.schema.text(i.text,a))}else s.delete(i.from,i.to)}return!0}}return!1},unsetAllMarks:()=>({tr:o,dispatch:e})=>{const{selection:t}=o,{empty:n,ranges:r}=t;return n||e&&r.forEach(i=>{o.removeMark(i.$from.pos,i.$to.pos)}),!0},unsetMark:(o,e={})=>({tr:t,state:n,dispatch:r})=>{var i;const{extendEmptyMarkRange:s=!1}=e,{selection:l}=t,a=ft(o,n.schema),{$from:c,empty:d,ranges:h}=l;if(!r)return!0;if(d&&s){let{from:u,to:f}=l;const p=(i=c.marks().find(g=>g.type===a))===null||i===void 0?void 0:i.attrs,m=cs(c,a,p);m&&(u=m.from,f=m.to),t.removeMark(u,f,a)}else h.forEach(u=>{t.removeMark(u.$from.pos,u.$to.pos,a)});return t.removeStoredMark(a),!0},updateAttributes:(o,e={})=>({tr:t,state:n,dispatch:r})=>{let i=null,s=null;const l=cr(typeof o=="string"?o:o.name,n.schema);return!!l&&(l==="node"&&(i=ue(o,n.schema)),l==="mark"&&(s=ft(o,n.schema)),r&&t.selection.ranges.forEach(a=>{const c=a.$from.pos,d=a.$to.pos;n.doc.nodesBetween(c,d,(h,u)=>{i&&i===h.type&&t.setNodeMarkup(u,void 0,{...h.attrs,...e}),s&&h.marks.length&&h.marks.forEach(f=>{if(s===f.type){const p=Math.max(u,c),m=Math.min(u+h.nodeSize,d);t.addMark(p,m,s.create({...f.attrs,...e}))}})})}),!0)},wrapIn:(o,e={})=>({state:t,dispatch:n})=>function(r,i=null){return function(s,l){let{$from:a,$to:c}=s.selection,d=a.blockRange(c),h=d&&Al(d,r,i);return!!h&&(l&&l(s.tr.wrap(d,h).scrollIntoView()),!0)}}(ue(o,t.schema),e)(t,n),wrapInList:(o,e={})=>({state:t,dispatch:n})=>Md(ue(o,t.schema),e)(t,n)});const zd=He.create({name:"commands",addCommands:()=>({...Pd})}),Fd=He.create({name:"editable",addProseMirrorPlugins(){return[new Fe({key:new ze("editable"),props:{editable:()=>this.editor.options.editable}})]}}),Bd=He.create({name:"focusEvents",addProseMirrorPlugins(){const{editor:o}=this;return[new Fe({key:new ze("focusEvents"),props:{handleDOMEvents:{focus:(e,t)=>{o.isFocused=!0;const n=o.state.tr.setMeta("focus",{event:t}).setMeta("addToHistory",!1);return e.dispatch(n),!1},blur:(e,t)=>{o.isFocused=!1;const n=o.state.tr.setMeta("blur",{event:t}).setMeta("addToHistory",!1);return e.dispatch(n),!1}}}})]}}),Vd=He.create({name:"keymap",addKeyboardShortcuts(){const o=()=>this.editor.commands.first(({commands:i})=>[()=>i.undoInputRule(),()=>i.command(({tr:s})=>{const{selection:l,doc:a}=s,{empty:c,$anchor:d}=l,{pos:h,parent:u}=d,f=d.parent.isTextblock&&h>0?s.doc.resolve(h-1):d,p=f.parent.type.spec.isolating,m=d.pos-d.parentOffset,g=p&&f.parent.childCount===1?m===d.pos:Z.atStart(a).from===h;return!(!c||!u.type.isTextblock||u.textContent.length||!g||g&&d.parent.type.name==="paragraph")&&i.clearNodes()}),()=>i.deleteSelection(),()=>i.joinBackward(),()=>i.selectNodeBackward()]),e=()=>this.editor.commands.first(({commands:i})=>[()=>i.deleteSelection(),()=>i.deleteCurrentNode(),()=>i.joinForward(),()=>i.selectNodeForward()]),t={Enter:()=>this.editor.commands.first(({commands:i})=>[()=>i.newlineInCode(),()=>i.createParagraphNear(),()=>i.liftEmptyBlock(),()=>i.splitBlock()]),"Mod-Enter":()=>this.editor.commands.exitCode(),Backspace:o,"Mod-Backspace":o,"Shift-Backspace":o,Delete:e,"Mod-Delete":e,"Mod-a":()=>this.editor.commands.selectAll()},n={...t},r={...t,"Ctrl-h":o,"Alt-Backspace":o,"Ctrl-d":e,"Ctrl-Alt-Backspace":e,"Alt-Delete":e,"Alt-d":e,"Ctrl-a":()=>this.editor.commands.selectTextblockStart(),"Ctrl-e":()=>this.editor.commands.selectTextblockEnd()};return yo()||Oa()?r:n},addProseMirrorPlugins(){return[new Fe({key:new ze("clearDocument"),appendTransaction:(o,e,t)=>{if(!(o.some(h=>h.docChanged)&&!e.doc.eq(t.doc)))return;const{empty:n,from:r,to:i}=e.selection,s=Z.atStart(e.doc).from,l=Z.atEnd(e.doc).to;if(n||!(r===s&&i===l)||t.doc.textBetween(0,t.doc.content.size," "," ").length!==0)return;const a=t.tr,c=wr({state:t,transaction:a}),{commands:d}=new vr({editor:this.editor,state:c});return d.clearNodes(),a.steps.length?a:void 0}})]}}),Hd=He.create({name:"tabindex",addProseMirrorPlugins(){return[new Fe({key:new ze("tabindex"),props:{attributes:this.editor.isEditable?{tabindex:"0"}:{}}})]}});class At{constructor(e,t,n=!1,r=null){this.currentNode=null,this.actualDepth=null,this.isBlock=n,this.resolvedPos=e,this.editor=t,this.currentNode=r}get name(){return this.node.type.name}get node(){return this.currentNode||this.resolvedPos.node()}get element(){return this.editor.view.domAtPos(this.pos).node}get depth(){var e;return(e=this.actualDepth)!==null&&e!==void 0?e:this.resolvedPos.depth}get pos(){return this.resolvedPos.pos}get content(){return this.node.content}set content(e){let t=this.from,n=this.to;if(this.isBlock){if(this.content.size===0)return void console.error(`You can’t set content on a block node. Tried to set content on ${this.name} at ${this.pos}`);t=this.from+1,n=this.to-1}this.editor.commands.insertContentAt({from:t,to:n},e)}get attributes(){return this.node.attrs}get textContent(){return this.node.textContent}get size(){return this.node.nodeSize}get from(){return this.isBlock?this.pos:this.resolvedPos.start(this.resolvedPos.depth)}get range(){return{from:this.from,to:this.to}}get to(){return this.isBlock?this.pos+this.size:this.resolvedPos.end(this.resolvedPos.depth)+(this.node.isText?0:1)}get parent(){if(this.depth===0)return null;const e=this.resolvedPos.start(this.resolvedPos.depth-1),t=this.resolvedPos.doc.resolve(e);return new At(t,this.editor)}get before(){let e=this.resolvedPos.doc.resolve(this.from-(this.isBlock?1:2));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.from-3)),new At(e,this.editor)}get after(){let e=this.resolvedPos.doc.resolve(this.to+(this.isBlock?2:1));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.to+3)),new At(e,this.editor)}get children(){const e=[];return this.node.content.forEach((t,n)=>{const r=t.isBlock&&!t.isTextblock,i=this.pos+n+1,s=this.resolvedPos.doc.resolve(i);if(!r&&s.depth<=this.depth)return;const l=new At(s,this.editor,r,r?t:null);r&&(l.actualDepth=this.depth+1),e.push(new At(s,this.editor,r,r?t:null))}),e}get firstChild(){return this.children[0]||null}get lastChild(){const e=this.children;return e[e.length-1]||null}closest(e,t={}){let n=null,r=this.parent;for(;r&&!n;){if(r.node.type.name===e)if(Object.keys(t).length>0){const i=r.node.attrs,s=Object.keys(t);for(let l=0;l<s.length;l+=1){const a=s[l];if(i[a]!==t[a])break}}else n=r;r=r.parent}return n}querySelector(e,t={}){return this.querySelectorAll(e,t,!0)[0]||null}querySelectorAll(e,t={},n=!1){let r=[];if(!this.children||this.children.length===0)return r;const i=Object.keys(t);return this.children.forEach(s=>{n&&r.length>0||(s.node.type.name===e&&i.every(l=>t[l]===s.node.attrs[l])&&r.push(s),n&&r.length>0||(r=r.concat(s.querySelectorAll(e,t,n))))}),r}setAttribute(e){const t=this.editor.state.selection;this.editor.chain().setTextSelection(this.from).updateAttributes(this.node.type.name,e).setTextSelection(t.from).run()}}class jd extends Od{constructor(e={}){super(),this.isFocused=!1,this.extensionStorage={},this.options={element:document.createElement("div"),content:"",injectCSS:!0,injectNonce:void 0,extensions:[],autofocus:!1,editable:!0,editorProps:{},parseOptions:{},coreExtensionOptions:{},enableInputRules:!0,enablePasteRules:!0,enableCoreExtensions:!0,onBeforeCreate:()=>null,onCreate:()=>null,onUpdate:()=>null,onSelectionUpdate:()=>null,onTransaction:()=>null,onFocus:()=>null,onBlur:()=>null,onDestroy:()=>null},this.isCapturingTransaction=!1,this.capturedTransaction=null,this.setOptions(e),this.createExtensionManager(),this.createCommandManager(),this.createSchema(),this.on("beforeCreate",this.options.onBeforeCreate),this.emit("beforeCreate",{editor:this}),this.createView(),this.injectCSS(),this.on("create",this.options.onCreate),this.on("update",this.options.onUpdate),this.on("selectionUpdate",this.options.onSelectionUpdate),this.on("transaction",this.options.onTransaction),this.on("focus",this.options.onFocus),this.on("blur",this.options.onBlur),this.on("destroy",this.options.onDestroy),window.setTimeout(()=>{this.isDestroyed||(this.commands.focus(this.options.autofocus),this.emit("create",{editor:this}))},0)}get storage(){return this.extensionStorage}get commands(){return this.commandManager.commands}chain(){return this.commandManager.chain()}can(){return this.commandManager.can()}injectCSS(){this.options.injectCSS&&document&&(this.css=function(e,t,n){const r=document.querySelector("style[data-tiptap-style]");if(r!==null)return r;const i=document.createElement("style");return t&&i.setAttribute("nonce",t),i.setAttribute("data-tiptap-style",""),i.innerHTML=e,document.getElementsByTagName("head")[0].appendChild(i),i}(`.ProseMirror {
  position: relative;
}

.ProseMirror {
  word-wrap: break-word;
  white-space: pre-wrap;
  white-space: break-spaces;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0; /* the above doesn't seem to work in Edge */
}

.ProseMirror [contenteditable="false"] {
  white-space: normal;
}

.ProseMirror [contenteditable="false"] [contenteditable="true"] {
  white-space: pre-wrap;
}

.ProseMirror pre {
  white-space: pre-wrap;
}

img.ProseMirror-separator {
  display: inline !important;
  border: none !important;
  margin: 0 !important;
  width: 1px !important;
  height: 1px !important;
}

.ProseMirror-gapcursor {
  display: none;
  pointer-events: none;
  position: absolute;
  margin: 0;
}

.ProseMirror-gapcursor:after {
  content: "";
  display: block;
  position: absolute;
  top: -2px;
  width: 20px;
  border-top: 1px solid black;
  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;
}

@keyframes ProseMirror-cursor-blink {
  to {
    visibility: hidden;
  }
}

.ProseMirror-hideselection *::selection {
  background: transparent;
}

.ProseMirror-hideselection *::-moz-selection {
  background: transparent;
}

.ProseMirror-hideselection * {
  caret-color: transparent;
}

.ProseMirror-focused .ProseMirror-gapcursor {
  display: block;
}

.tippy-box[data-animation=fade][data-state=hidden] {
  opacity: 0
}`,this.options.injectNonce))}setOptions(e={}){this.options={...this.options,...e},this.view&&this.state&&!this.isDestroyed&&(this.options.editorProps&&this.view.setProps(this.options.editorProps),this.view.updateState(this.state))}setEditable(e,t=!0){this.setOptions({editable:e}),t&&this.emit("update",{editor:this,transaction:this.state.tr})}get isEditable(){return this.options.editable&&this.view&&this.view.editable}get state(){return this.view.state}registerPlugin(e,t){const n=ba(t)?t(e,[...this.state.plugins]):[...this.state.plugins,e],r=this.state.reconfigure({plugins:n});this.view.updateState(r)}unregisterPlugin(e){if(this.isDestroyed)return;const t=typeof e=="string"?`${e}$`:e.key,n=this.state.reconfigure({plugins:this.state.plugins.filter(r=>!r.key.startsWith(t))});this.view.updateState(n)}createExtensionManager(){var e,t;const n=[...this.options.enableCoreExtensions?[Fd,Dd.configure({blockSeparator:(t=(e=this.options.coreExtensionOptions)===null||e===void 0?void 0:e.clipboardTextSerializer)===null||t===void 0?void 0:t.blockSeparator}),zd,Bd,Vd,Hd]:[],...this.options.extensions].filter(r=>["extension","node","mark"].includes(r==null?void 0:r.type));this.extensionManager=new on(n,this)}createCommandManager(){this.commandManager=new vr({editor:this})}createSchema(){this.schema=this.extensionManager.schema}createView(){const e=Ea(this.options.content,this.schema,this.options.parseOptions),t=Ma(e,this.options.autofocus);this.view=new yd(this.options.element,{...this.options.editorProps,dispatchTransaction:this.dispatchTransaction.bind(this),state:rn.create({doc:e,selection:t||void 0})});const n=this.state.reconfigure({plugins:this.extensionManager.plugins});this.view.updateState(n),this.createNodeViews(),this.prependClass(),this.view.dom.editor=this}createNodeViews(){this.view.setProps({nodeViews:this.extensionManager.nodeViews})}prependClass(){this.view.dom.className=`tiptap ${this.view.dom.className}`}captureTransaction(e){this.isCapturingTransaction=!0,e(),this.isCapturingTransaction=!1;const t=this.capturedTransaction;return this.capturedTransaction=null,t}dispatchTransaction(e){if(this.view.isDestroyed)return;if(this.isCapturingTransaction)return this.capturedTransaction?void e.steps.forEach(s=>{var l;return(l=this.capturedTransaction)===null||l===void 0?void 0:l.step(s)}):void(this.capturedTransaction=e);const t=this.state.apply(e),n=!this.state.selection.eq(t.selection);this.view.updateState(t),this.emit("transaction",{editor:this,transaction:e}),n&&this.emit("selectionUpdate",{editor:this,transaction:e});const r=e.getMeta("focus"),i=e.getMeta("blur");r&&this.emit("focus",{editor:this,event:r.event,transaction:e}),i&&this.emit("blur",{editor:this,event:i.event,transaction:e}),e.docChanged&&!e.getMeta("preventUpdate")&&this.emit("update",{editor:this,transaction:e})}getAttributes(e){return _d(this.state,e)}isActive(e,t){const n=typeof e=="string"?e:null,r=typeof e=="string"?t:e;return function(i,s,l={}){if(!s)return Nn(i,null,l)||Co(i,null,l);const a=cr(s,i.schema);return a==="node"?Nn(i,s,l):a==="mark"&&Co(i,s,l)}(this.state,n,r)}getJSON(){return this.state.doc.toJSON()}getHTML(){return function(e,t){const n=Ut.fromSchema(t).serializeFragment(e),r=document.implementation.createHTMLDocument().createElement("div");return r.appendChild(n),r.innerHTML}(this.state.doc.content,this.schema)}getText(e){const{blockSeparator:t=`

`,textSerializers:n={}}=e||{};return function(r,i){return $a(r,{from:0,to:r.content.size},i)}(this.state.doc,{blockSeparator:t,textSerializers:{...ka(this.schema),...n}})}get isEmpty(){return function(e){var t;const n=(t=e.type.createAndFill())===null||t===void 0?void 0:t.toJSON(),r=e.toJSON();return JSON.stringify(n)===JSON.stringify(r)}(this.state.doc)}getCharacterCount(){return console.warn('[tiptap warn]: "editor.getCharacterCount()" is deprecated. Please use "editor.storage.characterCount.characters()" instead.'),this.state.doc.content.size-2}destroy(){this.emit("destroy"),this.view&&this.view.destroy(),this.removeAllListeners()}get isDestroyed(){var e;return!(!((e=this.view)===null||e===void 0)&&e.docView)}$node(e,t){var n;return((n=this.$doc)===null||n===void 0?void 0:n.querySelector(e,t))||null}$nodes(e,t){var n;return((n=this.$doc)===null||n===void 0?void 0:n.querySelectorAll(e,t))||null}$pos(e){const t=this.state.doc.resolve(e);return new At(t,this)}get $doc(){return this.$pos(0)}}function Up(o){return new Td({find:o.find,handler:({state:e,range:t,match:n})=>{const r=Y(o.getAttributes,void 0,n)||{},{tr:i}=e,s=t.from;let l=t.to;const a=o.type.create(r);if(n[1]){let c=s+n[0].lastIndexOf(n[1]);c>l?c=l:l=c+n[1].length;const d=n[0][n[0].length-1];i.insertText(d,s+n[0].length-1),i.replaceWith(c,l,a)}else n[0]&&i.insert(s-1,o.type.create(r)).delete(i.mapping.map(s),i.mapping.map(l));i.scrollIntoView()}})}class dr{constructor(e={}){this.type="mark",this.name="mark",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=Y(D(this,"addOptions",{name:this.name}))),this.storage=Y(D(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new dr(e)}configure(e={}){const t=this.extend();return t.options=xr(this.options,e),t.storage=Y(D(t,"addStorage",{name:t.name,options:t.options})),t}extend(e={}){const t=new dr({...this.config,...e});return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=Y(D(t,"addOptions",{name:t.name})),t.storage=Y(D(t,"addStorage",{name:t.name,options:t.options})),t}static handleExit({editor:e,mark:t}){const{tr:n}=e.state,r=e.state.selection.$from;if(r.pos===r.end()){const i=r.marks();if(!i.find(l=>(l==null?void 0:l.type.name)===t.name))return!1;const s=i.find(l=>(l==null?void 0:l.type.name)===t.name);return s&&n.removeStoredMark(s),n.insertText(" ",r.pos),e.view.dispatch(n),!0}return!1}}class Ot{constructor(e={}){this.type="node",this.name="node",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=Y(D(this,"addOptions",{name:this.name}))),this.storage=Y(D(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new Ot(e)}configure(e={}){const t=this.extend();return t.options=xr(this.options,e),t.storage=Y(D(t,"addStorage",{name:t.name,options:t.options})),t}extend(e={}){const t=new Ot({...this.config,...e});return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=Y(D(t,"addOptions",{name:t.name})),t.storage=Y(D(t,"addStorage",{name:t.name,options:t.options})),t}}const qd=Ot.create({name:"paragraph",priority:1e3,addOptions:()=>({HTMLAttributes:{}}),group:"block",content:"inline*",parseHTML:()=>[{tag:"p"}],renderHTML({HTMLAttributes:o}){return["p",Pt(this.options.HTMLAttributes,o),0]},addCommands(){return{setParagraph:()=>({commands:o})=>o.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}}),Kd=Ot.create({name:"hardBreak",addOptions:()=>({keepMarks:!0,HTMLAttributes:{}}),inline:!0,group:"inline",selectable:!1,parseHTML:()=>[{tag:"br"}],renderHTML({HTMLAttributes:o}){return["br",Pt(this.options.HTMLAttributes,o)]},renderText:()=>`
`,addCommands(){return{setHardBreak:()=>({commands:o,chain:e,state:t,editor:n})=>o.first([()=>o.exitCode(),()=>o.command(()=>{const{selection:r,storedMarks:i}=t;if(r.$from.parent.type.spec.isolating)return!1;const{keepMarks:s}=this.options,{splittableMarks:l}=n.extensionManager,a=i||r.$to.parentOffset&&r.$from.marks();return e().insertContent({type:this.name}).command(({tr:c,dispatch:d})=>{if(d&&a&&s){const h=a.filter(u=>l.includes(u.type.name));c.ensureMarks(h)}return!0}).run()})])}},addKeyboardShortcuts(){return{"Mod-Enter":()=>this.editor.commands.setHardBreak(),"Shift-Enter":()=>this.editor.commands.setHardBreak()}}}),Wd=Ot.create({name:"text",group:"inline"}),Jd=Ot.create({name:"doc",topNode:!0,content:"block+"});var hr=200,pe=function(){};pe.prototype.append=function(o){return o.length?(o=pe.from(o),!this.length&&o||o.length<hr&&this.leafAppend(o)||this.length<hr&&o.leafPrepend(this)||this.appendInner(o)):this},pe.prototype.prepend=function(o){return o.length?pe.from(o).append(this):this},pe.prototype.appendInner=function(o){return new Ud(this,o)},pe.prototype.slice=function(o,e){return o===void 0&&(o=0),e===void 0&&(e=this.length),o>=e?pe.empty:this.sliceInner(Math.max(0,o),Math.min(this.length,e))},pe.prototype.get=function(o){if(!(o<0||o>=this.length))return this.getInner(o)},pe.prototype.forEach=function(o,e,t){e===void 0&&(e=0),t===void 0&&(t=this.length),e<=t?this.forEachInner(o,e,t,0):this.forEachInvertedInner(o,e,t,0)},pe.prototype.map=function(o,e,t){e===void 0&&(e=0),t===void 0&&(t=this.length);var n=[];return this.forEach(function(r,i){return n.push(o(r,i))},e,t),n},pe.from=function(o){return o instanceof pe?o:o&&o.length?new Na(o):pe.empty};var Na=function(o){function e(n){o.call(this),this.values=n}o&&(e.__proto__=o),e.prototype=Object.create(o&&o.prototype),e.prototype.constructor=e;var t={length:{configurable:!0},depth:{configurable:!0}};return e.prototype.flatten=function(){return this.values},e.prototype.sliceInner=function(n,r){return n==0&&r==this.length?this:new e(this.values.slice(n,r))},e.prototype.getInner=function(n){return this.values[n]},e.prototype.forEachInner=function(n,r,i,s){for(var l=r;l<i;l++)if(n(this.values[l],s+l)===!1)return!1},e.prototype.forEachInvertedInner=function(n,r,i,s){for(var l=r-1;l>=i;l--)if(n(this.values[l],s+l)===!1)return!1},e.prototype.leafAppend=function(n){if(this.length+n.length<=hr)return new e(this.values.concat(n.flatten()))},e.prototype.leafPrepend=function(n){if(this.length+n.length<=hr)return new e(n.flatten().concat(this.values))},t.length.get=function(){return this.values.length},t.depth.get=function(){return 0},Object.defineProperties(e.prototype,t),e}(pe);pe.empty=new Na([]);var Ud=function(o){function e(t,n){o.call(this),this.left=t,this.right=n,this.length=t.length+n.length,this.depth=Math.max(t.depth,n.depth)+1}return o&&(e.__proto__=o),e.prototype=Object.create(o&&o.prototype),e.prototype.constructor=e,e.prototype.flatten=function(){return this.left.flatten().concat(this.right.flatten())},e.prototype.getInner=function(t){return t<this.left.length?this.left.get(t):this.right.get(t-this.left.length)},e.prototype.forEachInner=function(t,n,r,i){var s=this.left.length;return!(n<s&&this.left.forEachInner(t,n,Math.min(r,s),i)===!1)&&!(r>s&&this.right.forEachInner(t,Math.max(n-s,0),Math.min(this.length,r)-s,i+s)===!1)&&void 0},e.prototype.forEachInvertedInner=function(t,n,r,i){var s=this.left.length;return!(n>s&&this.right.forEachInvertedInner(t,n-s,Math.max(r,s)-s,i+s)===!1)&&!(r<s&&this.left.forEachInvertedInner(t,Math.min(n,s),r,i)===!1)&&void 0},e.prototype.sliceInner=function(t,n){if(t==0&&n==this.length)return this;var r=this.left.length;return n<=r?this.left.slice(t,n):t>=r?this.right.slice(t-r,n-r):this.left.slice(t,r).append(this.right.slice(0,n-r))},e.prototype.leafAppend=function(t){var n=this.right.leafAppend(t);if(n)return new e(this.left,n)},e.prototype.leafPrepend=function(t){var n=this.left.leafPrepend(t);if(n)return new e(n,this.right)},e.prototype.appendInner=function(t){return this.left.depth>=Math.max(this.right.depth,t.depth)+1?new e(this.left,new e(this.right,t)):new e(this,t)},e}(pe);class Je{constructor(e,t){this.items=e,this.eventCount=t}popEvent(e,t){if(this.eventCount==0)return null;let n,r,i=this.items.length;for(;;i--)if(this.items.get(i-1).selection){--i;break}t&&(n=this.remapping(i,this.items.length),r=n.maps.length);let s,l,a=e.tr,c=[],d=[];return this.items.forEach((h,u)=>{if(!h.step)return n||(n=this.remapping(i,u+1),r=n.maps.length),r--,void d.push(h);if(n){d.push(new Ye(h.map));let f,p=h.step.map(n.slice(r));p&&a.maybeStep(p).doc&&(f=a.mapping.maps[a.mapping.maps.length-1],c.push(new Ye(f,void 0,void 0,c.length+d.length))),r--,f&&n.appendMap(f,r)}else a.maybeStep(h.step);return h.selection?(s=n?h.selection.map(n.slice(r)):h.selection,l=new Je(this.items.slice(0,i).append(d.reverse().concat(c)),this.eventCount-1),!1):void 0},this.items.length,0),{remaining:l,transform:a,selection:s}}addTransform(e,t,n,r){let i=[],s=this.eventCount,l=this.items,a=!r&&l.length?l.get(l.length-1):null;for(let d=0;d<e.steps.length;d++){let h,u=e.steps[d].invert(e.docs[d]),f=new Ye(e.mapping.maps[d],u,t);(h=a&&a.merge(f))&&(f=h,d?i.pop():l=l.slice(0,l.length-1)),i.push(f),t&&(s++,t=void 0),r||(a=f)}let c=s-n.depth;return c>Gd&&(l=function(d,h){let u;return d.forEach((f,p)=>{if(f.selection&&h--==0)return u=p,!1}),d.slice(u)}(l,c),s-=c),new Je(l.append(i),s)}remapping(e,t){let n=new an;return this.items.forEach((r,i)=>{let s=r.mirrorOffset!=null&&i-r.mirrorOffset>=e?n.maps.length-r.mirrorOffset:void 0;n.appendMap(r.map,s)},e,t),n}addMaps(e){return this.eventCount==0?this:new Je(this.items.append(e.map(t=>new Ye(t))),this.eventCount)}rebased(e,t){if(!this.eventCount)return this;let n=[],r=Math.max(0,this.items.length-t),i=e.mapping,s=e.steps.length,l=this.eventCount;this.items.forEach(u=>{u.selection&&l--},r);let a=t;this.items.forEach(u=>{let f=i.getMirror(--a);if(f==null)return;s=Math.min(s,f);let p=i.maps[f];if(u.step){let m=e.steps[f].invert(e.docs[f]),g=u.selection&&u.selection.map(i.slice(a+1,f));g&&l++,n.push(new Ye(p,m,g))}else n.push(new Ye(p))},r);let c=[];for(let u=t;u<s;u++)c.push(new Ye(i.maps[u]));let d=this.items.slice(0,r).append(c).append(n),h=new Je(d,l);return h.emptyItemCount()>500&&(h=h.compress(this.items.length-n.length)),h}emptyItemCount(){let e=0;return this.items.forEach(t=>{t.step||e++}),e}compress(e=this.items.length){let t=this.remapping(0,e),n=t.maps.length,r=[],i=0;return this.items.forEach((s,l)=>{if(l>=e)r.push(s),s.selection&&i++;else if(s.step){let a=s.step.map(t.slice(n)),c=a&&a.getMap();if(n--,c&&t.appendMap(c,n),a){let d=s.selection&&s.selection.map(t.slice(n));d&&i++;let h,u=new Ye(c.invert(),a,d),f=r.length-1;(h=r.length&&r[f].merge(u))?r[f]=h:r.push(u)}}else s.map&&n--},this.items.length,0),new Je(pe.from(r.reverse()),i)}}Je.empty=new Je(pe.empty,0);class Ye{constructor(e,t,n,r){this.map=e,this.step=t,this.selection=n,this.mirrorOffset=r}merge(e){if(this.step&&e.step&&!e.selection){let t=e.step.merge(this.step);if(t)return new Ye(t.getMap().invert(),t,this.selection)}}}class ut{constructor(e,t,n,r,i){this.done=e,this.undone=t,this.prevRanges=n,this.prevTime=r,this.prevComposition=i}}const Gd=20;function ms(o){let e=[];return o.forEach((t,n,r,i)=>e.push(r,i)),e}function Jr(o,e){if(!o)return null;let t=[];for(let n=0;n<o.length;n+=2){let r=e.map(o[n],1),i=e.map(o[n+1],-1);r<=i&&t.push(r,i)}return t}let Ur=!1,gs=null;function Zn(o){let e=o.plugins;if(gs!=e){Ur=!1,gs=e;for(let t=0;t<e.length;t++)if(e[t].spec.historyPreserveItems){Ur=!0;break}}return Ur}const Ft=new ze("history"),Zd=new ze("closeHistory");function Yd(o={}){return o={depth:o.depth||100,newGroupDelay:o.newGroupDelay||500},new Fe({key:Ft,state:{init:()=>new ut(Je.empty,Je.empty,null,0,-1),apply:(e,t,n)=>function(r,i,s,l){let a,c=s.getMeta(Ft);if(c)return c.historyState;s.getMeta(Zd)&&(r=new ut(r.done,r.undone,null,0,-1));let d=s.getMeta("appendedTransaction");if(s.steps.length==0)return r;if(d&&d.getMeta(Ft))return d.getMeta(Ft).redo?new ut(r.done.addTransform(s,void 0,l,Zn(i)),r.undone,ms(s.mapping.maps[s.steps.length-1]),r.prevTime,r.prevComposition):new ut(r.done,r.undone.addTransform(s,void 0,l,Zn(i)),null,r.prevTime,r.prevComposition);if(s.getMeta("addToHistory")===!1||d&&d.getMeta("addToHistory")===!1)return(a=s.getMeta("rebased"))?new ut(r.done.rebased(s,a),r.undone.rebased(s,a),Jr(r.prevRanges,s.mapping),r.prevTime,r.prevComposition):new ut(r.done.addMaps(s.mapping.maps),r.undone.addMaps(s.mapping.maps),Jr(r.prevRanges,s.mapping),r.prevTime,r.prevComposition);{let h=s.getMeta("composition"),u=r.prevTime==0||!d&&r.prevComposition!=h&&(r.prevTime<(s.time||0)-l.newGroupDelay||!function(p,m){if(!m)return!1;if(!p.docChanged)return!0;let g=!1;return p.mapping.maps[0].forEach((y,C)=>{for(let v=0;v<m.length;v+=2)y<=m[v+1]&&C>=m[v]&&(g=!0)}),g}(s,r.prevRanges)),f=d?Jr(r.prevRanges,s.mapping):ms(s.mapping.maps[s.steps.length-1]);return new ut(r.done.addTransform(s,u?i.selection.getBookmark():void 0,l,Zn(i)),Je.empty,f,s.time,h??r.prevComposition)}}(t,n,e,o)},config:o,props:{handleDOMEvents:{beforeinput(e,t){let n=t.inputType,r=n=="historyUndo"?Aa:n=="historyRedo"?Ia:null;return!!r&&(t.preventDefault(),r(e.state,e.dispatch))}}}})}function La(o,e){return(t,n)=>{let r=Ft.getState(t);if(!r||(o?r.undone:r.done).eventCount==0)return!1;if(n){let i=function(s,l,a){let c=Zn(l),d=Ft.get(l).spec.config,h=(a?s.undone:s.done).popEvent(l,c);if(!h)return null;let u=h.selection.resolve(h.transform.doc),f=(a?s.done:s.undone).addTransform(h.transform,l.selection.getBookmark(),d,c),p=new ut(a?f:h.remaining,a?h.remaining:f,null,0,-1);return h.transform.setSelection(u).setMeta(Ft,{redo:a,historyState:p})}(r,t,o);i&&n(e?i.scrollIntoView():i)}return!0}}const Aa=La(!1,!0),Ia=La(!0,!0),Xd=He.create({name:"history",addOptions:()=>({depth:100,newGroupDelay:500}),addCommands:()=>({undo:()=>({state:o,dispatch:e})=>Aa(o,e),redo:()=>({state:o,dispatch:e})=>Ia(o,e)}),addProseMirrorPlugins(){return[Yd(this.options)]},addKeyboardShortcuts(){return{"Mod-z":()=>this.editor.commands.undo(),"Shift-Mod-z":()=>this.editor.commands.redo(),"Mod-y":()=>this.editor.commands.redo(),"Mod-я":()=>this.editor.commands.undo(),"Shift-Mod-я":()=>this.editor.commands.redo()}}});class Qd{constructor(e){x(this,"_registeredPlugins",ke([]));x(this,"_defaultPlugins");x(this,"_allPlugins");x(this,"_getDefaultPlugins",e=>[Jd.extend({addKeyboardShortcuts:()=>({...e.keyboardShortcuts})}),qd,Wd,Kd,Xd.configure({depth:100})].map(t=>({tipTapExtension:t})));x(this,"registerPlugin",e=>(this._registeredPlugins.update(t=>[...t,e]),()=>{this._registeredPlugins.update(t=>t.filter(n=>n!==e))}));x(this,"onPluginsChanged",e=>this._allPlugins.subscribe(e));this._opts=e,this._defaultPlugins=Ke(this._opts,this._getDefaultPlugins),this._allPlugins=Ke([this._defaultPlugins,this._registeredPlugins],([t,n])=>[...t,...n])}get tipTapExtensions(){return re(this._allPlugins).map(e=>e.tipTapExtension)}}function ys(o){return o.length===0?[]:[{type:"text",text:o}]}function Cs(o){if(o!==void 0){if(typeof o=="string"){const e=o.split(`
`).map((t,n)=>n===0?ys(t):[{type:"hardBreak"},...ys(t)]).flat();return e.length>0?e:void 0}return Array.isArray(o)?o.length>0?o:void 0:o}}class eh{constructor(e){x(this,"_setupFns",[]);x(this,"_editor");x(this,"registerEditor",e=>{this._editor=e,this._runSetupFns()});x(this,"unregisterEditor",()=>{this._editor=void 0});x(this,"can",()=>{var e;return(e=this._editor)==null?void 0:e.can()});x(this,"chain",()=>{var e;return(e=this._editor)==null?void 0:e.chain()});x(this,"_queueOrRun",e=>{this._editor?e():this._setupFns.push(e)});x(this,"_runSetupFns",()=>{this._setupFns.forEach(e=>{e()}),this._setupFns=[]});x(this,"hide",()=>this._queueOrRun(this._hide));x(this,"show",()=>this._queueOrRun(this._show));x(this,"focus",()=>this._queueOrRun(this._focus));x(this,"requestFocus",()=>this._queueOrRun(this._requestFocus));x(this,"blur",()=>this._queueOrRun(this._blur));x(this,"scrollToCursor",()=>this._queueOrRun(this._scrollToCursor));x(this,"setEditable",e=>this._queueOrRun(()=>this._setEditable(e)));x(this,"clearContent",()=>this._queueOrRun(this._clearContent));x(this,"setContent",(e,t)=>this._queueOrRun(()=>this._setContent(e,t)));x(this,"insertContent",e=>this._queueOrRun(()=>this._insertContent(e)));x(this,"_hide",()=>{this._editor&&(this._editor.view.dom.style.display="none")});x(this,"_show",()=>{this._editor&&this._editor.view.dom.style.removeProperty("display")});x(this,"_focus",()=>{var e;return(e=this._editor)==null?void 0:e.commands.focus()});x(this,"_requestFocus",async()=>{await Ga(),this._editor&&this._editor.commands.focus()});x(this,"_blur",()=>{var e;return(e=this._editor)==null?void 0:e.commands.blur()});x(this,"_scrollToCursor",()=>{var e;return(e=this._editor)==null?void 0:e.commands.scrollIntoView()});x(this,"_setEditable",e=>{var t,n;(t=this._editor)==null||t.setEditable(e),e&&((n=this._editor)==null||n.commands.setTextSelection(this._editor.state.doc.content.size))});x(this,"_clearContent",()=>{var t;const e=re(this._opts.content);e&&e.rawText!==""&&((t=this._editor)==null||t.commands.clearContent(!0))});x(this,"_setContent",(e,t)=>{var r;const n=re(this._opts.content);e!==(n==null?void 0:n.rawText)&&e!==(n==null?void 0:n.richTextJsonRepr)&&((e=Cs(e??[]))!==void 0?(r=this._editor)==null||r.chain().setContent(e,!0,{preserveWhitespace:!0}).setTextSelection((t==null?void 0:t.cursorPosition)==="start"?0:1e20).run():this._clearContent())});x(this,"_insertContent",e=>{var n;const t=Cs(e);t!==void 0&&((n=this._editor)==null||n.commands.insertContent(t,{parseOptions:{preserveWhitespace:!0}}))});this._opts=e}get commands(){var e;return(e=this._editor)==null?void 0:e.commands}}class th{constructor(){x(this,"_isFocused",ke(!1));x(this,"_isEditable",ke(!1));x(this,"_content",ke(void 0));x(this,"_disposers",[]);x(this,"_isFooterClicked",!1);x(this,"_footerClickTimeout");x(this,"_footerAwareFocused",ke(!1));x(this,"registerEditor",e=>{this._isFocused.set(e.isFocused),this._isEditable.set(e.isEditable),this._content.set({richTextJsonRepr:e.getJSON(),rawText:e.getText()}),this._footerAwareFocused.set(e.isFocused);const t=()=>{this._isFocused.set(!0),this._isFooterClicked||this._footerAwareFocused.set(!0)},n=()=>{this._isFocused.set(!1),this._isFooterClicked||this._footerAwareFocused.set(!1)},r=()=>this._isEditable.set(e.isEditable),i=()=>{this._content.set({richTextJsonRepr:e.getJSON(),rawText:e.getText()})};e.on("focus",t),e.on("blur",n),e.on("update",r),e.on("update",i),this._disposers.push(()=>e.off("focus",t),()=>e.off("blur",n),()=>e.off("update",r),()=>e.off("update",i))});x(this,"unregisterEditor",()=>{this._isFocused.set(!1),this._isEditable.set(!1),this._footerAwareFocused.set(!1),this._footerClickTimeout&&(clearTimeout(this._footerClickTimeout),this._footerClickTimeout=void 0),this._disposers.forEach(e=>e()),this._disposers=[]});x(this,"handleFooterClick",()=>{this._isFooterClicked=!0,this._footerClickTimeout&&clearTimeout(this._footerClickTimeout),this._footerClickTimeout=setTimeout(()=>{this._isFooterClicked=!1,this._footerClickTimeout=void 0},200)});x(this,"onFocus",e=>this.onFocusChanged(t=>{t&&e()}));x(this,"onBlur",e=>this.onFocusChanged(t=>{!t&&e()}));x(this,"onFocusChanged",e=>this._isFocused.subscribe(e));x(this,"onEditableChanged",e=>this._isEditable.subscribe(e));x(this,"onContentChanged",e=>this._content.subscribe(t=>t&&e(t)))}get isFocused(){return this._isFocused}get footerAwareFocused(){return this._footerAwareFocused}get isEditable(){return this._isEditable}get content(){return this._content}}const Dt=class Dt{constructor(){x(this,"_opts",ke({}));x(this,"_editor");x(this,"_rootNode");x(this,"pluginManager",new Qd(this._opts));x(this,"eventManager",new th);x(this,"commandManager",new eh({content:this.eventManager.content}));x(this,"instanceId",`augment-rich-text-editor-${Dt._getNextInstanceIdx()}`);x(this,"registerRoot",(e,t)=>(this._destroyEditor(),this._rootNode=e,this._opts.set(t),this._initializeEditor(),{update:n=>{this._opts.set(n)},destroy:()=>{this._destroyEditor()}}));x(this,"_registerEditorWithManagers",e=>{this.eventManager.registerEditor(e),this.commandManager.registerEditor(e)});x(this,"_unregisterEditorFromManagers",()=>{this.commandManager.unregisterEditor(),this.eventManager.unregisterEditor()});x(this,"_reinitializeEditor",()=>{const e=re(this.eventManager.content);this._destroyEditor(),this._initializeEditor(),e!==void 0&&this.commandManager.setContent(e.richTextJsonRepr)});x(this,"_initializeEditor",()=>{if(this._rootNode===void 0||this._editor!==void 0)return;const e=re(this._opts),t={element:document.createElement("div"),editable:e.editable??!0,injectCSS:!0,extensions:this.pluginManager.tipTapExtensions,onCreate:({editor:n})=>{this._registerEditorWithManagers(n),re(this._opts).focusOnInit&&this.commandManager.focus();const r=this._attachCopyHandler();n.on("destroy",r);const i=re(this._opts).onFocus;i&&n.on("focus",i);const s=re(this._opts).onBlur;s&&n.on("blur",s)},onDestroy:()=>{this._unregisterEditorFromManagers()},onSelectionUpdate:()=>{(re(this._opts).editable??1)&&this.commandManager.scrollToCursor()},editorProps:{handlePaste:(n,r)=>{var s;if(this._isEventFromRichTextEditor(r))return!1;const i=(s=r.clipboardData)==null?void 0:s.getData("text/plain");return!!i&&(this.commandManager.insertContent(i),!0)},attributes:{style:"min-height: 100%; outline: none;","data-testid":"design-system-rich-text-editor-tiptap"}}};return this._editor=new jd(t),this._rootNode.appendChild(this._editor.view.dom),this._rootNode.addEventListener("keydown",n=>{n.key==="z"&&n.metaKey===!0&&n.stopPropagation()}),this._editor});x(this,"_attachCopyHandler",()=>{var e,t;return(e=this._rootNode)==null||e.addEventListener("copy",this._copyHandler),(t=this._rootNode)==null||t.addEventListener("cut",this._copyHandler),()=>{var n,r;(n=this._rootNode)==null||n.removeEventListener("copy",this._copyHandler),(r=this._rootNode)==null||r.removeEventListener("cut",this._copyHandler)}});x(this,"_copyHandler",e=>{var t;(t=e.clipboardData)==null||t.setData("application/x-augment/rich-text","true")});x(this,"_isEventFromRichTextEditor",e=>{var t;return((t=e.clipboardData)==null?void 0:t.getData("application/x-augment/rich-text"))==="true"});x(this,"_destroyEditor",()=>{var e,t;this._unregisterEditorFromManagers(),(e=this._editor)==null||e.view.dom.remove(),(t=this._editor)==null||t.destroy(),this._editor=void 0});this.pluginManager.onPluginsChanged(this._reinitializeEditor)}get rootNode(){var e;return(e=this._editor)==null?void 0:e.view.dom}};x(Dt,"CONTEXT_KEY","augment-rich-text-editor"),x(Dt,"INSTANCE_IDX",0),x(Dt,"_getNextInstanceIdx",()=>Dt.INSTANCE_IDX++);let Ge=Dt;function Gp(){const o=nt(Ge.CONTEXT_KEY);if(!o)throw new Error(`No editor context '${Ge.CONTEXT_KEY}' found.`);return o}function ws(o){let e,t,n;return t=new lc({props:{variant:"soft",color:"error",size:1,$$slots:{icon:[rh],default:[nh]},$$scope:{ctx:o}}}),{c(){e=te("div"),P(t.$$.fragment),w(e,"class","paste-error-container svelte-spp06o")},m(r,i){V(r,e,i),_(t,e,null),n=!0},p(r,i){const s={};1026&i&&(s.$$scope={dirty:i,ctx:r}),t.$set(s)},i(r){n||($(t.$$.fragment,r),n=!0)},o(r){M(t.$$.fragment,r),n=!1},d(r){r&&B(e),R(t)}}}function nh(o){let e;return{c(){e=qe(o[1])},m(t,n){V(t,e,n)},p(t,n){2&n&&je(e,t[1])},d(t){t&&B(e)}}}function rh(o){let e,t;return e=new tl({props:{slot:"icon"}}),{c(){P(e.$$.fragment)},m(n,r){_(e,n,r),t=!0},p:W,i(n){t||($(e.$$.fragment,n),t=!0)},o(n){M(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function oh(o){let e,t,n=o[0]&&ws(o);return{c(){n&&n.c(),e=dt()},m(r,i){n&&n.m(r,i),V(r,e,i),t=!0},p(r,[i]){r[0]?n?(n.p(r,i),1&i&&$(n,1)):(n=ws(r),n.c(),$(n,1),n.m(e.parentNode,e)):n&&(ae(),M(n,1,1,()=>{n=null}),ce())},i(r){t||($(n),t=!0)},o(r){M(n),t=!1},d(r){r&&B(e),n&&n.d(r)}}}function ih(o,e,t){let n,r,{onContentChanged:i=()=>{}}=e,{content:s=""}=e;const l=ke(!1);Ct(o,l,f=>t(0,n=f));const a=ke("");let c;Ct(o,a,f=>t(1,r=f));const d=nt(Ge.CONTEXT_KEY);let h,u;return Za(()=>{u=function(f={}){const p=f.maxPasteSize??1e5,m=f.onExceedsPasteLimit,g=function(y){const C=y.clipboardData||window.clipboardData;if(!C)return;const v=C.getData("text/plain");!v||v.length<=p||(y.preventDefault(),console.warn(`Paste operation blocked: Content exceeds ${p} character limit (${v.length} characters)`),m&&m(v.length,p))};return document.addEventListener("paste",g,!0),()=>{document.removeEventListener("paste",g,!0)}}({onExceedsPasteLimit:(f,p)=>{$r(a,r=`Cannot paste: Content exceeds ${p.toLocaleString()} character limit (${f.toLocaleString()} characters)`,r),$r(l,n=!0,n),c&&clearTimeout(c),c=window.setTimeout(()=>{$r(l,n=!1,n)},3e3)}})}),Pn(()=>{u&&u(),clearTimeout(c)}),o.$$set=f=>{"onContentChanged"in f&&t(4,i=f.onContentChanged),"content"in f&&t(5,s=f.content)},o.$$.update=()=>{80&o.$$.dirty&&(h==null||h(),t(6,h=d.eventManager.onContentChanged(i))),32&o.$$.dirty&&d.commandManager.setContent(s)},[n,r,l,a,i,s,h]}const sh=o=>({}),vs=o=>({}),lh=o=>({}),xs=o=>({}),ah=o=>({}),bs=o=>({}),ch=o=>({}),$s=o=>({});function ks(o){let e,t;const n=o[16].banner,r=Te(n,o,o[19],$s);return{c(){e=te("div"),r&&r.c(),w(e,"class","c-rich-text-editor-augment__banner svelte-16c70y7")},m(i,s){V(i,e,s),r&&r.m(e,null),t=!0},p(i,s){r&&r.p&&(!t||524288&s)&&Ne(r,n,i,i[19],t?Ae(n,i[19],s,ch):Le(i[19]),$s)},i(i){t||($(r,i),t=!0)},o(i){M(r,i),t=!1},d(i){i&&B(e),r&&r.d(i)}}}function dh(o){let e,t,n,r,i,s,l,a;const c=o[16].header,d=Te(c,o,o[19],xs),h=o[16].default,u=Te(h,o,o[19],null);let f=o[12].footer&&Ms(o);return{c(){e=te("div"),d&&d.c(),t=le(),n=te("div"),u&&u.c(),i=le(),f&&f.c(),w(n,"class","c-rich-text-editor-augment__editor svelte-16c70y7"),w(e,"class","l-rich-text-editor-augment svelte-16c70y7"),w(e,"role","button"),w(e,"tabindex","-1")},m(p,m){V(p,e,m),d&&d.m(e,null),N(e,t),N(e,n),u&&u.m(n,null),N(e,i),f&&f.m(e,null),s=!0,l||(a=[Xa(r=o[10].registerRoot(n,{editable:o[0],focusOnInit:o[1],onFocus:o[2],onBlur:o[3]})),ve(e,"keydown",o[7]),ve(e,"click",o[7]),ve(e,"click",o[17]),ve(e,"dblclick",o[18])],l=!0)},p(p,m){d&&d.p&&(!s||524288&m)&&Ne(d,c,p,p[19],s?Ae(c,p[19],m,lh):Le(p[19]),xs),u&&u.p&&(!s||524288&m)&&Ne(u,h,p,p[19],s?Ae(h,p[19],m,null):Le(p[19]),null),r&&Qa(r.update)&&15&m&&r.update.call(null,{editable:p[0],focusOnInit:p[1],onFocus:p[2],onBlur:p[3]}),p[12].footer?f?(f.p(p,m),4096&m&&$(f,1)):(f=Ms(p),f.c(),$(f,1),f.m(e,null)):f&&(ae(),M(f,1,1,()=>{f=null}),ce())},i(p){s||($(d,p),$(u,p),$(f),s=!0)},o(p){M(d,p),M(u,p),M(f),s=!1},d(p){p&&B(e),d&&d.d(p),u&&u.d(p),f&&f.d(),l=!1,pr(a)}}}function hh(o){let e,t;const n=o[16].activeButton,r=Te(n,o,o[19],bs);return{c(){e=te("div"),r&&r.c(),w(e,"class","c-rich-text-editor-augment__active-button svelte-16c70y7")},m(i,s){V(i,e,s),r&&r.m(e,null),t=!0},p(i,s){r&&r.p&&(!t||524288&s)&&Ne(r,n,i,i[19],t?Ae(n,i[19],s,ah):Le(i[19]),bs)},i(i){t||($(r,i),t=!0)},o(i){M(r,i),t=!1},d(i){i&&B(e),r&&r.d(i)}}}function Ms(o){let e,t,n,r;const i=o[16].footer,s=Te(i,o,o[19],vs);return{c(){e=te("div"),s&&s.c(),w(e,"class","c-rich-text-editor-augment__footer-wrapper svelte-16c70y7")},m(l,a){V(l,e,a),s&&s.m(e,null),t=!0,n||(r=[ve(e,"click",o[11]),ve(e,"mousedown",o[11])],n=!0)},p(l,a){s&&s.p&&(!t||524288&a)&&Ne(s,i,l,l[19],t?Ae(i,l[19],a,sh):Le(l[19]),vs)},i(l){t||($(s,l),t=!0)},o(l){M(s,l),t=!1},d(l){l&&B(e),s&&s.d(l),n=!1,pr(r)}}}function uh(o){let e,t,n,r,i,s=o[12].banner&&ks(o);const l=[hh,dh],a=[];function c(d,h){return d[6]?0:1}return t=c(o),n=a[t]=l[t](o),{c(){s&&s.c(),e=le(),n.c(),r=dt()},m(d,h){s&&s.m(d,h),V(d,e,h),a[t].m(d,h),V(d,r,h),i=!0},p(d,h){d[12].banner?s?(s.p(d,h),4096&h&&$(s,1)):(s=ks(d),s.c(),$(s,1),s.m(e.parentNode,e)):s&&(ae(),M(s,1,1,()=>{s=null}),ce());let u=t;t=c(d),t===u?a[t].p(d,h):(ae(),M(a[u],1,1,()=>{a[u]=null}),ce(),n=a[t],n?n.p(d,h):(n=a[t]=l[t](d),n.c()),$(n,1),n.m(r.parentNode,r))},i(d){i||($(s),$(n),i=!0)},o(d){M(s),M(n),i=!1},d(d){d&&(B(e),B(r)),s&&s.d(d),a[t].d(d)}}}function ph(o){let e,t,n,r;return t=new ac({props:{class:"c-rich-text-editor-augment__card",insetContent:!0,variant:"ghost",size:o[4],$$slots:{default:[uh]},$$scope:{ctx:o}}}),{c(){e=te("div"),P(t.$$.fragment),w(e,"class",n=hn(`c-rich-text-editor-augment c-rich-text-editor-augment--${o[5]}`)+" svelte-16c70y7"),Oe(e,"c-rich-text-editor-augment--editable",o[0]),Oe(e,"c-rich-text-editor-augment--focused",o[9])},m(i,s){V(i,e,s),_(t,e,null),r=!0},p(i,[s]){const l={};16&s&&(l.size=i[4]),528463&s&&(l.$$scope={dirty:s,ctx:i}),t.$set(l),(!r||32&s&&n!==(n=hn(`c-rich-text-editor-augment c-rich-text-editor-augment--${i[5]}`)+" svelte-16c70y7"))&&w(e,"class",n),(!r||33&s)&&Oe(e,"c-rich-text-editor-augment--editable",i[0]),(!r||544&s)&&Oe(e,"c-rich-text-editor-augment--focused",i[9])},i(i){r||($(t.$$.fragment,i),r=!0)},o(i){M(t.$$.fragment,i),r=!1},d(i){i&&B(e),R(t)}}}function fh(o,e,t){let n,r,i=W;o.$$.on_destroy.push(()=>i());let{$$slots:s={},$$scope:l}=e;const a=So(s);let{editable:c}=e,{focusOnInit:d}=e,{onFocus:h}=e,{onBlur:u}=e,{size:f=1}=e,{variant:p="default"}=e,{showActionButton:m=!1}=e;const g=new Ge;return el(Ge.CONTEXT_KEY,g),d&&g.commandManager.requestFocus(),o.$$set=y=>{"editable"in y&&t(0,c=y.editable),"focusOnInit"in y&&t(1,d=y.focusOnInit),"onFocus"in y&&t(2,h=y.onFocus),"onBlur"in y&&t(3,u=y.onBlur),"size"in y&&t(4,f=y.size),"variant"in y&&t(5,p=y.variant),"showActionButton"in y&&t(6,m=y.showActionButton),"$$scope"in y&&t(19,l=y.$$scope)},t(8,n=g.eventManager.footerAwareFocused),i(),i=Ya(n,y=>t(9,r=y)),[c,d,h,u,f,p,m,()=>g.commandManager.focus(),n,r,g,function(){g.eventManager.handleFooterClick()},a,()=>g.commandManager.requestFocus(),()=>g.commandManager.blur(),()=>g.eventManager.footerAwareFocused,s,function(y){Ze.call(this,o,y)},function(y){Ze.call(this,o,y)},l]}const Zp={Content:class extends oe{constructor(o){super(),ie(this,o,ih,oh,se,{onContentChanged:4,content:5})}},Root:class extends oe{constructor(o){super(),ie(this,o,fh,ph,se,{editable:0,focusOnInit:1,onFocus:2,onBlur:3,size:4,variant:5,showActionButton:6,requestFocus:13,forceFocus:7,blur:14,isFocused:15})}get requestFocus(){return this.$$.ctx[13]}get forceFocus(){return this.$$.ctx[7]}get blur(){return this.$$.ctx[14]}get isFocused(){return this.$$.ctx[15]}}};function mh(o){var e;const{char:t,allowSpaces:n,allowedPrefixes:r,startOfLine:i,$position:s}=o,l=t.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&"),a=new RegExp(`\\s${l}$`),c=i?"^":"",d=n?new RegExp(`${c}${l}.*?(?=\\s${l}|$)`,"gm"):new RegExp(`${c}(?:^)?${l}[^\\s${l}]*`,"gm"),h=((e=s.nodeBefore)===null||e===void 0?void 0:e.isText)&&s.nodeBefore.text;if(!h)return null;const u=s.pos-h.length,f=Array.from(h.matchAll(d)).pop();if(!f||f.input===void 0||f.index===void 0)return null;const p=f.input.slice(Math.max(0,f.index-1),f.index),m=new RegExp(`^[${r==null?void 0:r.join("")}\0]?$`).test(p);if(r!==null&&!m)return null;const g=u+f.index;let y=g+f[0].length;return n&&a.test(h.slice(y-1,y+1))&&(f[0]+=" ",y+=1),g<s.pos&&y>=s.pos?{range:{from:g,to:y},query:f[0].slice(t.length),text:f[0]}:null}const gh=new ze("suggestion");function yh({pluginKey:o=gh,editor:e,char:t="@",allowSpaces:n=!1,allowedPrefixes:r=[" "],startOfLine:i=!1,decorationTag:s="span",decorationClass:l="suggestion",command:a=()=>null,items:c=()=>[],render:d=()=>({}),allow:h=()=>!0,findSuggestionMatch:u=mh}){let f;const p=d==null?void 0:d(),m=new Fe({key:o,view(){return{update:async(g,y)=>{var C,v,b,k,I,T,E;const S=(C=this.key)===null||C===void 0?void 0:C.getState(y),A=(v=this.key)===null||v===void 0?void 0:v.getState(g.state),q=S.active&&A.active&&S.range.from!==A.range.from,H=!S.active&&A.active,J=S.active&&!A.active,z=H||q,U=!H&&!J&&S.query!==A.query&&!q,Q=J||q;if(!z&&!U&&!Q)return;const ne=Q&&!z?S:A,De=g.dom.querySelector(`[data-decoration-id="${ne.decorationId}"]`);f={editor:e,range:ne.range,query:ne.query,text:ne.text,items:[],command:X=>a({editor:e,range:ne.range,props:X}),decorationNode:De,clientRect:De?()=>{var X;const{decorationId:Gt}=(X=this.key)===null||X===void 0?void 0:X.getState(e.state),Zt=g.dom.querySelector(`[data-decoration-id="${Gt}"]`);return(Zt==null?void 0:Zt.getBoundingClientRect())||null}:null},z&&((b=p==null?void 0:p.onBeforeStart)===null||b===void 0||b.call(p,f)),U&&((k=p==null?void 0:p.onBeforeUpdate)===null||k===void 0||k.call(p,f)),(U||z)&&(f.items=await c({editor:e,query:ne.query})),Q&&((I=p==null?void 0:p.onExit)===null||I===void 0||I.call(p,f)),U&&((T=p==null?void 0:p.onUpdate)===null||T===void 0||T.call(p,f)),z&&((E=p==null?void 0:p.onStart)===null||E===void 0||E.call(p,f))},destroy:()=>{var g;f&&((g=p==null?void 0:p.onExit)===null||g===void 0||g.call(p,f))}}},state:{init:()=>({active:!1,range:{from:0,to:0},query:null,text:null,composing:!1}),apply(g,y,C,v){const{isEditable:b}=e,{composing:k}=e.view,{selection:I}=g,{empty:T,from:E}=I,S={...y};if(S.composing=k,b&&(T||e.view.composing)){!(E<y.range.from||E>y.range.to)||k||y.composing||(S.active=!1);const A=u({char:t,allowSpaces:n,allowedPrefixes:r,startOfLine:i,$position:I.$from}),q=`id_${Math.floor(4294967295*Math.random())}`;A&&h({editor:e,state:v,range:A.range})?(S.active=!0,S.decorationId=y.decorationId?y.decorationId:q,S.range=A.range,S.query=A.query,S.text=A.text):S.active=!1}else S.active=!1;return S.active||(S.decorationId=null,S.range={from:0,to:0},S.query=null,S.text=null),S}},props:{handleKeyDown(g,y){var C;const{active:v,range:b}=m.getState(g.state);return v&&((C=p==null?void 0:p.onKeyDown)===null||C===void 0?void 0:C.call(p,{view:g,event:y,range:b}))||!1},decorations(g){const{active:y,range:C,decorationId:v}=m.getState(g);return y?de.create(g.doc,[Pe.inline(C.from,C.to,{nodeName:s,class:l,"data-decoration-id":v})]):null}}});return m}const Ch=new ze("mention"),wh=Ot.create({name:"mention",addOptions(){return{HTMLAttributes:{},renderText({options:o,node:e}){var t;return`${o.suggestion.char}${(t=e.attrs.label)!==null&&t!==void 0?t:e.attrs.id}`},deleteTriggerWithBackspace:!1,renderHTML({options:o,node:e}){var t;return["span",Pt(this.HTMLAttributes,o.HTMLAttributes),`${o.suggestion.char}${(t=e.attrs.label)!==null&&t!==void 0?t:e.attrs.id}`]},suggestion:{char:"@",pluginKey:Ch,command:({editor:o,range:e,props:t})=>{var n,r;const i=o.view.state.selection.$to.nodeAfter;!((n=i==null?void 0:i.text)===null||n===void 0)&&n.startsWith(" ")&&(e.to+=1),o.chain().focus().insertContentAt(e,[{type:this.name,attrs:t},{type:"text",text:" "}]).run(),(r=window.getSelection())===null||r===void 0||r.collapseToEnd()},allow:({state:o,range:e})=>{const t=o.doc.resolve(e.from),n=o.schema.nodes[this.name];return!!t.parent.type.contentMatch.matchType(n)}}}},group:"inline",inline:!0,selectable:!1,atom:!0,addAttributes:()=>({id:{default:null,parseHTML:o=>o.getAttribute("data-id"),renderHTML:o=>o.id?{"data-id":o.id}:{}},label:{default:null,parseHTML:o=>o.getAttribute("data-label"),renderHTML:o=>o.label?{"data-label":o.label}:{}}}),parseHTML(){return[{tag:`span[data-type="${this.name}"]`}]},renderHTML({node:o,HTMLAttributes:e}){if(this.options.renderLabel!==void 0)return console.warn("renderLabel is deprecated use renderText and renderHTML instead"),["span",Pt({"data-type":this.name},this.options.HTMLAttributes,e),this.options.renderLabel({options:this.options,node:o})];const t={...this.options};t.HTMLAttributes=Pt({"data-type":this.name},this.options.HTMLAttributes,e);const n=this.options.renderHTML({options:t,node:o});return typeof n=="string"?["span",Pt({"data-type":this.name},this.options.HTMLAttributes,e),n]:n},renderText({node:o}){return this.options.renderLabel!==void 0?(console.warn("renderLabel is deprecated use renderText and renderHTML instead"),this.options.renderLabel({options:this.options,node:o})):this.options.renderText({options:this.options,node:o})},addKeyboardShortcuts(){return{Backspace:()=>this.editor.commands.command(({tr:o,state:e})=>{let t=!1;const{selection:n}=e,{empty:r,anchor:i}=n;return!!r&&(e.doc.nodesBetween(i-1,i,(s,l)=>{if(s.type.name===this.name)return t=!0,o.insertText(this.options.deleteTriggerWithBackspace?"":this.options.suggestion.char||"",l,l+s.nodeSize),!1}),t)})}},addProseMirrorPlugins(){return[yh({editor:this.editor,...this.options.suggestion})]}});function ct(o){return Array.isArray?Array.isArray(o):_a(o)==="[object Array]"}function tt(o){return typeof o=="string"}function Da(o){return typeof o=="number"}function vh(o){return o===!0||o===!1||function(e){return Ra(e)&&e!==null}(o)&&_a(o)=="[object Boolean]"}function Ra(o){return typeof o=="object"}function Re(o){return o!=null}function Gr(o){return!o.trim().length}function _a(o){return o==null?o===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(o)}const xh=o=>`Missing ${o} property in key`,bh=o=>`Property 'weight' in key '${o}' must be a positive integer`,Ss=Object.prototype.hasOwnProperty;class $h{constructor(e){this._keys=[],this._keyMap={};let t=0;e.forEach(n=>{let r=Pa(n);this._keys.push(r),this._keyMap[r.id]=r,t+=r.weight}),this._keys.forEach(n=>{n.weight/=t})}get(e){return this._keyMap[e]}keys(){return this._keys}toJSON(){return JSON.stringify(this._keys)}}function Pa(o){let e=null,t=null,n=null,r=1,i=null;if(tt(o)||ct(o))n=o,e=Os(o),t=wo(o);else{if(!Ss.call(o,"name"))throw new Error(xh("name"));const s=o.name;if(n=s,Ss.call(o,"weight")&&(r=o.weight,r<=0))throw new Error(bh(s));e=Os(s),t=wo(s),i=o.getFn}return{path:e,id:t,weight:r,src:n,getFn:i}}function Os(o){return ct(o)?o:o.split(".")}function wo(o){return ct(o)?o.join("."):o}const kh={useExtendedSearch:!1,getFn:function(o,e){let t=[],n=!1;const r=(i,s,l)=>{if(Re(i))if(s[l]){const a=i[s[l]];if(!Re(a))return;if(l===s.length-1&&(tt(a)||Da(a)||vh(a)))t.push(function(c){return c==null?"":function(d){if(typeof d=="string")return d;let h=d+"";return h=="0"&&1/d==-1/0?"-0":h}(c)}(a));else if(ct(a)){n=!0;for(let c=0,d=a.length;c<d;c+=1)r(a[c],s,l+1)}else s.length&&r(a,s,l+1)}else t.push(i)};return r(o,tt(e)?e.split("."):e,0),n?t:t[0]},ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1};var K={isCaseSensitive:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:(o,e)=>o.score===e.score?o.idx<e.idx?-1:1:o.score<e.score?-1:1,includeMatches:!1,findAllMatches:!1,minMatchCharLength:1,location:0,threshold:.6,distance:100,...kh};const Mh=/[^ ]+/g;class Vo{constructor({getFn:e=K.getFn,fieldNormWeight:t=K.fieldNormWeight}={}){this.norm=function(n=1,r=3){const i=new Map,s=Math.pow(10,r);return{get(l){const a=l.match(Mh).length;if(i.has(a))return i.get(a);const c=1/Math.pow(a,.5*n),d=parseFloat(Math.round(c*s)/s);return i.set(a,d),d},clear(){i.clear()}}}(t,3),this.getFn=e,this.isCreated=!1,this.setIndexRecords()}setSources(e=[]){this.docs=e}setIndexRecords(e=[]){this.records=e}setKeys(e=[]){this.keys=e,this._keysMap={},e.forEach((t,n)=>{this._keysMap[t.id]=n})}create(){!this.isCreated&&this.docs.length&&(this.isCreated=!0,tt(this.docs[0])?this.docs.forEach((e,t)=>{this._addString(e,t)}):this.docs.forEach((e,t)=>{this._addObject(e,t)}),this.norm.clear())}add(e){const t=this.size();tt(e)?this._addString(e,t):this._addObject(e,t)}removeAt(e){this.records.splice(e,1);for(let t=e,n=this.size();t<n;t+=1)this.records[t].i-=1}getValueForItemAtKeyId(e,t){return e[this._keysMap[t]]}size(){return this.records.length}_addString(e,t){if(!Re(e)||Gr(e))return;let n={v:e,i:t,n:this.norm.get(e)};this.records.push(n)}_addObject(e,t){let n={i:t,$:{}};this.keys.forEach((r,i)=>{let s=r.getFn?r.getFn(e):this.getFn(e,r.path);if(Re(s)){if(ct(s)){let l=[];const a=[{nestedArrIndex:-1,value:s}];for(;a.length;){const{nestedArrIndex:c,value:d}=a.pop();if(Re(d))if(tt(d)&&!Gr(d)){let h={v:d,i:c,n:this.norm.get(d)};l.push(h)}else ct(d)&&d.forEach((h,u)=>{a.push({nestedArrIndex:u,value:h})})}n.$[i]=l}else if(tt(s)&&!Gr(s)){let l={v:s,n:this.norm.get(s)};n.$[i]=l}}}),this.records.push(n)}toJSON(){return{keys:this.keys,records:this.records}}}function za(o,e,{getFn:t=K.getFn,fieldNormWeight:n=K.fieldNormWeight}={}){const r=new Vo({getFn:t,fieldNormWeight:n});return r.setKeys(o.map(Pa)),r.setSources(e),r.create(),r}function Jn(o,{errors:e=0,currentLocation:t=0,expectedLocation:n=0,distance:r=K.distance,ignoreLocation:i=K.ignoreLocation}={}){const s=e/o.length;if(i)return s;const l=Math.abs(n-t);return r?s+l/r:l?1:s}const It=32;function Sh(o,e,t,{location:n=K.location,distance:r=K.distance,threshold:i=K.threshold,findAllMatches:s=K.findAllMatches,minMatchCharLength:l=K.minMatchCharLength,includeMatches:a=K.includeMatches,ignoreLocation:c=K.ignoreLocation}={}){if(e.length>It)throw new Error(`Pattern length exceeds max of ${It}.`);const d=e.length,h=o.length,u=Math.max(0,Math.min(n,h));let f=i,p=u;const m=l>1||a,g=m?Array(h):[];let y;for(;(y=o.indexOf(e,p))>-1;){let T=Jn(e,{currentLocation:y,expectedLocation:u,distance:r,ignoreLocation:c});if(f=Math.min(T,f),p=y+d,m){let E=0;for(;E<d;)g[y+E]=1,E+=1}}p=-1;let C=[],v=1,b=d+h;const k=1<<d-1;for(let T=0;T<d;T+=1){let E=0,S=b;for(;E<S;)Jn(e,{errors:T,currentLocation:u+S,expectedLocation:u,distance:r,ignoreLocation:c})<=f?E=S:b=S,S=Math.floor((b-E)/2+E);b=S;let A=Math.max(1,u-S+1),q=s?h:Math.min(u+S,h)+d,H=Array(q+2);H[q+1]=(1<<T)-1;for(let J=q;J>=A;J-=1){let z=J-1,U=t[o.charAt(z)];if(m&&(g[z]=+!!U),H[J]=(H[J+1]<<1|1)&U,T&&(H[J]|=(C[J+1]|C[J])<<1|1|C[J+1]),H[J]&k&&(v=Jn(e,{errors:T,currentLocation:z,expectedLocation:u,distance:r,ignoreLocation:c}),v<=f)){if(f=v,p=z,p<=u)break;A=Math.max(1,2*u-p)}}if(Jn(e,{errors:T+1,currentLocation:u,expectedLocation:u,distance:r,ignoreLocation:c})>f)break;C=H}const I={isMatch:p>=0,score:Math.max(.001,v)};if(m){const T=function(E=[],S=K.minMatchCharLength){let A=[],q=-1,H=-1,J=0;for(let z=E.length;J<z;J+=1){let U=E[J];U&&q===-1?q=J:U||q===-1||(H=J-1,H-q+1>=S&&A.push([q,H]),q=-1)}return E[J-1]&&J-q>=S&&A.push([q,J-1]),A}(g,l);T.length?a&&(I.indices=T):I.isMatch=!1}return I}function Oh(o){let e={};for(let t=0,n=o.length;t<n;t+=1){const r=o.charAt(t);e[r]=(e[r]||0)|1<<n-t-1}return e}class Fa{constructor(e,{location:t=K.location,threshold:n=K.threshold,distance:r=K.distance,includeMatches:i=K.includeMatches,findAllMatches:s=K.findAllMatches,minMatchCharLength:l=K.minMatchCharLength,isCaseSensitive:a=K.isCaseSensitive,ignoreLocation:c=K.ignoreLocation}={}){if(this.options={location:t,threshold:n,distance:r,includeMatches:i,findAllMatches:s,minMatchCharLength:l,isCaseSensitive:a,ignoreLocation:c},this.pattern=a?e:e.toLowerCase(),this.chunks=[],!this.pattern.length)return;const d=(u,f)=>{this.chunks.push({pattern:u,alphabet:Oh(u),startIndex:f})},h=this.pattern.length;if(h>It){let u=0;const f=h%It,p=h-f;for(;u<p;)d(this.pattern.substr(u,It),u),u+=It;if(f){const m=h-It;d(this.pattern.substr(m),m)}}else d(this.pattern,0)}searchIn(e){const{isCaseSensitive:t,includeMatches:n}=this.options;if(t||(e=e.toLowerCase()),this.pattern===e){let p={isMatch:!0,score:0};return n&&(p.indices=[[0,e.length-1]]),p}const{location:r,distance:i,threshold:s,findAllMatches:l,minMatchCharLength:a,ignoreLocation:c}=this.options;let d=[],h=0,u=!1;this.chunks.forEach(({pattern:p,alphabet:m,startIndex:g})=>{const{isMatch:y,score:C,indices:v}=Sh(e,p,m,{location:r+g,distance:i,threshold:s,findAllMatches:l,minMatchCharLength:a,includeMatches:n,ignoreLocation:c});y&&(u=!0),h+=C,y&&v&&(d=[...d,...v])});let f={isMatch:u,score:u?h/this.chunks.length:1};return u&&n&&(f.indices=d),f}}class mt{constructor(e){this.pattern=e}static isMultiMatch(e){return Es(e,this.multiRegex)}static isSingleMatch(e){return Es(e,this.singleRegex)}search(){}}function Es(o,e){const t=o.match(e);return t?t[1]:null}class Ba extends mt{constructor(e,{location:t=K.location,threshold:n=K.threshold,distance:r=K.distance,includeMatches:i=K.includeMatches,findAllMatches:s=K.findAllMatches,minMatchCharLength:l=K.minMatchCharLength,isCaseSensitive:a=K.isCaseSensitive,ignoreLocation:c=K.ignoreLocation}={}){super(e),this._bitapSearch=new Fa(e,{location:t,threshold:n,distance:r,includeMatches:i,findAllMatches:s,minMatchCharLength:l,isCaseSensitive:a,ignoreLocation:c})}static get type(){return"fuzzy"}static get multiRegex(){return/^"(.*)"$/}static get singleRegex(){return/^(.*)$/}search(e){return this._bitapSearch.searchIn(e)}}class Va extends mt{constructor(e){super(e)}static get type(){return"include"}static get multiRegex(){return/^'"(.*)"$/}static get singleRegex(){return/^'(.*)$/}search(e){let t,n=0;const r=[],i=this.pattern.length;for(;(t=e.indexOf(this.pattern,n))>-1;)n=t+i,r.push([t,n-1]);const s=!!r.length;return{isMatch:s,score:s?0:1,indices:r}}}const vo=[class extends mt{constructor(o){super(o)}static get type(){return"exact"}static get multiRegex(){return/^="(.*)"$/}static get singleRegex(){return/^=(.*)$/}search(o){const e=o===this.pattern;return{isMatch:e,score:e?0:1,indices:[0,this.pattern.length-1]}}},Va,class extends mt{constructor(o){super(o)}static get type(){return"prefix-exact"}static get multiRegex(){return/^\^"(.*)"$/}static get singleRegex(){return/^\^(.*)$/}search(o){const e=o.startsWith(this.pattern);return{isMatch:e,score:e?0:1,indices:[0,this.pattern.length-1]}}},class extends mt{constructor(o){super(o)}static get type(){return"inverse-prefix-exact"}static get multiRegex(){return/^!\^"(.*)"$/}static get singleRegex(){return/^!\^(.*)$/}search(o){const e=!o.startsWith(this.pattern);return{isMatch:e,score:e?0:1,indices:[0,o.length-1]}}},class extends mt{constructor(o){super(o)}static get type(){return"inverse-suffix-exact"}static get multiRegex(){return/^!"(.*)"\$$/}static get singleRegex(){return/^!(.*)\$$/}search(o){const e=!o.endsWith(this.pattern);return{isMatch:e,score:e?0:1,indices:[0,o.length-1]}}},class extends mt{constructor(o){super(o)}static get type(){return"suffix-exact"}static get multiRegex(){return/^"(.*)"\$$/}static get singleRegex(){return/^(.*)\$$/}search(o){const e=o.endsWith(this.pattern);return{isMatch:e,score:e?0:1,indices:[o.length-this.pattern.length,o.length-1]}}},class extends mt{constructor(o){super(o)}static get type(){return"inverse-exact"}static get multiRegex(){return/^!"(.*)"$/}static get singleRegex(){return/^!(.*)$/}search(o){const e=o.indexOf(this.pattern)===-1;return{isMatch:e,score:e?0:1,indices:[0,o.length-1]}}},Ba],Ts=vo.length,Eh=/ +(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)/,Th=new Set([Ba.type,Va.type]);class Nh{constructor(e,{isCaseSensitive:t=K.isCaseSensitive,includeMatches:n=K.includeMatches,minMatchCharLength:r=K.minMatchCharLength,ignoreLocation:i=K.ignoreLocation,findAllMatches:s=K.findAllMatches,location:l=K.location,threshold:a=K.threshold,distance:c=K.distance}={}){this.query=null,this.options={isCaseSensitive:t,includeMatches:n,minMatchCharLength:r,findAllMatches:s,ignoreLocation:i,location:l,threshold:a,distance:c},this.pattern=t?e:e.toLowerCase(),this.query=function(d,h={}){return d.split("|").map(u=>{let f=u.trim().split(Eh).filter(m=>m&&!!m.trim()),p=[];for(let m=0,g=f.length;m<g;m+=1){const y=f[m];let C=!1,v=-1;for(;!C&&++v<Ts;){const b=vo[v];let k=b.isMultiMatch(y);k&&(p.push(new b(k,h)),C=!0)}if(!C)for(v=-1;++v<Ts;){const b=vo[v];let k=b.isSingleMatch(y);if(k){p.push(new b(k,h));break}}}return p})}(this.pattern,this.options)}static condition(e,t){return t.useExtendedSearch}searchIn(e){const t=this.query;if(!t)return{isMatch:!1,score:1};const{includeMatches:n,isCaseSensitive:r}=this.options;e=r?e:e.toLowerCase();let i=0,s=[],l=0;for(let a=0,c=t.length;a<c;a+=1){const d=t[a];s.length=0,i=0;for(let h=0,u=d.length;h<u;h+=1){const f=d[h],{isMatch:p,indices:m,score:g}=f.search(e);if(!p){l=0,i=0,s.length=0;break}if(i+=1,l+=g,n){const y=f.constructor.type;Th.has(y)?s=[...s,...m]:s.push(m)}}if(i){let h={isMatch:!0,score:l/i};return n&&(h.indices=s),h}}return{isMatch:!1,score:1}}}const xo=[];function bo(o,e){for(let t=0,n=xo.length;t<n;t+=1){let r=xo[t];if(r.condition(o,e))return new r(o,e)}return new Fa(o,e)}const Ho="$and",Lh="$or",Ns="$path",Ah="$val",Zr=o=>!(!o[Ho]&&!o[Lh]),Ls=o=>({[Ho]:Object.keys(o).map(e=>({[e]:o[e]}))});function Ha(o,e,{auto:t=!0}={}){const n=r=>{let i=Object.keys(r);const s=(a=>!!a[Ns])(r);if(!s&&i.length>1&&!Zr(r))return n(Ls(r));if((a=>!ct(a)&&Ra(a)&&!Zr(a))(r)){const a=s?r[Ns]:i[0],c=s?r[Ah]:r[a];if(!tt(c))throw new Error((h=>`Invalid value for key ${h}`)(a));const d={keyId:wo(a),pattern:c};return t&&(d.searcher=bo(c,e)),d}let l={children:[],operator:i[0]};return i.forEach(a=>{const c=r[a];ct(c)&&c.forEach(d=>{l.children.push(n(d))})}),l};return Zr(o)||(o=Ls(o)),n(o)}function Ih(o,e){const t=o.matches;e.matches=[],Re(t)&&t.forEach(n=>{if(!Re(n.indices)||!n.indices.length)return;const{indices:r,value:i}=n;let s={indices:r,value:i};n.key&&(s.key=n.key.src),n.idx>-1&&(s.refIndex=n.idx),e.matches.push(s)})}function Dh(o,e){e.score=o.score}class nn{constructor(e,t={},n){this.options={...K,...t},this.options.useExtendedSearch,this._keyStore=new $h(this.options.keys),this.setCollection(e,n)}setCollection(e,t){if(this._docs=e,t&&!(t instanceof Vo))throw new Error("Incorrect 'index' type");this._myIndex=t||za(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}add(e){Re(e)&&(this._docs.push(e),this._myIndex.add(e))}remove(e=()=>!1){const t=[];for(let n=0,r=this._docs.length;n<r;n+=1){const i=this._docs[n];e(i,n)&&(this.removeAt(n),n-=1,r-=1,t.push(i))}return t}removeAt(e){this._docs.splice(e,1),this._myIndex.removeAt(e)}getIndex(){return this._myIndex}search(e,{limit:t=-1}={}){const{includeMatches:n,includeScore:r,shouldSort:i,sortFn:s,ignoreFieldNorm:l}=this.options;let a=tt(e)?tt(this._docs[0])?this._searchStringList(e):this._searchObjectList(e):this._searchLogical(e);return function(c,{ignoreFieldNorm:d=K.ignoreFieldNorm}){c.forEach(h=>{let u=1;h.matches.forEach(({key:f,norm:p,score:m})=>{const g=f?f.weight:null;u*=Math.pow(m===0&&g?Number.EPSILON:m,(g||1)*(d?1:p))}),h.score=u})}(a,{ignoreFieldNorm:l}),i&&a.sort(s),Da(t)&&t>-1&&(a=a.slice(0,t)),function(c,d,{includeMatches:h=K.includeMatches,includeScore:u=K.includeScore}={}){const f=[];return h&&f.push(Ih),u&&f.push(Dh),c.map(p=>{const{idx:m}=p,g={item:d[m],refIndex:m};return f.length&&f.forEach(y=>{y(p,g)}),g})}(a,this._docs,{includeMatches:n,includeScore:r})}_searchStringList(e){const t=bo(e,this.options),{records:n}=this._myIndex,r=[];return n.forEach(({v:i,i:s,n:l})=>{if(!Re(i))return;const{isMatch:a,score:c,indices:d}=t.searchIn(i);a&&r.push({item:i,idx:s,matches:[{score:c,value:i,norm:l,indices:d}]})}),r}_searchLogical(e){const t=Ha(e,this.options),n=(l,a,c)=>{if(!l.children){const{keyId:h,searcher:u}=l,f=this._findMatches({key:this._keyStore.get(h),value:this._myIndex.getValueForItemAtKeyId(a,h),searcher:u});return f&&f.length?[{idx:c,item:a,matches:f}]:[]}const d=[];for(let h=0,u=l.children.length;h<u;h+=1){const f=l.children[h],p=n(f,a,c);if(p.length)d.push(...p);else if(l.operator===Ho)return[]}return d},r=this._myIndex.records,i={},s=[];return r.forEach(({$:l,i:a})=>{if(Re(l)){let c=n(t,l,a);c.length&&(i[a]||(i[a]={idx:a,item:l,matches:[]},s.push(i[a])),c.forEach(({matches:d})=>{i[a].matches.push(...d)}))}}),s}_searchObjectList(e){const t=bo(e,this.options),{keys:n,records:r}=this._myIndex,i=[];return r.forEach(({$:s,i:l})=>{if(!Re(s))return;let a=[];n.forEach((c,d)=>{a.push(...this._findMatches({key:c,value:s[d],searcher:t}))}),a.length&&i.push({idx:l,item:s,matches:a})}),i}_findMatches({key:e,value:t,searcher:n}){if(!Re(t))return[];let r=[];if(ct(t))t.forEach(({v:i,i:s,n:l})=>{if(!Re(i))return;const{isMatch:a,score:c,indices:d}=n.searchIn(i);a&&r.push({score:c,key:e,value:i,idx:s,norm:l,indices:d})});else{const{v:i,n:s}=t,{isMatch:l,score:a,indices:c}=n.searchIn(i);l&&r.push({score:a,key:e,value:i,norm:s,indices:c})}return r}}function As(o,e){const t=[];return e.$doc.content.descendants(n=>{n.type.name===o&&t.push(n)}),t}function Is(o,e,t,n={}){const r=[],i=(s,l)=>{var a;(a=n.onNewMention)==null||a.call(n,s,l),r.push(s)};return e.descendants((s,l)=>{if(s.type.name!==o)return;if(t===null)return void i(s,l);const a=Math.abs(e.nodeSize-t.nodeSize),c=t.nodeSize-2,d=a+s.nodeSize+1,h=Math.max(0,l-d),u=Math.min(c,l+d);let f=!1;t.nodesBetween(h,u,p=>{var m;if(p.type.name===o&&p.attrs.id===s.attrs.id)return f=!0,(m=n.onExistingMention)==null||m.call(n,p,l),!1}),f||i(s,l)}),r}function ja(o,e){const t=new MutationObserver(n=>{for(const r of n)if(e.shouldTrigger(r))return e.callback(),void t.disconnect()});return t.observe(o,{childList:!0,subtree:!0,...e.mutationObserverInit}),e.timeout&&setTimeout(()=>t.disconnect(),e.timeout),()=>t.disconnect()}function Ds(o,e,t,n){return ja(o,{shouldTrigger:r=>new Set(r.addedNodes).has(e),callback:t,timeout:n})}nn.version="7.0.0",nn.createIndex=za,nn.parseIndex=function(o,{getFn:e=K.getFn,fieldNormWeight:t=K.fieldNormWeight}={}){const{keys:n,records:r}=o,i=new Vo({getFn:e,fieldNormWeight:t});return i.setKeys(n),i.setIndexRecords(r),i},nn.config=K,nn.parseQuery=Ha,function(...o){xo.push(...o)}(Nh);class sn{constructor(e){x(this,"_disposers",[]);x(this,"_editor");x(this,"_tooltipData",ke(void 0));x(this,"onCreate",e=>{this._editor=e});x(this,"onDispose",()=>{this._editor=void 0,this._disposers.forEach(e=>e()),this._disposers=[]});x(this,"createMentionChip",({options:e,node:t})=>{var i,s;if(t.type.name!==this._mentionPluginId)throw new Error("Expected a mention node");const n=document.createElement("span");n.innerText=`${((i=e.suggestion)==null?void 0:i.char)??"@"}${t.attrs.label}`;for(const[l,a]of Object.entries(e.HTMLAttributes))typeof a=="string"?n.setAttribute(l,a.toString()):console.warn(`Unexpected HTML attribute value type: [${l}] = ${a}`);const r=(s=this._editor)==null?void 0:s.view.dom;return r&&this._setupMountUnmountCycle(r,n,()=>{this._tooltipData.set({data:t.attrs.data,anchorElement:n})}),n});x(this,"hideTooltip",()=>{this._tooltipData.set(void 0)});this._mentionPluginId=e}get tooltipData(){return this._tooltipData}_attachChipEventListeners(e,t){e.addEventListener("mouseenter",t),e.addEventListener("mouseover",t),e.addEventListener("mouseleave",this.hideTooltip)}_detachChipEventListeners(e,t){e.removeEventListener("mouseenter",t),e.removeEventListener("mouseover",t),e.removeEventListener("mouseleave",this.hideTooltip)}_removeFromDisposers(e){e&&(this._disposers=this._disposers.filter(t=>t!==e))}_setupMountUnmountCycle(e,t,n){let r,i;const s=()=>{this._removeFromDisposers(r),this._detachChipEventListeners(t,n),i=Ds(e,t,l),this._disposers.push(i)},l=()=>{var a,c;this._removeFromDisposers(i),this._attachChipEventListeners(t,n),a=t,r=ja(e,{shouldTrigger:d=>new Set(d.removedNodes).has(a),callback:s,timeout:c}),this._disposers.push(r)};Ds(e,t,l)}}x(sn,"CHIP_CLASS_NAME","c-mention-chip"),x(sn,"CHIP_DATA_ATTR_KEY","data-augment-mention-chip-tooltip");class Rh{constructor(e={}){x(this,"_menuData",ke(void 0));x(this,"_mentionables",ke([]));x(this,"query",Ke(this._menuData,e=>e==null?void 0:e.tiptapExtensionProps.query));x(this,"activeIdx",Ke([this._mentionables,this._menuData],Ph));x(this,"activeItem",Ke([this._mentionables,this.activeIdx],_h));x(this,"referenceClientRect",Ke(this._menuData,e=>e==null?void 0:e.referenceClientRect));x(this,"tiptapExtensionProps",Ke(this._menuData,e=>e==null?void 0:e.tiptapExtensionProps));x(this,"isMenuActive",Ke(this._menuData,e=>e!==void 0));x(this,"mentionables",this._mentionables);x(this,"updateOptions",e=>{this._opts={...this._opts,...e}});x(this,"updateMentionables",e=>{this._mentionables.set(e)});x(this,"replaceQueryWithMentionNode",e=>{var r;const t=re(this.isMenuActive),n=(r=re(this.tiptapExtensionProps))==null?void 0:r.command;return!(!t||!n)&&(n(e),!0)});x(this,"selectItem",e=>{var n,r;return!(re(this._mentionables).findIndex(i=>i.id===e.id)===-1||!this._opts.onSelectMentionable)&&((r=(n=this._opts).onSelectMentionable)==null||r.call(n,e),!0)});x(this,"_incrementActiveIdx",()=>{const e=re(this.activeIdx)??0;this._setActiveIdx(e+1)});x(this,"_decrementActiveIdx",()=>{const e=re(this.activeIdx)??0;this._setActiveIdx(e-1)});x(this,"_setActiveIdx",e=>{this._menuData.update(t=>t&&{...t,activeIdx:e})});x(this,"onUpdateSuggestion",e=>{var n;const t=(n=e.clientRect)==null?void 0:n.call(e);t&&this._menuData.update(r=>({referenceClientRect:t,tiptapExtensionProps:e,activeIdx:(r==null?void 0:r.activeIdx)??0}))});x(this,"exitMenu",()=>{this._menuData.set(void 0),this._mentionables.set([])});x(this,"_handleKeyIfActive",e=>()=>!!re(this.isMenuActive)&&(e(),!0));x(this,"onArrowUp",this._handleKeyIfActive(this._decrementActiveIdx));x(this,"onArrowDown",this._handleKeyIfActive(this._incrementActiveIdx));x(this,"selectActiveItem",this._handleKeyIfActive(()=>{const e=re(this.activeItem);return!!e&&this.selectItem(e)}));this._opts=e}}function _h([o,e]){if(e!==void 0&&o.length!==0)return o[e]}function Ph([o,e]){if(!((e==null?void 0:e.activeIdx)===void 0||o.length===0))return(e.activeIdx%o.length+o.length)%o.length%o.length}const st=class st{constructor(e){x(this,"_triggerCharacter");x(this,"_editor");x(this,"_mention");x(this,"_chipController");x(this,"_mentionableMenuContext",new Rh);x(this,"insertMentionNode",e=>!!this._editor&&(this._mentionableMenuContext.replaceQueryWithMentionNode(e.data)||this._editor.commands.insertContent({type:this._mentionPluginId,attrs:e}),!0));x(this,"_onCreate",e=>{var n,r;const t=As(this._mentionPluginId,e).map(i=>i.attrs.data);(r=(n=this._options).onMentionItemsUpdated)==null||r.call(n,{added:t,removed:[],current:t}),this._editor=e,this._chipController.onCreate(e)});x(this,"_onDestroy",()=>{var e,t;if(this._editor){const n=As(this._mentionPluginId,this._editor).map(r=>r.attrs.data);(t=(e=this._options).onMentionItemsUpdated)==null||t.call(e,{added:[],removed:n,current:[]}),this._editor=void 0,this._chipController.onDispose()}this._chipController.hideTooltip(),this._mentionableMenuContext.exitMenu()});x(this,"_onProseMirrorUpdate",(e,t)=>{var l,a;if(e===t)return;const n=c=>c.attrs.data,r=[],i=Is(this._mentionPluginId,e,t).map(n),s=Is(this._mentionPluginId,t,e,{onNewMention:c=>r.push(n(c)),onExistingMention:c=>r.push(n(c))}).map(n);(a=(l=this._options).onMentionItemsUpdated)==null||a.call(l,{added:s,removed:i,current:r})});this._options=e;const t=e.triggerCharacter??"@";this._triggerCharacter=t;const n=new ze(this._mentionListenerPluginId),r=new ze(this._mentionPluginId),i=e.allowedPrefixes??[" ","	",`
`],s=e.renderText??(g=>`${t}${g.name??g.id}`);this._chipController=new sn(this._mentionPluginId);const l=this._onCreate.bind(this),a=this._onProseMirrorUpdate.bind(this),c=this._onDestroy.bind(this),d=this._chipController.createMentionChip,h=this._mentionableMenuContext.onUpdateSuggestion,u=this._mentionableMenuContext.exitMenu,f=this._mentionableMenuContext.onArrowUp,p=this._mentionableMenuContext.onArrowDown,m=this._mentionableMenuContext.selectActiveItem;this._mention=wh.extend({name:this._mentionPluginId,onCreate(){l(this.editor)},onDestroy(){c()},addKeyboardShortcuts:()=>({ArrowUp:f,ArrowDown:p,Enter:m,Tab:m}),addAttributes(){var g;return{...(g=this.parent)==null?void 0:g.call(this),data:{default:null,keepOnSplit:!1,parseHTML:y=>{const C=y.getAttribute(sn.CHIP_DATA_ATTR_KEY);return C?JSON.parse(C):null},renderHTML:y=>y.data?{[sn.CHIP_DATA_ATTR_KEY]:JSON.stringify(y.data)}:{}}}},addProseMirrorPlugins(){var g;return[...((g=this.parent)==null?void 0:g.call(this))??[],new Fe({key:n,view:()=>({update:(y,C)=>{a(C.doc,y.state.doc)}})})]},addOptions(){var y;const g=(y=this.parent)==null?void 0:y.call(this);return{...g,HTMLAttributes:{class:sn.CHIP_CLASS_NAME},renderHTML:d,renderText:({node:C})=>{const v=C.attrs.data;if(!v){const b={id:C.attrs.id||"unknown",label:C.attrs.label||"Unknown mention",name:C.attrs.label||C.attrs.id||"unknown"};return console.warn("Mention data missing, using fallback:",b),s(b)}return s(v)},suggestion:{...g==null?void 0:g.suggestion,pluginKey:r,char:t,allowedPrefixes:i,command:({editor:C,range:v,props:b})=>{var k,I;C&&v&&((I=(k=g.suggestion)==null?void 0:k.command)==null||I.call(k,{editor:C,range:v,props:{id:b.id,name:b.name??b.id,label:b.label,data:b}}))},render:()=>({onStart:h,onUpdate:h,onExit:u})}}}})}get chipController(){return this._chipController}get mentionableMenuContext(){return this._mentionableMenuContext}get _mentionPluginId(){return this._options.pluginId?this._options.pluginId:this._triggerCharacter==="@"?st.DEFAULT_MENTION_PLUGIN_ID:st.MENTION_PLUGIN_ID_BASE.replace("{}",this._triggerCharacter)}get _mentionListenerPluginId(){return st.MENTION_LISTENER_PLUGIN_ID_BASE.replace("{}",this._mentionPluginId)}get tipTapExtension(){return this._mention}};x(st,"CONTEXT_KEY","augment-svelte-mention-plugin"),x(st,"MENTION_LISTENER_PLUGIN_ID_BASE","{}-listener"),x(st,"MENTION_PLUGIN_ID_BASE","augment-prosemirror-mention-{}"),x(st,"DEFAULT_MENTION_PLUGIN_ID","mention");let Cn=st;const zh=o=>({mentionable:2&o}),Rs=o=>({mentionable:o[1].data}),Fh=o=>({mentionable:2&o}),_s=o=>({mentionable:o[1].data});function Ps(o){let e;const t=o[4].mentionable,n=Te(t,o,o[6],_s);return{c(){n&&n.c()},m(r,i){n&&n.m(r,i),e=!0},p(r,i){n&&n.p&&(!e||66&i)&&Ne(n,t,r,r[6],e?Ae(t,r[6],i,Fh):Le(r[6]),_s)},i(r){e||($(n,r),e=!0)},o(r){M(n,r),e=!1},d(r){n&&n.d(r)}}}function zs(o){let e;const t=o[4].default,n=Te(t,o,o[6],Rs);return{c(){n&&n.c()},m(r,i){n&&n.m(r,i),e=!0},p(r,i){n&&n.p&&(!e||66&i)&&Ne(n,t,r,r[6],e?Ae(t,r[6],i,zh):Le(r[6]),Rs)},i(r){e||($(n,r),e=!0)},o(r){M(n,r),e=!1},d(r){n&&n.d(r)}}}function Bh(o){let e,t,n,r=o[1]&&o[3].mentionable&&Ps(o),i=o[1]&&o[3].default&&zs(o);return{c(){r&&r.c(),e=le(),i&&i.c(),t=dt()},m(s,l){r&&r.m(s,l),V(s,e,l),i&&i.m(s,l),V(s,t,l),n=!0},p(s,l){s[1]&&s[3].mentionable?r?(r.p(s,l),10&l&&$(r,1)):(r=Ps(s),r.c(),$(r,1),r.m(e.parentNode,e)):r&&(ae(),M(r,1,1,()=>{r=null}),ce()),s[1]&&s[3].default?i?(i.p(s,l),10&l&&$(i,1)):(i=zs(s),i.c(),$(i,1),i.m(t.parentNode,t)):i&&(ae(),M(i,1,1,()=>{i=null}),ce())},i(s){n||($(r),$(i),n=!0)},o(s){M(r),M(i),n=!1},d(s){s&&(B(e),B(t)),r&&r.d(s),i&&i.d(s)}}}function Vh(o){var r;let e,t,n={triggerOn:[],referenceClientRect:(r=o[1])==null?void 0:r.anchorElement.getBoundingClientRect(),$$slots:{content:[Bh]},$$scope:{ctx:o}};return e=new cc({props:n}),o[5](e),{c(){P(e.$$.fragment)},m(i,s){_(e,i,s),t=!0},p(i,[s]){var a;const l={};2&s&&(l.referenceClientRect=(a=i[1])==null?void 0:a.anchorElement.getBoundingClientRect()),74&s&&(l.$$scope={dirty:s,ctx:i}),e.$set(l)},i(i){t||($(e.$$.fragment,i),t=!0)},o(i){M(e.$$.fragment,i),t=!1},d(i){o[5](null),R(e,i)}}}function Hh(o,e,t){let n,{$$slots:r={},$$scope:i}=e;const s=So(r),l=nt(Cn.CONTEXT_KEY).chipController.tooltipData;let a;return Ct(o,l,c=>t(1,n=c)),o.$$set=c=>{"$$scope"in c&&t(6,i=c.$$scope)},o.$$.update=()=>{3&o.$$.dirty&&(n===void 0?a==null||a.requestClose():a==null||a.requestOpen())},[a,n,l,s,r,function(c){Oo[c?"unshift":"push"](()=>{a=c,t(0,a)})},i]}function jh(o){let e;const t=o[7].default,n=Te(t,o,o[6],null);return{c(){n&&n.c()},m(r,i){n&&n.m(r,i),e=!0},p(r,[i]){n&&n.p&&(!e||64&i)&&Ne(n,t,r,r[6],e?Ae(t,r[6],i,null):Le(r[6]),null)},i(r){e||($(n,r),e=!0)},o(r){M(n,r),e=!1},d(r){n&&n.d(r)}}}function qh(o,e,t){let{$$slots:n={},$$scope:r}=e,{onMentionItemsUpdated:i}=e,{triggerCharacter:s}=e,{allowedPrefixes:l}=e,{pluginId:a}=e,{renderText:c}=e;const d=new Cn({onMentionItemsUpdated:u=>i==null?void 0:i(u),triggerCharacter:s,allowedPrefixes:l,pluginId:a,renderText:c});el(Cn.CONTEXT_KEY,d);const h=nt(Ge.CONTEXT_KEY).pluginManager.registerPlugin(d);return Pn(h),o.$$set=u=>{"onMentionItemsUpdated"in u&&t(0,i=u.onMentionItemsUpdated),"triggerCharacter"in u&&t(1,s=u.triggerCharacter),"allowedPrefixes"in u&&t(2,l=u.allowedPrefixes),"pluginId"in u&&t(3,a=u.pluginId),"renderText"in u&&t(4,c=u.renderText),"$$scope"in u&&t(6,r=u.$$scope)},[i,s,l,a,c,u=>{d.insertMentionNode({id:u.id,label:u.label,data:u})},r,n]}const Kh=o=>({activeItem:16&o,query:1&o}),Fs=o=>({activeItem:o[4],query:o[0]});function Wh(o){let e;const t=o[17].default,n=Te(t,o,o[19],Fs);return{c(){n&&n.c()},m(r,i){n&&n.m(r,i),e=!0},p(r,i){n&&n.p&&(!e||524305&i)&&Ne(n,t,r,r[19],e?Ae(t,r[19],i,Kh):Le(r[19]),Fs)},i(r){e||($(n,r),e=!0)},o(r){M(n,r),e=!1},d(r){n&&n.d(r)}}}function Jh(o){let e,t,n,r;return e=new ln.Trigger({props:{referenceClientRect:o[3]}}),n=new ln.Content({props:{side:"top",align:"start",size:1,onClickOutside:o[9],onEscapeKeyDown:o[9],$$slots:{default:[Wh]},$$scope:{ctx:o}}}),{c(){P(e.$$.fragment),t=le(),P(n.$$.fragment)},m(i,s){_(e,i,s),V(i,t,s),_(n,i,s),r=!0},p(i,s){const l={};8&s&&(l.referenceClientRect=i[3]),e.$set(l);const a={};524305&s&&(a.$$scope={dirty:s,ctx:i}),n.$set(a)},i(i){r||($(e.$$.fragment,i),$(n.$$.fragment,i),r=!0)},o(i){M(e.$$.fragment,i),M(n.$$.fragment,i),r=!1},d(i){i&&B(t),R(e,i),R(n,i)}}}function Uh(o){let e,t,n={open:o[2],onOpenChange:o[10],$$slots:{default:[Jh]},$$scope:{ctx:o}};return e=new ln.Root({props:n}),o[18](e),{c(){P(e.$$.fragment)},m(r,i){_(e,r,i),t=!0},p(r,[i]){const s={};4&i&&(s.open=r[2]),524313&i&&(s.$$scope={dirty:i,ctx:r}),e.$set(s)},i(r){t||($(e.$$.fragment,r),t=!0)},o(r){M(e.$$.fragment,r),t=!1},d(r){o[18](null),R(e,r)}}}function Gh(o,e,t){let n,r,i,s,l,{$$slots:a={},$$scope:c}=e,{mentionables:d}=e,{onQueryUpdate:h}=e,{onSelectMentionable:u=k=>(p.insertMentionNode({...k,data:k}),!0)}=e;const f=nt(Ge.CONTEXT_KEY),p=nt(Cn.CONTEXT_KEY),m=p.mentionableMenuContext,{referenceClientRect:g,query:y,activeItem:C,isMenuActive:v,exitMenu:b}=m;return Ct(o,g,k=>t(3,i=k)),Ct(o,y,k=>t(0,n=k)),Ct(o,C,k=>t(4,s=k)),Ct(o,v,k=>t(2,r=k)),o.$$set=k=>{"mentionables"in k&&t(11,d=k.mentionables),"onQueryUpdate"in k&&t(12,h=k.onQueryUpdate),"onSelectMentionable"in k&&t(13,u=k.onSelectMentionable),"$$scope"in k&&t(19,c=k.$$scope)},o.$$.update=()=>{4097&o.$$.dirty&&h(n),8192&o.$$.dirty&&m.updateOptions({onSelectMentionable:u}),2048&o.$$.dirty&&m.updateMentionables(d)},[n,l,r,i,s,g,y,C,v,b,async k=>{k||f.commandManager.requestFocus()},d,h,u,()=>l==null?void 0:l.requestOpen(),()=>l==null?void 0:l.requestClose(),k=>l==null?void 0:l.focusIdx(k),a,function(k){Oo[k?"unshift":"push"](()=>{l=k,t(1,l)})},c]}const $o={ChipTooltip:class extends oe{constructor(o){super(),ie(this,o,Hh,Vh,se,{})}},Root:class extends oe{constructor(o){super(),ie(this,o,qh,jh,se,{onMentionItemsUpdated:0,triggerCharacter:1,allowedPrefixes:2,pluginId:3,renderText:4,insertMention:5})}get insertMention(){return this.$$.ctx[5]}},Menu:{Root:class extends oe{constructor(o){super(),ie(this,o,Gh,Uh,se,{mentionables:11,onQueryUpdate:12,onSelectMentionable:13,requestOpen:14,requestClose:15,focusIdx:16})}get requestOpen(){return this.$$.ctx[14]}get requestClose(){return this.$$.ctx[15]}get focusIdx(){return this.$$.ctx[16]}}}},gt=class gt{constructor(e,t){x(this,"_disposers",[]);x(this,"_allMentionables",ke([]));x(this,"_breadcrumbIds",ke([]));x(this,"_userQuery",ke(""));x(this,"_active",ke(!1));x(this,"_allGroups",Ke([this._active,this._allMentionables],([e,t])=>e?hc(t):[]));x(this,"_currentGroup",Ke([this._breadcrumbIds,this._allGroups],([e,t])=>{if(e.length===0)return;const n=e[e.length-1];return t.find(r=>Ln(r)&&r.id===n)}));x(this,"dispose",()=>{for(const e of this._disposers)e()});x(this,"openDropdown",()=>{this._active.set(!0)});x(this,"closeDropdown",()=>{this._active.set(!1),this._resetState()});x(this,"toggleDropdown",()=>re(this._active)?(this.closeDropdown(),!1):(this.openDropdown(),!0));x(this,"pushBreadcrumb",e=>{re(this._active)&&this._breadcrumbIds.update(t=>[...t,e.id])});x(this,"popBreadcrumb",()=>{re(this._active)&&this._breadcrumbIds.update(e=>e.slice(0,-1))});x(this,"selectMentionable",e=>{var r;const t=this._chatModel.extensionClient,n=this._chatModel.specialContextInputModel;return Ln(e)&&e.type==="breadcrumb"?(this.pushBreadcrumb(e),!0):e.type==="breadcrumb-back"?(this.popBreadcrumb(),!0):nl(e)?(n.markAllActive(),this.closeDropdown(),t.reportWebviewClientEvent(Wo.chatRestoreDefaultContext),!0):e.clearContext?(n.markAllInactive(),this.closeDropdown(),t.reportWebviewClientEvent(Wo.chatClearContext),!0):e.userGuidelines?(t.openSettingsPage("guidelines"),this.closeDropdown(),!0):((r=this._insertMentionNode)==null||r.call(this,e),this.closeDropdown(),!0)});x(this,"_displayItems",Ke([this._active,this._breadcrumbIds,this._userQuery,this._currentGroup,this.allGroups],([e,t,n,r,i])=>{if(!e)return[];if(t.length>0&&r)return[{...r,type:"breadcrumb-back"},...r.group.items.slice(0,gt.SINGLE_GROUP_MAX_ITEMS).map(s=>({...s,type:"item"}))];if(n.length>0){const s=Zh(re(this._userQuery)).map(l=>({...l,type:"item"}));return i.flatMap(l=>[{...l,type:"breadcrumb"},...l.group.items.slice(0,gt.MULTI_GROUP_MAX_ITEMS).map(a=>({...a,type:"item"}))]).concat(s)}return[{...rl,type:"item"},...i.map(s=>({...s,type:"breadcrumb"})),{...ol,type:"item"},{...il,type:"item"}]}));x(this,"_refreshSeqNum",0);x(this,"_refreshMentionables",fc(async()=>{if(!re(this._active))return;this._refreshSeqNum++;const e=this._refreshSeqNum,t=this._chatModel.currentConversationModel&&dc(this._chatModel.currentConversationModel),n=re(this._userQuery),r=await this._chatModel.extensionClient.getSuggestions(n,t);e===this._refreshSeqNum&&this._allMentionables.set(qa({query:n,mentionables:r}))},gt.REFRESH_THROTTLE_MS,{leading:!0,trailing:!0}));this._chatModel=e,this._insertMentionNode=t,this._disposers.push(this._userQuery.subscribe(this._refreshMentionables)),this._disposers.push(this._active.subscribe(this._refreshMentionables))}get allGroups(){return this._allGroups}get currentGroup(){return this._currentGroup}get breadcrumbIds(){return this._breadcrumbIds}get displayItems(){return this._displayItems}get active(){return this._active}get userQuery(){return this._userQuery}_resetState(){this._breadcrumbIds.set([]),this._userQuery.set("")}};x(gt,"REFRESH_THROTTLE_MS",600),x(gt,"SINGLE_GROUP_MAX_ITEMS",12),x(gt,"MULTI_GROUP_MAX_ITEMS",6);let ko=gt;const qa=({query:o,mentionables:e,returnAllIfNoResults:t=!0,threshold:n=1})=>{if(o.length<=1)return e;const r=new nn(e,{keys:["label"],threshold:n,minMatchCharLength:0,ignoreLocation:!0,includeScore:!0,useExtendedSearch:!1,shouldSort:!0,findAllMatches:!0}).search(o);return r.length===0&&t?e:r.map(i=>i.item)},Zh=o=>qa({query:o,mentionables:[rl,ol,il],returnAllIfNoResults:!1,threshold:.6});function Yh(o){let e,t,n,r,i,s,l,a,c,d,h,u,f;return{c(){e=F("svg"),t=F("g"),n=F("path"),r=F("path"),i=F("path"),s=F("path"),l=F("path"),a=F("path"),c=F("path"),d=F("path"),h=F("path"),u=F("path"),f=F("path"),w(n,"d","M2.08,107.33 C3.82,110.98 6.43,114.11 9.73,115.85 C13.73,118.11 17.73,118.28 20.16,118.28 C20.86,118.28 21.38,118.28 22.08,118.28 C22.6,118.28 22.95,118.28 23.12,118.28 L90.23,118.28 C90.58,118.28 90.92,118.28 91.27,118.28 C91.97,118.28 92.49,118.28 93.18,118.28 C95.62,118.28 99.62,118.11 103.61,115.85 C106.92,113.93 109.53,110.98 111.26,107.33 M111.26,107.33 C112.66,104.02 113.35,100.55 113.35,96.9 L113.35,87.33 C115.09,86.29 116.65,85.07 117.87,83.16 C119.96,80.38 121,76.9 121,73.08 C121,69.25 119.78,65.43 117.52,62.47 C116.31,60.91 114.92,59.86 113.35,59 L113.35,46.13 C113.35,40.22 111.79,34.66 107.96,30.48 C104.31,26.31 99.44,24.57 94.23,24.57 L92.49,24.57 L92.31,24.57 C92.66,24.57 91.27,15.18 91.1,14.31 C90.4,11.01 89.18,7.71 87.45,4.93 C81.19,-5.85 67.97,-4.98 56.5,-4.98 C45.03,-4.98 31.81,-5.68 25.55,4.93 C23.82,7.88 22.77,11.01 21.9,14.31 C21.73,15.18 20.16,24.57 20.69,24.57 L20.51,24.57 L18.77,24.57 C13.56,24.57 8.86,26.31 5.04,30.48 C1.39,34.66 -0.35,40.22 -0.35,46.13 L-0.35,59 C-1.92,59.86 -3.31,61.08 -4.52,62.47 C-6.78,65.43 -8,69.08 -8,73.08 C-8,76.73 -6.96,80.21 -4.87,83.16 C-3.65,84.9 -2.09,86.29 -0.35,87.33 L-0.35,96.9 C-0.35,100.55 0.35,104.02 1.74,107.33"),w(n,"id","Shape"),w(n,"fill-rule","nonzero"),w(r,"d","M27.29,66.65 L36.86,66.65 C40.86,66.65 43.98,69.78 43.98,73.78 C43.98,77.77 40.86,80.9 36.86,80.9 L27.29,80.9 C23.3,80.9 20.17,77.77 20.17,73.78 C20.17,69.95 23.47,66.65 27.29,66.65 Z"),w(r,"id","Path"),w(r,"fill-rule","nonzero"),w(i,"d","M75.45,66.65 L85.01,66.65 C89.01,66.65 92.14,69.78 92.14,73.78 C92.14,77.77 89.01,80.9 85.01,80.9 L75.45,80.9 C71.45,80.9 68.32,77.77 68.32,73.78 C68.32,69.95 71.63,66.65 75.45,66.65 Z"),w(i,"id","Path"),w(i,"fill-rule","nonzero"),w(s,"d","M17.38,110.98 C15.64,110.98 14.08,110.63 12.86,110.11 C11.65,109.41 10.78,108.54 10.08,107.33 C9.39,106.11 9.21,104.55 9.21,102.63 L9.21,84.03 C9.21,81.77 8.69,80.03 7.82,78.99 C6.95,77.95 5.39,77.42 2.95,77.42 C2.26,77.42 1.74,77.08 1.21,76.56 C0.69,76.03 0.52,75.34 0.52,74.64 C0.52,73.95 0.69,73.25 1.21,72.73 C1.74,72.21 2.26,72.03 2.95,71.86 C5.21,71.86 6.78,71.34 7.82,70.3 C8.69,69.25 9.21,67.69 9.21,65.43 L9.21,46.65 C9.21,43.87 9.91,41.78 11.3,40.39 C12.69,39 14.78,38.31 17.56,38.31 L39.46,38.31 C40.33,38.31 41.03,38.48 41.55,39 C42.07,39.52 42.42,40.05 42.42,40.91 C42.42,41.61 42.24,42.31 41.72,42.83 C41.2,43.35 40.68,43.7 39.81,43.7 L19.47,43.7 C18.25,43.7 17.38,44.04 16.86,44.57 C16.34,45.09 15.99,46.13 15.99,47.35 L15.99,66.12 C15.99,67.69 15.64,69.25 14.95,70.64 C14.25,72.03 13.38,73.08 12.34,73.77 C11.3,74.47 9.91,74.99 8.52,74.99 L8.52,74.47 C10.08,74.47 11.3,74.82 12.34,75.69 C13.38,76.38 14.25,77.42 14.95,78.82 C15.64,80.21 15.99,81.6 15.99,83.34 L15.99,102.11 C15.99,103.33 16.34,104.37 16.86,104.89 C17.38,105.59 18.43,105.76 19.47,105.76 L39.81,105.76 C40.51,105.76 41.2,106.11 41.72,106.63 C42.24,107.15 42.42,107.85 42.42,108.54 C42.42,109.24 42.07,109.94 41.55,110.46 C41.03,110.98 40.33,111.33 39.46,111.33 L17.38,110.98 Z"),w(s,"id","Path"),w(s,"fill","currentColor"),w(s,"fill-rule","nonzero"),w(l,"d","M74.06,110.98 C73.19,110.98 72.49,110.63 71.97,110.11 C71.45,109.59 71.1,108.89 71.1,108.2 C71.1,107.5 71.28,106.81 71.8,106.29 C72.32,105.76 72.84,105.42 73.71,105.42 L94.05,105.42 C95.27,105.42 96.14,105.07 96.66,104.55 C97.18,104.03 97.53,102.98 97.53,101.77 L97.53,83.16 C97.53,81.6 97.88,80.03 98.57,78.64 C99.27,77.25 100.14,76.21 101.18,75.51 C102.22,74.82 103.61,74.3 105.01,74.3 L105.01,74.64 C103.44,74.64 102.22,74.3 101.18,73.43 C100.14,72.73 99.27,71.51 98.57,70.3 C97.88,68.91 97.53,67.52 97.53,65.78 L97.53,47 C97.53,45.78 97.18,44.74 96.66,44.22 C95.96,43.7 95.1,43.35 94.05,43.35 L73.71,43.35 C73.02,43.35 72.32,43 71.8,42.48 C71.28,41.96 71.1,41.26 71.1,40.57 C71.1,39.87 71.45,39.18 71.97,38.66 C72.49,38.13 73.19,37.96 74.06,37.96 L95.96,37.96 C98.57,37.96 100.66,38.66 102.22,40.05 C103.61,41.44 104.31,43.52 104.31,46.31 L104.31,64.91 C104.31,67.17 104.83,68.73 105.7,69.78 C106.57,70.82 108.13,71.34 110.57,71.34 C111.26,71.34 111.79,71.69 112.31,72.21 C112.83,72.73 113,73.25 113,74.12 C113,74.82 112.83,75.51 112.31,76.04 C111.96,76.56 111.26,76.9 110.57,76.9 C108.31,76.9 106.74,77.43 105.7,78.47 C104.83,79.51 104.31,81.25 104.31,83.51 L104.31,102.11 C104.31,103.85 103.96,105.42 103.44,106.81 C102.75,108.02 101.88,109.07 100.66,109.59 C99.44,110.28 97.88,110.46 96.14,110.46 L74.06,110.98 Z"),w(l,"id","Path"),w(l,"fill","currentColor"),w(l,"fill-rule","nonzero"),w(a,"d","M77.88,64.04 L75.1,67.69 C73.19,70.12 72.15,70.82 70.24,70.82 C69.19,70.82 68.32,70.3 68.32,69.43 C68.32,68.73 68.67,68.21 69.02,67.52 L73.36,61.43 C74.41,59.87 75.62,59 77.54,59 L78.06,59 C79.97,59 81.19,59.87 82.23,61.43 L86.58,67.52 C87.1,68.21 87.27,68.91 87.27,69.43 C87.27,70.3 86.4,70.82 85.36,70.82 C83.45,70.82 82.4,70.12 80.49,67.69 L77.88,64.04 Z"),w(a,"id","Path"),w(a,"fill","currentColor"),w(a,"fill-rule","nonzero"),w(c,"d","M37.2,64.04 L34.42,67.69 C32.51,70.12 31.46,70.82 29.55,70.82 C28.51,70.82 27.64,70.3 27.64,69.43 C27.64,68.73 27.99,68.21 28.34,67.52 L32.68,61.43 C33.73,59.87 34.94,59 36.85,59 L37.38,59 C39.29,59 40.51,59.87 41.55,61.43 L45.89,67.52 C46.42,68.21 46.59,68.91 46.59,69.43 C46.59,70.3 45.72,70.82 44.68,70.82 C42.77,70.82 41.72,70.12 39.81,67.69 L37.2,64.04 Z"),w(c,"id","Path"),w(c,"fill","currentColor"),w(c,"fill-rule","nonzero"),w(d,"d","M81.88,6.84 L75.8,23.01 C75.45,23.88 74.41,24.05 72.67,23.53 C70.93,23.01 70.24,22.14 70.58,21.27 C71.8,17.1 72.67,13.97 73.36,11.71 C74.06,9.45 74.58,7.71 74.93,6.67 C75.28,5.62 75.45,5.1 75.45,4.93 C75.45,4.75 75.62,4.58 75.62,4.58 C75.97,3.36 77.36,3.19 79.45,3.89 C81.36,4.75 82.23,5.62 81.88,6.84 Z"),w(d,"id","Path"),w(d,"fill","currentColor"),w(d,"fill-rule","nonzero"),w(h,"d","M30.07,6.84 L36.16,23.01 C36.51,23.88 37.55,24.05 39.29,23.53 C41.03,23.01 41.72,22.14 41.37,21.27 C40.16,17.1 39.29,13.97 38.59,11.71 C37.9,9.45 37.38,7.71 37.03,6.67 C36.68,5.62 36.51,5.1 36.51,4.93 C36.51,4.75 36.33,4.58 36.33,4.58 C35.98,3.36 34.59,3.19 32.51,3.89 C30.42,4.75 29.55,5.62 30.07,6.84 Z"),w(h,"id","Path"),w(h,"fill","currentColor"),w(h,"fill-rule","nonzero"),w(u,"d","M59.46,3.01 L58.76,20.4 C58.76,21.44 57.89,21.79 55.98,21.79 C54.07,21.79 53.2,21.27 53.2,20.4 C53.02,16.05 52.85,12.75 52.85,10.32 C52.85,7.88 52.68,6.14 52.68,5.1 C52.68,4.06 52.68,3.54 52.68,3.19 C52.68,2.84 52.68,2.84 52.68,2.84 C52.68,1.62 53.89,0.93 56.15,0.93 C58.41,1.1 59.46,1.62 59.46,3.01 Z"),w(u,"id","Path"),w(u,"fill","currentColor"),w(u,"fill-rule","nonzero"),w(f,"d","M42.07,79.6 C41.55,79.08 41.03,78.73 39.99,78.73 C39.29,78.73 38.77,78.91 38.25,79.25 C37.73,79.6 37.55,80.12 37.55,80.64 C37.55,80.99 37.55,81.17 37.73,81.34 C37.73,81.51 37.9,81.69 37.9,81.86 C42.42,88.29 48.51,91.77 56.85,91.77 C65.02,91.77 71.28,88.29 75.8,81.86 C75.97,81.69 75.97,81.51 75.97,81.34 C75.97,81.17 76.15,80.82 76.15,80.64 C76.15,80.12 75.8,79.6 75.45,79.25 C74.93,78.91 74.41,78.73 73.71,78.73 C72.84,78.73 72.15,79.08 71.63,79.6 C69.72,82.04 67.46,83.77 65.02,84.99 C62.59,86.21 59.98,86.73 56.68,86.73 C53.55,86.73 50.77,86.21 48.33,84.99 C45.9,83.77 43.98,82.04 42.07,79.6 Z"),w(f,"id","Path"),w(f,"stroke","currentColor"),w(f,"stroke-width","0.452891"),w(f,"fill","currentColor"),w(f,"fill-rule","nonzero"),w(t,"id","Page-1"),w(t,"stroke","none"),w(t,"stroke-width","1"),w(t,"fill","none"),w(t,"fill-rule","evenodd"),w(e,"width","113"),w(e,"height","112"),w(e,"viewBox","0 0 113 112"),w(e,"fill","none"),w(e,"xmlns","http://www.w3.org/2000/svg")},m(p,m){V(p,e,m),N(e,t),N(t,n),N(t,r),N(t,i),N(t,s),N(t,l),N(t,a),N(t,c),N(t,d),N(t,h),N(t,u),N(t,f)},p:W,i:W,o:W,d(p){p&&B(e)}}}class Bs extends oe{constructor(e){super(),ie(this,e,null,Yh,se,{})}}function Xh(o){let e,t,n,r,i,s,l,a,c,d,h,u;return{c(){e=F("svg"),t=F("path"),n=F("path"),r=F("path"),i=F("path"),s=F("path"),l=F("path"),a=F("path"),c=F("path"),d=F("path"),h=F("path"),u=F("path"),w(t,"d","M18.0699 114.34C16.1299 114.34 14.5 114.004 13.17 113.331C11.85 112.659 10.8501 111.637 10.1801 110.286C9.50005 108.934 9.16003 107.284 9.16003 105.34V85.3291C9.16003 82.8631 8.67996 81.0691 7.70996 79.9481C6.73996 78.8331 5.02005 78.2421 2.55005 78.1861C1.79005 78.1861 1.16995 77.8931 0.699951 77.3011C0.229951 76.7161 0 76.0371 0 75.2771C0 74.4551 0.229951 73.7761 0.699951 73.2471C1.16995 72.7171 1.79005 72.4241 2.55005 72.3681C5.02005 72.3061 6.73996 71.7211 7.70996 70.6061C8.67996 69.4911 9.16003 67.7221 9.16003 65.3121V45.3011C9.16003 42.3611 9.92995 40.1251 11.45 38.5991C12.98 37.0731 15.1799 36.3071 18.0699 36.3071H41.59C42.48 36.3071 43.2 36.5751 43.8 37.0981C44.38 37.6281 44.6801 38.3061 44.6801 39.1221C44.6801 39.8881 44.4301 40.5491 43.9301 41.1091C43.4401 41.6701 42.78 41.9501 41.95 41.9501H20.1C18.87 41.9501 17.92 42.2741 17.28 42.9221C16.64 43.5691 16.3101 44.5661 16.3101 45.9241V66.0281C16.3101 67.7911 15.96 69.3911 15.25 70.8301C14.55 72.2751 13.6101 73.4021 12.4301 74.2311C11.2501 75.0531 9.88004 75.4641 8.29004 75.4641V75.1091C9.88004 75.1091 11.2501 75.5201 12.4301 76.3421C13.6101 77.1641 14.55 78.2981 15.25 79.7431C15.96 81.1811 16.3101 82.7821 16.3101 84.5451V104.736C16.3101 106.094 16.64 107.091 17.28 107.738C17.93 108.392 18.87 108.71 20.1 108.71H41.95C42.77 108.71 43.4301 108.99 43.9301 109.551C44.4401 110.111 44.6801 110.771 44.6801 111.538C44.6801 112.304 44.39 112.958 43.8 113.518C43.2 114.079 42.48 114.359 41.59 114.359H18.0699V114.34Z"),w(t,"fill","currentColor"),w(n,"d","M78.84 114.339C77.95 114.339 77.22 114.059 76.63 113.499C76.04 112.938 75.75 112.278 75.75 111.518C75.75 110.758 76 110.092 76.5 109.531C76.99 108.971 77.65 108.69 78.48 108.69H100.32C101.56 108.69 102.5 108.373 103.15 107.719C103.79 107.071 104.12 106.075 104.12 104.717V84.5251C104.12 82.7621 104.47 81.1621 105.18 79.7231C105.88 78.2781 106.82 77.1511 108 76.3231C109.18 75.5001 110.55 75.0891 112.14 75.0891V75.444C110.55 75.444 109.18 75.0331 108 74.2111C106.82 73.3891 105.88 72.256 105.18 70.811C104.47 69.372 104.12 67.771 104.12 66.009V45.9041C104.12 44.5531 103.79 43.556 103.15 42.902C102.5 42.254 101.56 41.9301 100.32 41.9301H78.48C77.66 41.9301 77 41.6501 76.5 41.0901C76 40.5351 75.75 39.8691 75.75 39.1031C75.75 38.2811 76.04 37.608 76.63 37.079C77.22 36.549 77.95 36.2881 78.84 36.2881H102.36C105.25 36.2881 107.44 37.0541 108.98 38.5801C110.51 40.1061 111.27 42.3421 111.27 45.2811V65.2921C111.27 67.7031 111.75 69.4721 112.72 70.5861C113.69 71.7011 115.41 72.2931 117.88 72.3491C118.64 72.4051 119.26 72.6981 119.73 73.2271C120.2 73.7571 120.43 74.4351 120.43 75.2581C120.43 76.0241 120.2 76.696 119.73 77.282C119.26 77.867 118.64 78.1661 117.88 78.1661C115.41 78.2221 113.69 78.8141 112.72 79.9291C111.75 81.0441 111.27 82.8371 111.27 85.3101V105.321C111.27 107.264 110.93 108.908 110.25 110.266C109.57 111.624 108.58 112.633 107.26 113.312C105.93 113.984 104.3 114.321 102.36 114.321H78.84V114.339Z"),w(n,"fill","currentColor"),w(r,"d","M79.92 76.025C83.88 76.025 87.09 72.8159 87.09 68.8569C87.09 64.8979 83.88 61.689 79.92 61.689C75.96 61.689 72.75 64.8979 72.75 68.8569C72.75 72.8159 75.96 76.025 79.92 76.025Z"),w(r,"fill","currentColor"),w(i,"d","M39.8301 76.025C43.7901 76.025 46.9901 72.8159 46.9901 68.8569C46.9901 64.8979 43.7901 61.689 39.8301 61.689C35.8701 61.689 32.6602 64.8979 32.6602 68.8569C32.6602 72.8159 35.8701 76.025 39.8301 76.025Z"),w(i,"fill","currentColor"),w(s,"d","M59.8701 94.9769C59.7201 94.9769 59.5701 94.9769 59.4301 94.9769C59.1501 94.9769 58.8701 94.9609 58.5901 94.9459H58.5101C57.4801 94.8899 56.45 94.7669 55.45 94.5819L55.0901 94.515C54.9501 94.49 54.8001 94.4589 54.6601 94.4279C49.1801 93.2799 44.1001 90.3729 40.3601 86.2509C39.8101 85.6359 39.85 84.877 40.07 84.369C40.32 83.81 40.8101 83.4669 41.3401 83.4669C41.4701 83.4669 41.5901 83.4869 41.7201 83.5179C47.4801 85.1129 53.4301 86.764 59.3101 86.835H60.4301C66.3101 86.764 72.2501 85.1179 78.0001 83.5229C78.1501 83.4819 78.2801 83.4619 78.4101 83.4619C78.9401 83.4619 79.4301 83.81 79.6801 84.364C79.9001 84.872 79.9401 85.6309 79.3901 86.2459C75.6501 90.3679 70.5701 93.2749 65.0901 94.4229C64.9301 94.4589 64.7601 94.4899 64.6001 94.5259L64.3001 94.5819C63.2901 94.7669 62.2601 94.8839 61.2401 94.9409H61.1601C60.8801 94.9609 60.61 94.9669 60.33 94.9719C60.18 94.9719 60.0301 94.9719 59.8801 94.9719L59.8701 94.9769Z"),w(s,"fill","currentColor"),w(l,"d","M70.1802 51.6431L70.8801 42.201L62.9901 47.5721C62.4701 47.9181 62.1301 48.0911 61.6101 48.0911C60.6501 48.0911 59.8701 47.225 59.8701 46.272C59.8701 45.493 60.4801 44.8871 61.0901 44.6271L70.0101 40.3821L61.0901 36.1381C60.3901 35.7911 59.8701 35.2721 59.8701 34.4921C59.8701 33.5391 60.6501 32.76 61.6101 32.673C62.2101 32.673 62.4701 32.8461 62.9901 33.1931L70.8801 38.563L70.1802 29.121C70.1002 28.082 70.8802 27.2161 71.9202 27.2161C72.9502 27.2161 73.7301 28.082 73.6501 29.121L72.9501 38.563L80.8401 33.1931C81.3601 32.8461 81.7001 32.673 82.2201 32.673C83.1801 32.673 83.9601 33.5391 83.9601 34.4921C83.9601 35.4451 83.3501 35.8781 82.7401 36.1381L73.8201 40.3821L82.7401 44.6271C83.3501 44.8871 83.9601 45.32 83.9601 46.272C83.9601 47.225 83.1801 48.0911 82.2201 48.0911C81.7001 48.0911 81.3601 47.9181 80.8401 47.5721L72.9501 42.201L73.6501 51.6431C73.7301 52.6821 72.9502 53.5491 71.9202 53.5491C70.8802 53.5491 70.1002 52.6821 70.1802 51.6431Z"),w(l,"fill","currentColor"),w(a,"fill-rule","evenodd"),w(a,"clip-rule","evenodd"),w(a,"d","M73.65 51.6429C73.73 52.6819 72.95 53.5489 71.92 53.5489C70.88 53.5489 70.1001 52.6819 70.1801 51.6429L70.88 42.2009L62.99 47.5719C62.47 47.9179 62.13 48.0909 61.61 48.0909C60.65 48.0909 59.87 47.2249 59.87 46.2719C59.87 45.4929 60.48 44.8869 61.09 44.6269L70.01 40.3819L61.09 36.1379C60.39 35.7909 59.87 35.2719 59.87 34.4919C59.87 33.5389 60.65 32.7599 61.61 32.6729C62.21 32.6729 62.47 32.8459 62.99 33.1929L70.88 38.5629L70.1801 29.1208C70.1001 28.0818 70.88 27.2159 71.92 27.2159C72.95 27.2159 73.73 28.0818 73.65 29.1208L72.95 38.5629L80.84 33.1929C81.36 32.8459 81.7 32.6729 82.22 32.6729C83.18 32.6729 83.96 33.5389 83.96 34.4919C83.96 35.4449 83.35 35.8779 82.74 36.1379L73.8199 40.3819L82.74 44.6269C83.35 44.8869 83.96 45.3199 83.96 46.2719C83.96 47.2249 83.18 48.0909 82.22 48.0909C81.7 48.0909 81.36 47.9179 80.84 47.5719L72.95 42.2009L73.65 51.6429ZM75.04 45.8169L75.46 51.5029C75.62 53.5679 74.04 55.3629 71.92 55.3629C69.79 55.3629 68.21 53.5679 68.37 51.5029L68.79 45.8169L64 49.0809C63.39 49.4879 62.67 49.9059 61.61 49.9059C59.54 49.9059 58.0601 48.1109 58.0601 46.2719C58.0601 44.5309 59.34 43.4159 60.33 42.9759L65.79 40.3819L60.28 37.7609C59.31 37.2759 58.0601 36.2689 58.0601 34.4919C58.0601 32.5089 59.6499 31.0289 61.4399 30.8659L61.52 30.8589H61.61C62.11 30.8589 62.57 30.9339 63.04 31.1309C63.41 31.2859 63.73 31.5049 63.95 31.6519C63.97 31.6629 63.98 31.6729 64 31.6829L64.01 31.6929L68.79 34.9469L68.37 29.2619C68.21 27.1959 69.79 25.4009 71.92 25.4009C74.04 25.4009 75.62 27.1959 75.46 29.2619L75.04 34.9469L79.83 31.6829C80.44 31.2759 81.16 30.8589 82.22 30.8589C84.29 30.8589 85.77 32.6529 85.77 34.4919C85.77 35.3979 85.4699 36.1709 84.9399 36.7699C84.4699 37.3069 83.91 37.6099 83.49 37.7899L78.04 40.3819L83.49 42.9739C83.91 43.1549 84.4699 43.4579 84.9399 43.9949C85.4699 44.5929 85.77 45.3669 85.77 46.2719C85.77 48.1109 84.29 49.9059 82.22 49.9059C81.16 49.9059 80.44 49.4879 79.83 49.0809L79.8199 49.0709L75.04 45.8169Z"),w(a,"fill","currentColor"),w(c,"d","M41.1499 37.1279L41.8499 27.6859L33.9598 33.0569C33.4398 33.4029 33.0998 33.5759 32.5798 33.5759C31.6198 33.5759 30.8398 32.71 30.8398 31.757C30.8398 30.978 31.4499 30.3709 32.0599 30.1119L40.9799 25.8669L32.0599 21.6229C31.3599 21.2759 30.8398 20.7559 30.8398 19.9769C30.8398 19.0239 31.6198 18.244 32.5798 18.158C33.1798 18.158 33.4398 18.3309 33.9598 18.6779L41.8499 24.0479L41.1499 14.606C41.0699 13.567 41.8499 12.7009 42.8799 12.7009C43.9199 12.7009 44.6999 13.567 44.6199 14.606L43.9199 24.0479L51.8099 18.6779C52.3299 18.3309 52.6698 18.158 53.1898 18.158C54.1498 18.158 54.9299 19.0239 54.9299 19.9769C54.9299 20.9299 54.3198 21.3629 53.7098 21.6229L44.7899 25.8669L53.7098 30.1119C54.3198 30.3709 54.9299 30.805 54.9299 31.757C54.9299 32.71 54.1498 33.5759 53.1898 33.5759C52.6698 33.5759 52.3299 33.4029 51.8099 33.0569L43.9199 27.6859L44.6199 37.1279C44.6999 38.1669 43.9199 39.0339 42.8799 39.0339C41.8499 39.0339 41.0699 38.1669 41.1499 37.1279Z"),w(c,"fill","currentColor"),w(d,"fill-rule","evenodd"),w(d,"clip-rule","evenodd"),w(d,"d","M44.62 37.128C44.7 38.167 43.92 39.034 42.88 39.034C41.85 39.034 41.07 38.167 41.15 37.128L41.85 27.686L33.96 33.057C33.44 33.403 33.1 33.576 32.58 33.576C31.62 33.576 30.84 32.71 30.84 31.757C30.84 30.978 31.4501 30.371 32.0601 30.112L40.98 25.867L32.0601 21.623C31.3601 21.276 30.84 20.756 30.84 19.977C30.84 19.024 31.62 18.244 32.58 18.158C33.18 18.158 33.44 18.331 33.96 18.678L41.85 24.048L41.15 14.606C41.07 13.567 41.85 12.701 42.88 12.701C43.92 12.701 44.7 13.567 44.62 14.606L43.92 24.048L51.8101 18.678C52.3301 18.331 52.6699 18.158 53.1899 18.158C54.1499 18.158 54.9301 19.024 54.9301 19.977C54.9301 20.93 54.32 21.363 53.71 21.623L44.79 25.867L53.71 30.112C54.32 30.371 54.9301 30.805 54.9301 31.757C54.9301 32.71 54.1499 33.576 53.1899 33.576C52.6699 33.576 52.3301 33.403 51.8101 33.057L43.92 27.686L44.62 37.128ZM46.01 31.302L46.4301 36.988C46.5901 39.053 45.01 40.848 42.88 40.848C40.76 40.848 39.18 39.053 39.34 36.988L39.76 31.302L34.97 34.566C34.36 34.973 33.64 35.391 32.58 35.391C30.51 35.391 29.03 33.596 29.03 31.757C29.03 30.016 30.31 28.9 31.3 28.461L36.76 25.867L31.25 23.246C30.28 22.761 29.03 21.754 29.03 19.977C29.03 17.994 30.62 16.514 32.41 16.351L32.49 16.343H32.58C33.08 16.343 33.54 16.419 34.01 16.616C34.38 16.771 34.7 16.99 34.92 17.137C34.94 17.148 34.95 17.158 34.97 17.168L34.98 17.178L39.76 20.432L39.34 14.747C39.18 12.681 40.76 10.886 42.88 10.886C45.01 10.886 46.5901 12.681 46.4301 14.747L46.01 20.432L50.8 17.168C51.41 16.761 52.1299 16.343 53.1899 16.343C55.2599 16.343 56.74 18.138 56.74 19.977C56.74 20.882 56.44 21.656 55.91 22.254C55.44 22.791 54.88 23.095 54.46 23.275L49.01 25.867L54.46 28.459C54.88 28.639 55.44 28.943 55.91 29.48C56.44 30.078 56.74 30.852 56.74 31.757C56.74 33.596 55.2599 35.391 53.1899 35.391C52.1299 35.391 51.41 34.973 50.8 34.566L50.79 34.556L46.01 31.302Z"),w(d,"fill","currentColor"),w(h,"d","M84.7001 26.2419L85.3901 16.7999L77.5101 22.1699C76.9901 22.5169 76.6401 22.6899 76.1201 22.6899C75.1701 22.6899 74.3901 21.8239 74.3901 20.8709C74.3901 20.0909 75.0001 19.4849 75.6001 19.2249L84.5201 14.981L75.6001 10.736C74.9101 10.39 74.3901 9.86994 74.3901 9.09094C74.3901 8.13794 75.1701 7.35791 76.1201 7.27191C76.7301 7.27191 76.9901 7.44495 77.5101 7.79095L85.3901 13.1619L84.7001 3.71991C84.6101 2.68091 85.3902 1.81396 86.4302 1.81396C87.4702 1.81396 88.2502 2.68091 88.1602 3.71991L87.4701 13.1619L95.3501 7.79095C95.8701 7.44495 96.2201 7.27191 96.7401 7.27191C97.6901 7.27191 98.4701 8.13794 98.4701 9.09094C98.4701 10.0429 97.8601 10.476 97.2601 10.736L88.3401 14.981L97.2601 19.2249C97.8601 19.4849 98.4701 19.9179 98.4701 20.8709C98.4701 21.8239 97.6901 22.6899 96.7401 22.6899C96.2201 22.6899 95.8701 22.5169 95.3501 22.1699L87.4701 16.7999L88.1602 26.2419C88.2502 27.2809 87.4702 28.1469 86.4302 28.1469C85.3902 28.1469 84.6101 27.2809 84.7001 26.2419Z"),w(h,"fill","currentColor"),w(u,"fill-rule","evenodd"),w(u,"clip-rule","evenodd"),w(u,"d","M88.1602 26.242C88.2502 27.281 87.4702 28.147 86.4302 28.147C85.3902 28.147 84.6101 27.281 84.7001 26.242L85.3901 16.8L77.5101 22.17C76.9901 22.517 76.6401 22.69 76.1201 22.69C75.1701 22.69 74.3901 21.824 74.3901 20.871C74.3901 20.091 75.0001 19.485 75.6001 19.225L84.5201 14.981L75.6001 10.736C74.9101 10.39 74.3901 9.87 74.3901 9.091C74.3901 8.138 75.1701 7.35797 76.1201 7.27197C76.7301 7.27197 76.9901 7.44502 77.5101 7.79102L85.3901 13.162L84.7001 3.71997C84.6101 2.68097 85.3902 1.81403 86.4302 1.81403C87.4702 1.81403 88.2502 2.68097 88.1602 3.71997L87.4701 13.162L95.3501 7.79102C95.8701 7.44502 96.2201 7.27197 96.7401 7.27197C97.6901 7.27197 98.4701 8.138 98.4701 9.091C98.4701 10.043 97.8601 10.476 97.2601 10.736L88.3401 14.981L97.2601 19.225C97.8601 19.485 98.4701 19.918 98.4701 20.871C98.4701 21.824 97.6901 22.69 96.7401 22.69C96.2201 22.69 95.8701 22.517 95.3501 22.17L87.4701 16.8L88.1602 26.242ZM89.5502 20.416L89.9701 26.101C90.1401 28.167 88.5502 29.962 86.4302 29.962C84.3102 29.962 82.7201 28.167 82.8901 26.101L83.3102 20.416L78.5101 23.68C77.9001 24.087 77.1901 24.504 76.1201 24.504C74.0601 24.504 72.5801 22.71 72.5801 20.871C72.5801 19.13 73.8601 18.014 74.8501 17.574L80.3002 14.981L74.7902 12.359C73.8202 11.874 72.5801 10.867 72.5801 9.091C72.5801 7.108 74.1601 5.62803 75.9601 5.46503L76.0402 5.45697H76.1201C76.6201 5.45697 77.0902 5.533 77.5502 5.729C77.9202 5.885 78.2501 6.10398 78.4701 6.25098C78.4801 6.26198 78.5001 6.27198 78.5101 6.28198L78.5302 6.29199L83.3102 9.54602L82.8901 3.85999C82.7201 1.79399 84.3102 0 86.4302 0C88.5502 0 90.1401 1.79399 89.9701 3.85999L89.5502 9.54602L94.3501 6.28198C94.9601 5.87498 95.6701 5.45697 96.7401 5.45697C98.8101 5.45697 100.28 7.252 100.28 9.091C100.28 9.996 99.9801 10.77 99.4601 11.368C98.9901 11.905 98.4201 12.208 98.0101 12.389L92.5602 14.981L98.0101 17.573C98.4201 17.753 98.9901 18.056 99.4601 18.593C99.9801 19.192 100.28 19.965 100.28 20.871C100.28 22.71 98.8101 24.504 96.7401 24.504C95.6701 24.504 94.9601 24.087 94.3501 23.68L94.3301 23.67L89.5502 20.416Z"),w(u,"fill","currentColor"),w(e,"width","121"),w(e,"height","115"),w(e,"viewBox","0 0 121 115"),w(e,"fill","none"),w(e,"xmlns","http://www.w3.org/2000/svg")},m(f,p){V(f,e,p),N(e,t),N(e,n),N(e,r),N(e,i),N(e,s),N(e,l),N(e,a),N(e,c),N(e,d),N(e,h),N(e,u)},p:W,i:W,o:W,d(f){f&&B(e)}}}class Qh extends oe{constructor(e){super(),ie(this,e,null,Xh,se,{})}}function eu(o){let e,t,n,r,i,s,l,a,c,d,h,u;return{c(){e=F("svg"),t=F("g"),n=F("path"),r=F("path"),i=F("path"),s=F("path"),l=F("path"),a=F("path"),c=F("path"),d=F("path"),h=F("path"),u=F("path"),w(n,"d","M18.068,99.825 C16.131,99.825 14.499,99.489 13.173,98.816 C11.852,98.143 10.85,97.122 10.177,95.77 C9.504,94.419 9.162,92.768 9.162,90.825 L9.162,70.814 C9.162,68.348 8.676,66.554 7.711,65.433 C6.739,64.318 5.02,63.726 2.554,63.67 C1.787,63.67 1.171,63.378 0.704,62.786 C0.23,62.2 0,61.522 0,60.762 C0,59.94 0.23,59.261 0.704,58.731 C1.171,58.202 1.794,57.909 2.554,57.853 C5.02,57.791 6.739,57.205 7.711,56.091 C8.682,54.976 9.162,53.207 9.162,50.797 L9.162,30.785 C9.162,27.846 9.928,25.61 11.454,24.084 C12.98,22.558 15.184,21.792 18.068,21.792 L41.592,21.792 C42.476,21.792 43.205,22.06 43.797,22.583 C44.382,23.112 44.681,23.791 44.681,24.607 C44.681,25.373 44.432,26.033 43.934,26.594 C43.435,27.154 42.775,27.435 41.947,27.435 L20.105,27.435 C18.865,27.435 17.925,27.758 17.283,28.406 C16.636,29.054 16.312,30.05 16.312,31.408 L16.312,51.513 C16.312,53.275 15.957,54.876 15.253,56.315 C14.549,57.76 13.609,58.887 12.431,59.715 C11.254,60.537 9.878,60.949 8.29,60.949 L8.29,60.594 C9.878,60.594 11.254,61.005 12.431,61.827 C13.609,62.649 14.549,63.782 15.253,65.227 C15.957,66.666 16.312,68.267 16.312,70.029 L16.312,90.221 C16.312,91.579 16.636,92.575 17.283,93.223 C17.931,93.877 18.871,94.195 20.105,94.195 L41.947,94.195 C42.769,94.195 43.429,94.475 43.934,95.035 C44.438,95.596 44.681,96.256 44.681,97.022 C44.681,97.788 44.388,98.442 43.797,99.003 C43.205,99.563 42.476,99.844 41.592,99.844 L18.068,99.844 L18.068,99.825 Z"),w(n,"id","Path"),w(n,"fill","currentColor"),w(n,"fill-rule","nonzero"),w(r,"d","M78.838,99.825 C77.953,99.825 77.224,99.545 76.633,98.984 C76.041,98.424 75.748,97.764 75.748,97.004 C75.748,96.244 75.997,95.578 76.496,95.017 C76.994,94.457 77.654,94.176 78.483,94.176 L100.325,94.176 C101.564,94.176 102.505,93.859 103.146,93.205 C103.794,92.557 104.118,91.56 104.118,90.203 L104.118,70.011 C104.118,68.248 104.473,66.648 105.177,65.209 C105.88,63.764 106.821,62.637 107.998,61.808 C109.175,60.986 110.551,60.575 112.14,60.575 L112.14,60.93 C110.551,60.93 109.175,60.519 107.998,59.697 C106.821,58.875 105.88,57.741 105.177,56.296 C104.473,54.858 104.118,53.257 104.118,51.494 L104.118,31.39 C104.118,30.038 103.794,29.042 103.146,28.388 C102.498,27.74 101.558,27.416 100.325,27.416 L78.483,27.416 C77.66,27.416 77,27.136 76.496,26.575 C75.997,26.021 75.748,25.355 75.748,24.589 C75.748,23.767 76.041,23.094 76.633,22.564 C77.218,22.035 77.953,21.773 78.838,21.773 L102.361,21.773 C105.245,21.773 107.444,22.54 108.976,24.065 C110.508,25.591 111.268,27.827 111.268,30.767 L111.268,50.778 C111.268,53.189 111.754,54.957 112.719,56.072 C113.69,57.187 115.409,57.779 117.876,57.835 C118.642,57.891 119.258,58.184 119.726,58.713 C120.199,59.242 120.429,59.921 120.429,60.743 C120.429,61.509 120.199,62.182 119.726,62.767 C119.258,63.353 118.636,63.652 117.876,63.652 C115.409,63.708 113.69,64.3 112.719,65.414 C111.747,66.529 111.268,68.323 111.268,70.796 L111.268,90.807 C111.268,92.75 110.931,94.394 110.253,95.752 C109.574,97.11 108.577,98.119 107.257,98.798 C105.93,99.47 104.298,99.807 102.361,99.807 L78.838,99.807 L78.838,99.825 Z"),w(r,"id","Path"),w(r,"fill","currentColor"),w(r,"fill-rule","nonzero"),w(i,"d","M39.827,61.511 C43.785,61.511 46.995,58.301 46.995,54.343 C46.995,50.384 43.785,47.175 39.827,47.175 C35.868,47.175 32.659,50.384 32.659,54.343 C32.659,58.301 35.868,61.511 39.827,61.511 Z"),w(i,"id","Path"),w(i,"fill","currentColor"),w(i,"fill-rule","nonzero"),w(s,"d","M61.599,115.942 C65.558,115.942 68.767,112.733 68.767,108.774 C68.767,104.816 65.558,101.607 61.599,101.607 C57.641,101.607 54.432,104.816 54.432,108.774 C54.432,112.733 57.641,115.942 61.599,115.942 Z"),w(s,"id","Path"),w(s,"fill","currentColor"),w(s,"fill-rule","nonzero"),w(l,"d","M59.872,80.462 C59.724,80.462 59.575,80.462 59.426,80.462 C59.149,80.462 58.872,80.447 58.59,80.431 L58.514,80.431 C57.483,80.375 56.453,80.252 55.453,80.067 L55.089,80.001 C54.945,79.975 54.802,79.944 54.658,79.914 C49.178,78.765 44.097,75.858 40.365,71.736 C39.806,71.121 39.847,70.362 40.072,69.855 C40.318,69.296 40.805,68.952 41.344,68.952 C41.467,68.952 41.595,68.973 41.723,69.004 C47.481,70.598 53.433,72.249 59.313,72.321 L60.431,72.321 C66.312,72.249 72.254,70.603 78.001,69.009 C78.149,68.968 78.278,68.947 78.406,68.947 C78.944,68.947 79.431,69.296 79.677,69.85 C79.903,70.357 79.944,71.116 79.385,71.731 C75.648,75.853 70.572,78.76 65.091,79.908 C64.927,79.944 64.763,79.975 64.599,80.011 L64.297,80.067 C63.292,80.252 62.261,80.37 61.236,80.426 L61.159,80.426 C60.882,80.447 60.605,80.452 60.329,80.457 C60.18,80.457 60.031,80.457 59.882,80.457 L59.872,80.462 Z"),w(l,"id","Path"),w(l,"fill","currentColor"),w(l,"fill-rule","nonzero"),w(a,"d","M50.388,15.193 C50.342,15.193 50.25,15.171 50.112,15.125 C49.975,15.033 49.654,14.895 49.149,14.712 L37.044,8.934 C36.54,8.659 36.288,8.201 36.288,7.559 C36.288,6.963 36.471,6.298 36.838,5.564 C37.205,4.831 37.617,4.212 38.076,3.707 C38.58,3.157 39.039,2.882 39.451,2.882 C39.818,2.882 40.116,2.974 40.346,3.157 L51.763,10.998 C52.222,11.273 52.451,11.617 52.451,12.03 C52.451,12.58 52.199,13.245 51.694,14.024 C51.19,14.804 50.754,15.193 50.388,15.193 Z"),w(a,"id","Path"),w(a,"fill","currentColor"),w(a,"fill-rule","nonzero"),w(c,"d","M73.439,12.312 C73.393,12.312 73.302,12.289 73.164,12.243 C73.027,12.151 72.706,12.014 72.201,11.83 L60.096,6.053 C59.592,5.778 59.339,5.319 59.339,4.677 C59.339,4.081 59.523,3.416 59.89,2.682 C60.256,1.949 60.669,1.33 61.128,0.825 C61.632,0.275 62.091,-1.13686838e-13 62.503,-1.13686838e-13 C62.87,-1.13686838e-13 63.168,0.092 63.397,0.275 L74.815,8.116 C75.273,8.391 75.503,8.735 75.503,9.148 C75.503,9.698 75.25,10.363 74.746,11.142 C74.242,11.922 73.806,12.312 73.439,12.312 Z"),w(c,"id","Path"),w(c,"fill","currentColor"),w(c,"fill-rule","nonzero"),w(d,"d","M52.207,112.646 C52.157,112.676 52.043,112.71 51.864,112.748 C51.657,112.737 51.22,112.794 50.556,112.918 L33.73,114.413 C33.007,114.438 32.439,114.102 32.028,113.406 C31.646,112.76 31.418,111.922 31.346,110.891 C31.273,109.861 31.324,108.925 31.498,108.085 C31.692,107.165 32.013,106.573 32.46,106.308 C32.858,106.073 33.24,105.981 33.606,106.033 L51.009,107.216 C51.683,107.221 52.152,107.447 52.416,107.894 C52.769,108.491 52.921,109.373 52.874,110.541 C52.827,111.71 52.604,112.411 52.207,112.646 Z"),w(d,"id","Path"),w(d,"fill","currentColor"),w(d,"fill-rule","nonzero"),w(h,"d","M71.184,112.646 C71.234,112.676 71.348,112.71 71.526,112.748 C71.734,112.737 72.17,112.794 72.835,112.918 L89.661,114.413 C90.384,114.438 90.951,114.102 91.363,113.406 C91.745,112.76 91.972,111.922 92.045,110.891 C92.117,109.861 92.067,108.925 91.893,108.085 C91.699,107.165 91.378,106.573 90.93,106.308 C90.533,106.073 90.151,105.981 89.785,106.033 L72.381,107.216 C71.708,107.221 71.239,107.447 70.975,107.894 C70.622,108.491 70.469,109.373 70.516,110.541 C70.564,111.71 70.786,112.411 71.184,112.646 Z"),w(h,"id","Path"),w(h,"fill","currentColor"),w(h,"fill-rule","nonzero"),w(u,"d","M79.214,52.635 L76.187,56.552 C74.11,59.223 72.983,59.876 70.965,59.876 C69.837,59.876 68.947,59.341 68.947,58.451 C68.947,57.798 69.244,57.086 69.778,56.315 L74.466,49.727 C75.653,48.065 76.9,47.175 78.977,47.175 L79.511,47.175 C81.588,47.175 82.834,48.065 84.021,49.727 L88.71,56.315 C89.244,57.086 89.541,57.798 89.541,58.451 C89.541,59.341 88.651,59.876 87.523,59.876 C85.386,59.876 84.318,59.223 82.241,56.552 L79.214,52.635 Z"),w(u,"id","Path"),w(u,"fill","currentColor"),w(u,"fill-rule","nonzero"),w(t,"id","Page-1"),w(t,"stroke","none"),w(t,"stroke-width","1"),w(t,"fill","none"),w(t,"fill-rule","evenodd"),w(e,"width","121px"),w(e,"height","116px"),w(e,"viewBox","0 0 121 116"),w(e,"version","1.1"),w(e,"xmlns","http://www.w3.org/2000/svg")},m(f,p){V(f,e,p),N(e,t),N(t,n),N(t,r),N(t,i),N(t,s),N(t,l),N(t,a),N(t,c),N(t,d),N(t,h),N(t,u)},p:W,i:W,o:W,d(f){f&&B(e)}}}class tu extends oe{constructor(e){super(),ie(this,e,null,eu,se,{})}}function nu(o){let e,t,n,r,i,s,l,a,c,d,h;return{c(){e=F("svg"),t=F("path"),n=F("path"),r=F("path"),i=F("path"),s=F("path"),l=F("path"),a=F("path"),c=F("path"),d=F("path"),h=F("path"),w(t,"d","M18.068 110.71C16.131 110.71 14.499 110.38 13.173 109.7C11.852 109.03 10.85 108.01 10.177 106.66C9.504 105.31 9.16199 103.66 9.16199 101.71V81.7001C9.16199 79.2301 8.676 77.4401 7.711 76.3201C6.739 75.2101 5.02002 74.6102 2.55402 74.5602C1.78702 74.5602 1.17098 74.2602 0.703979 73.6702C0.229979 73.0902 0 72.4102 0 71.6502C0 70.8302 0.229979 70.1502 0.703979 69.6202C1.17098 69.0902 1.79402 68.8002 2.55402 68.7402C5.02002 68.6802 6.739 68.0902 7.711 66.9802C8.682 65.8602 9.16199 64.0902 9.16199 61.6802V41.6702C9.16199 38.7302 9.92798 36.5002 11.454 34.9702C12.98 33.4502 15.184 32.6802 18.068 32.6802H41.592C42.476 32.6802 43.205 32.9502 43.797 33.4702C44.382 34.0002 44.681 34.6802 44.681 35.4902C44.681 36.2602 44.432 36.9202 43.934 37.4802C43.435 38.0402 42.775 38.3202 41.947 38.3202H20.105C18.865 38.3202 17.925 38.6502 17.283 39.2902C16.636 39.9402 16.312 40.9402 16.312 42.3002V62.4002C16.312 64.1602 15.957 65.7601 15.253 67.2001C14.549 68.6501 13.609 69.7702 12.431 70.6002C11.254 71.4202 9.87798 71.8401 8.28998 71.8401V71.4802C9.87798 71.4802 11.254 71.8901 12.431 72.7101C13.609 73.5401 14.549 74.6702 15.253 76.1102C15.957 77.5502 16.312 79.1502 16.312 80.9202V101.11C16.312 102.47 16.636 103.46 17.283 104.11C17.931 104.76 18.871 105.08 20.105 105.08H41.947C42.769 105.08 43.429 105.36 43.934 105.92C44.438 106.48 44.681 107.14 44.681 107.91C44.681 108.68 44.388 109.33 43.797 109.89C43.205 110.45 42.476 110.73 41.592 110.73H18.068V110.71Z"),w(t,"fill","currentColor"),w(n,"d","M78.838 110.71C77.953 110.71 77.225 110.43 76.633 109.87C76.041 109.31 75.749 108.65 75.749 107.89C75.749 107.13 75.998 106.46 76.496 105.9C76.994 105.34 77.654 105.06 78.483 105.06H100.325C101.564 105.06 102.505 104.74 103.146 104.09C103.794 103.44 104.118 102.45 104.118 101.09V80.9002C104.118 79.1302 104.473 77.5301 105.177 76.0901C105.881 74.6501 106.821 73.5201 107.998 72.6901C109.175 71.8701 110.552 71.4601 112.14 71.4601V71.8201C110.552 71.8201 109.175 71.4101 107.998 70.5801C106.821 69.7601 105.881 68.6302 105.177 67.1802C104.473 65.7402 104.118 64.1402 104.118 62.3802V42.2802C104.118 40.9202 103.794 39.9302 103.146 39.2702C102.499 38.6302 101.558 38.3002 100.325 38.3002H78.483C77.661 38.3002 77 38.0202 76.496 37.4602C75.998 36.9102 75.749 36.2402 75.749 35.4702C75.749 34.6502 76.041 33.9802 76.633 33.4502C77.218 32.9202 77.953 32.6602 78.838 32.6602H102.362C105.245 32.6602 107.444 33.4302 108.976 34.9502C110.508 36.4802 111.268 38.7102 111.268 41.6502V61.6602C111.268 64.0702 111.754 65.8401 112.719 66.9601C113.691 68.0701 115.41 68.6602 117.876 68.7202C118.642 68.7802 119.259 69.0702 119.726 69.6002C120.199 70.1302 120.43 70.8102 120.43 71.6302C120.43 72.4002 120.199 73.0702 119.726 73.6502C119.259 74.2402 118.636 74.5402 117.876 74.5402C115.41 74.5902 113.691 75.1902 112.719 76.3002C111.747 77.4202 111.268 79.2102 111.268 81.6802V101.69C111.268 103.64 110.932 105.28 110.253 106.64C109.574 108 108.577 109 107.257 109.68C105.93 110.36 104.299 110.69 102.362 110.69H78.838V110.71Z"),w(n,"fill","currentColor"),w(r,"d","M39.827 76.03C43.786 76.03 46.995 72.82 46.995 68.86C46.995 64.9 43.786 61.6899 39.827 61.6899C35.868 61.6899 32.659 64.9 32.659 68.86C32.659 72.82 35.868 76.03 39.827 76.03Z"),w(r,"fill","currentColor"),w(i,"d","M28.903 57.47L28.908 57.46H28.913C29.272 57.09 29.484 56.58 29.484 55.91C29.484 55.44 29.335 54.98 29.096 54.63C28.866 54.29 28.492 53.98 28.016 53.98C27.811 53.98 27.62 54.02 27.465 54.08C27.332 54.13 27.184 54.2 27.074 54.3C23.331 57.37 21.319 61.57 21.319 67.13C21.319 72.7 23.331 76.89 27.074 79.96C27.184 80.06 27.332 80.1399 27.465 80.1899C27.62 80.2399 27.811 80.29 28.016 80.29C28.492 80.29 28.866 79.97 29.096 79.63C29.335 79.28 29.484 78.83 29.484 78.36C29.484 77.68 29.272 77.1701 28.913 76.8101L28.908 76.8H28.903C26.193 74.21 24.895 71.29 24.895 67.13C24.895 62.98 26.193 60.06 28.903 57.47Z"),w(i,"fill","currentColor"),w(i,"stroke","currentColor"),w(i,"stroke-width","0.907194"),w(s,"d","M50.931 76.8H50.925L50.92 76.8101C50.562 77.1701 50.349 77.68 50.349 78.36C50.349 78.83 50.498 79.28 50.737 79.63C50.967 79.97 51.342 80.29 51.818 80.29C52.022 80.29 52.213 80.2499 52.368 80.1899C52.501 80.1399 52.649 80.06 52.759 79.96C56.502 76.89 58.514 72.7 58.514 67.13C58.514 61.57 56.502 57.37 52.759 54.3C52.649 54.2 52.501 54.13 52.368 54.08C52.213 54.02 52.022 53.98 51.818 53.98C51.342 53.98 50.967 54.29 50.737 54.63C50.498 54.98 50.349 55.44 50.349 55.91C50.349 56.58 50.562 57.09 50.92 57.46H50.925L50.931 57.47C53.64 60.06 54.938 62.98 54.938 67.13C54.938 71.29 53.64 74.21 50.931 76.8Z"),w(s,"fill","currentColor"),w(s,"stroke","currentColor"),w(s,"stroke-width","0.907194"),w(l,"d","M68.819 57.47L68.825 57.46H68.83C69.188 57.09 69.4 56.58 69.4 55.91C69.4 55.44 69.251 54.98 69.013 54.63C68.782 54.29 68.408 53.98 67.932 53.98C67.728 53.98 67.537 54.02 67.382 54.08C67.248 54.13 67.1 54.2 66.991 54.3C63.248 57.37 61.236 61.57 61.236 67.13C61.236 72.7 63.248 76.89 66.991 79.96C67.1 80.06 67.248 80.1399 67.382 80.1899C67.537 80.2399 67.728 80.29 67.932 80.29C68.408 80.29 68.782 79.97 69.013 79.63C69.251 79.28 69.4 78.83 69.4 78.36C69.4 77.68 69.188 77.1701 68.83 76.8101L68.825 76.8H68.819C66.109 74.21 64.812 71.29 64.812 67.13C64.812 62.98 66.109 60.06 68.819 57.47Z"),w(l,"fill","currentColor"),w(l,"stroke","currentColor"),w(l,"stroke-width","0.907194"),w(a,"d","M90.847 76.8H90.842L90.836 76.8101C90.478 77.1701 90.266 77.68 90.266 78.36C90.266 78.83 90.415 79.28 90.654 79.63C90.884 79.97 91.258 80.29 91.734 80.29C91.938 80.29 92.129 80.2499 92.284 80.1899C92.418 80.1399 92.566 80.06 92.676 79.96C96.418 76.89 98.431 72.7 98.431 67.13C98.431 61.57 96.418 57.37 92.676 54.3C92.566 54.2 92.418 54.13 92.284 54.08C92.129 54.02 91.938 53.98 91.734 53.98C91.258 53.98 90.884 54.29 90.654 54.63C90.415 54.98 90.266 55.44 90.266 55.91C90.266 56.58 90.478 57.09 90.836 57.46H90.842L90.847 57.47C93.557 60.06 94.854 62.98 94.854 67.13C94.854 71.29 93.557 74.21 90.847 76.8Z"),w(a,"fill","currentColor"),w(a,"stroke","currentColor"),w(a,"stroke-width","0.907194"),w(c,"d","M44.445 84.1503L44.44 84.1403C43.96 83.5903 43.268 83.2402 42.287 83.2402C41.627 83.2402 40.986 83.4802 40.507 83.8602C40.03 84.2402 39.69 84.7802 39.69 85.3702C39.69 85.6602 39.743 85.9403 39.815 86.1703C39.883 86.3903 39.976 86.5902 40.076 86.7002C44.904 93.5802 51.5 97.3003 60.328 97.3003C69.157 97.3003 75.753 93.5802 80.581 86.7002C80.681 86.5902 80.773 86.3903 80.842 86.1703C80.914 85.9403 80.967 85.6602 80.967 85.3702C80.967 84.7802 80.626 84.2402 80.15 83.8602C79.671 83.4802 79.029 83.2402 78.37 83.2402C77.389 83.2402 76.697 83.5903 76.216 84.1403L76.212 84.1503C74.075 86.7503 71.802 88.6702 69.219 89.9402C66.637 91.2102 63.734 91.8402 60.328 91.8402C56.923 91.8402 54.02 91.2102 51.438 89.9402C48.855 88.6702 46.582 86.7503 44.445 84.1503Z"),w(c,"fill","currentColor"),w(c,"stroke","currentColor"),w(c,"stroke-width","0.453597"),w(d,"d","M54.456 28.9769L55.988 10.851C56 10.336 56.719 10.095 58.143 10.129C58.748 10.143 59.264 10.2259 59.693 10.3759C60.078 10.5259 60.268 10.718 60.262 10.952C60.338 13.202 60.416 15.4059 60.494 17.5619C60.572 19.7419 60.655 21.6999 60.743 23.4349C60.788 25.1929 60.841 26.587 60.903 27.619C60.922 28.65 60.931 29.166 60.931 29.166C60.923 29.517 60.593 29.7549 59.942 29.8799C59.248 30.0269 58.49 30.091 57.67 30.072C56.763 30.05 56.01 29.9509 55.409 29.7719C54.765 29.6169 54.447 29.3509 54.456 28.9769ZM55.486 4.69092L55.557 0.852965C55.564 0.548965 55.828 0.343951 56.349 0.238951C56.871 0.110951 57.52 0.0559499 58.297 0.0749499C59.117 0.0939499 59.785 0.179945 60.3 0.332945C60.815 0.462945 61.069 0.678946 61.062 0.983947L60.991 4.82092C60.983 5.14892 60.719 5.36491 60.198 5.46991C59.677 5.57491 59.028 5.61791 58.251 5.59991C57.431 5.57991 56.763 5.50591 56.248 5.37591C55.732 5.24691 55.478 5.01792 55.486 4.69092Z"),w(d,"fill","currentColor"),w(h,"d","M80.121 67.15L77.095 71.0699C75.017 73.7399 73.89 74.39 71.872 74.39C70.744 74.39 69.854 73.86 69.854 72.97C69.854 72.31 70.151 71.6 70.685 70.83L75.373 64.24C76.56 62.58 77.807 61.6899 79.884 61.6899H80.418C82.495 61.6899 83.742 62.58 84.929 64.24L89.617 70.83C90.151 71.6 90.448 72.31 90.448 72.97C90.448 73.86 89.558 74.39 88.43 74.39C86.294 74.39 85.225 73.7399 83.148 71.0699L80.121 67.15Z"),w(h,"fill","currentColor"),w(e,"width","121"),w(e,"height","111"),w(e,"viewBox","0 0 121 111"),w(e,"fill","none"),w(e,"xmlns","http://www.w3.org/2000/svg")},m(u,f){V(u,e,f),N(e,t),N(e,n),N(e,r),N(e,i),N(e,s),N(e,l),N(e,a),N(e,c),N(e,d),N(e,h)},p:W,i:W,o:W,d(u){u&&B(e)}}}class ru extends oe{constructor(e){super(),ie(this,e,null,nu,se,{})}}function ur(o){switch(o){case Vn.DEFAULT:return Bs;case Vn.PROTOTYPER:return tu;case Vn.BRAINSTORM:return Qh;case Vn.REVIEWER:return ru;default:return Bs}}const ou=o=>({}),Vs=o=>({}),iu=o=>({}),Hs=o=>({}),su=o=>({}),js=o=>({}),lu=o=>({}),qs=o=>({});function Ks(o){let e;const t=o[6].leftIcon,n=Te(t,o,o[15],qs);return{c(){n&&n.c()},m(r,i){n&&n.m(r,i),e=!0},p(r,i){n&&n.p&&(!e||32768&i)&&Ne(n,t,r,r[15],e?Ae(t,r[15],i,lu):Le(r[15]),qs)},i(r){e||($(n,r),e=!0)},o(r){M(n,r),e=!1},d(r){n&&n.d(r)}}}function Ws(o){let e,t;const n=o[6].text,r=Te(n,o,o[15],js);return{c(){e=te("span"),r&&r.c(),w(e,"class","c-text-combo__text svelte-1pddlam")},m(i,s){V(i,e,s),r&&r.m(e,null),t=!0},p(i,s){r&&r.p&&(!t||32768&s)&&Ne(r,n,i,i[15],t?Ae(n,i[15],s,su):Le(i[15]),js)},i(i){t||($(r,i),t=!0)},o(i){M(r,i),t=!1},d(i){i&&B(e),r&&r.d(i)}}}function Js(o){let e,t,n;const r=o[6].grayText,i=Te(r,o,o[15],Hs);return{c(){e=te("div"),t=te("div"),i&&i.c(),w(t,"class","c-text-combo__gray-text svelte-1pddlam"),w(e,"class","c-text-combo__gray svelte-1pddlam"),Oe(e,"c-text-combo--gray-truncate-left",o[3]==="left"),Oe(e,"c-text-combo--gray-truncate-right",o[3]==="right")},m(s,l){V(s,e,l),N(e,t),i&&i.m(t,null),n=!0},p(s,l){i&&i.p&&(!n||32768&l)&&Ne(i,r,s,s[15],n?Ae(r,s[15],l,iu):Le(s[15]),Hs),(!n||8&l)&&Oe(e,"c-text-combo--gray-truncate-left",s[3]==="left"),(!n||8&l)&&Oe(e,"c-text-combo--gray-truncate-right",s[3]==="right")},i(s){n||($(i,s),n=!0)},o(s){M(i,s),n=!1},d(s){s&&B(e),i&&i.d(s)}}}function Us(o){let e;const t=o[6].rightIcon,n=Te(t,o,o[15],Vs);return{c(){n&&n.c()},m(r,i){n&&n.m(r,i),e=!0},p(r,i){n&&n.p&&(!e||32768&i)&&Ne(n,t,r,r[15],e?Ae(t,r[15],i,ou):Le(r[15]),Vs)},i(r){e||($(n,r),e=!0)},o(r){M(n,r),e=!1},d(r){n&&n.d(r)}}}function au(o){let e,t,n,r,i,s,l,a,c=o[5].leftIcon&&Ks(o),d=o[5].text&&Ws(o),h=o[5].grayText&&Js(o),u=o[5].rightIcon&&Us(o);return{c(){e=te("div"),c&&c.c(),t=le(),d&&d.c(),n=le(),h&&h.c(),r=le(),u&&u.c(),w(e,"class",i=hn(`c-text-combo ${o[0]}`)+" svelte-1pddlam"),w(e,"role","button"),w(e,"tabindex","-1"),Oe(e,"c-text-combo--align-right",o[2]==="right"),Oe(e,"c-text-combo--shrink",o[4])},m(f,p){V(f,e,p),c&&c.m(e,null),N(e,t),d&&d.m(e,null),N(e,n),h&&h.m(e,null),N(e,r),u&&u.m(e,null),s=!0,l||(a=[ve(e,"click",o[7]),ve(e,"keydown",o[8]),ve(e,"keyup",o[9]),ve(e,"blur",o[10]),ve(e,"focus",o[11]),ve(e,"mouseenter",o[12]),ve(e,"mouseleave",o[13]),ve(e,"contextmenu",o[14])],l=!0)},p(f,p){f[5].leftIcon?c?(c.p(f,p),32&p&&$(c,1)):(c=Ks(f),c.c(),$(c,1),c.m(e,t)):c&&(ae(),M(c,1,1,()=>{c=null}),ce()),f[5].text?d?(d.p(f,p),32&p&&$(d,1)):(d=Ws(f),d.c(),$(d,1),d.m(e,n)):d&&(ae(),M(d,1,1,()=>{d=null}),ce()),f[5].grayText?h?(h.p(f,p),32&p&&$(h,1)):(h=Js(f),h.c(),$(h,1),h.m(e,r)):h&&(ae(),M(h,1,1,()=>{h=null}),ce()),f[5].rightIcon?u?(u.p(f,p),32&p&&$(u,1)):(u=Us(f),u.c(),$(u,1),u.m(e,null)):u&&(ae(),M(u,1,1,()=>{u=null}),ce()),(!s||1&p&&i!==(i=hn(`c-text-combo ${f[0]}`)+" svelte-1pddlam"))&&w(e,"class",i),(!s||5&p)&&Oe(e,"c-text-combo--align-right",f[2]==="right"),(!s||17&p)&&Oe(e,"c-text-combo--shrink",f[4])},i(f){s||($(c),$(d),$(h),$(u),s=!0)},o(f){M(c),M(d),M(h),M(u),s=!1},d(f){f&&B(e),c&&c.d(),d&&d.d(),h&&h.d(),u&&u.d(),l=!1,pr(a)}}}function cu(o){let e,t;return e=new Mt({props:{size:o[1],$$slots:{default:[au]},$$scope:{ctx:o}}}),{c(){P(e.$$.fragment)},m(n,r){_(e,n,r),t=!0},p(n,[r]){const i={};2&r&&(i.size=n[1]),32829&r&&(i.$$scope={dirty:r,ctx:n}),e.$set(i)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){M(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function du(o,e,t){let{$$slots:n={},$$scope:r}=e;const i=So(n);let{class:s=""}=e,{size:l=1}=e,{align:a="left"}=e,{greyTextTruncateDirection:c="right"}=e,{shrink:d=!1}=e;return o.$$set=h=>{"class"in h&&t(0,s=h.class),"size"in h&&t(1,l=h.size),"align"in h&&t(2,a=h.align),"greyTextTruncateDirection"in h&&t(3,c=h.greyTextTruncateDirection),"shrink"in h&&t(4,d=h.shrink),"$$scope"in h&&t(15,r=h.$$scope)},[s,l,a,c,d,i,n,function(h){Ze.call(this,o,h)},function(h){Ze.call(this,o,h)},function(h){Ze.call(this,o,h)},function(h){Ze.call(this,o,h)},function(h){Ze.call(this,o,h)},function(h){Ze.call(this,o,h)},function(h){Ze.call(this,o,h)},function(h){Ze.call(this,o,h)},r]}class hu extends oe{constructor(e){super(),ie(this,e,du,cu,se,{class:0,size:1,align:2,greyTextTruncateDirection:3,shrink:4})}}function uu(o){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 256 512"},o[0]],r={};for(let i=0;i<n.length;i+=1)r=Yr(r,n[i]);return{c(){e=F("svg"),t=new ic(!0),this.h()},l(i){e=nc(i,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var s=rc(e);t=oc(s,!0),s.forEach(B),this.h()},h(){t.a=null,jo(e,r)},m(i,s){tc(i,e,s),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M192 48c8.8 0 16 7.2 16 16v32h-48c-8.8 0-16 7.2-16 16s7.2 16 16 16h48v64h-48c-8.8 0-16 7.2-16 16s7.2 16 16 16h48v64h-48c-8.8 0-16 7.2-16 16s7.2 16 16 16h48v64h-48c-8.8 0-16 7.2-16 16s7.2 16 16 16h48v32c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16V64c0-8.8 7.2-16 16-16zM64 0C28.7 0 0 28.7 0 64v384c0 35.3 28.7 64 64 64h128c35.3 0 64-28.7 64-64V64c0-35.3-28.7-64-64-64z"/>',e)},p(i,[s]){jo(e,r=ec(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 256 512"},1&s&&i[0]]))},i:W,o:W,d(i){i&&B(e)}}}function pu(o,e,t){return o.$$set=n=>{t(0,e=Yr(Yr({},e),qo(n)))},[e=qo(e)]}class Ka extends oe{constructor(e){super(),ie(this,e,pu,uu,se,{})}}function fu(o){let e,t,n,r=o[0].label+"";return{c(){e=te("span"),t=te("span"),n=qe(r),w(t,"class","c-mentionable-group-label__text right"),w(e,"class","c-mentionable-group-label")},m(i,s){V(i,e,s),N(e,t),N(t,n)},p(i,s){1&s&&r!==(r=i[0].label+"")&&je(n,r)},i:W,o:W,d(i){i&&B(e)}}}function mu(o){let e,t;return e=new hu({props:{$$slots:{text:[Mu],leftIcon:[ku]},$$scope:{ctx:o}}}),{c(){P(e.$$.fragment)},m(n,r){_(e,n,r),t=!0},p(n,r){const i={};17&r&&(i.$$scope={dirty:r,ctx:n}),e.$set(i)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){M(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function gu(o){let e,t=o[0].label+"";return{c(){e=qe(t)},m(n,r){V(n,e,r)},p(n,r){1&r&&t!==(t=n[0].label+"")&&je(e,t)},i:W,o:W,d(n){n&&B(e)}}}function yu(o){let e,t;return e=new Ie({props:{filepath:o[0].rule.path,$$slots:{leftIcon:[Su]},$$scope:{ctx:o}}}),{c(){P(e.$$.fragment)},m(n,r){_(e,n,r),t=!0},p(n,r){const i={};1&r&&(i.filepath=n[0].rule.path),16&r&&(i.$$scope={dirty:r,ctx:n}),e.$set(i)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){M(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Cu(o){let e,t;return e=new Ie({props:{filepath:o[0].recentFile.pathName,$$slots:{leftIcon:[Ou]},$$scope:{ctx:o}}}),{c(){P(e.$$.fragment)},m(n,r){_(e,n,r),t=!0},p(n,r){const i={};1&r&&(i.filepath=n[0].recentFile.pathName),16&r&&(i.$$scope={dirty:r,ctx:n}),e.$set(i)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){M(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function wu(o){let e,t;return e=new Ie({props:{filepath:o[0].selection.pathName,$$slots:{leftIcon:[Eu]},$$scope:{ctx:o}}}),{c(){P(e.$$.fragment)},m(n,r){_(e,n,r),t=!0},p(n,r){const i={};1&r&&(i.filepath=n[0].selection.pathName),16&r&&(i.$$scope={dirty:r,ctx:n}),e.$set(i)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){M(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function vu(o){let e,t;return e=new Ie({props:{filepath:o[0].sourceFolder.folderRoot,$$slots:{leftIcon:[Tu]},$$scope:{ctx:o}}}),{c(){P(e.$$.fragment)},m(n,r){_(e,n,r),t=!0},p(n,r){const i={};1&r&&(i.filepath=n[0].sourceFolder.folderRoot),16&r&&(i.$$scope={dirty:r,ctx:n}),e.$set(i)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){M(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function xu(o){let e,t;return e=new Ie({props:{filepath:o[0].externalSource.name,$$slots:{leftIcon:[Nu]},$$scope:{ctx:o}}}),{c(){P(e.$$.fragment)},m(n,r){_(e,n,r),t=!0},p(n,r){const i={};1&r&&(i.filepath=n[0].externalSource.name),16&r&&(i.$$scope={dirty:r,ctx:n}),e.$set(i)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){M(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function bu(o){let e,t;return e=new Ie({props:{filepath:o[0].folder.pathName,$$slots:{leftIcon:[Lu]},$$scope:{ctx:o}}}),{c(){P(e.$$.fragment)},m(n,r){_(e,n,r),t=!0},p(n,r){const i={};1&r&&(i.filepath=n[0].folder.pathName),16&r&&(i.$$scope={dirty:r,ctx:n}),e.$set(i)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){M(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function $u(o){let e,t;return e=new Ie({props:{filepath:o[0].file.pathName,$$slots:{leftIcon:[Au]},$$scope:{ctx:o}}}),{c(){P(e.$$.fragment)},m(n,r){_(e,n,r),t=!0},p(n,r){const i={};1&r&&(i.filepath=n[0].file.pathName),16&r&&(i.$$scope={dirty:r,ctx:n}),e.$set(i)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){M(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function ku(o){let e,t,n;var r=ur(o[0].personality.type);return r&&(t=un(r,{})),{c(){e=te("span"),t&&P(t.$$.fragment),w(e,"slot","leftIcon"),w(e,"class","c-context-menu-item__icon svelte-1a2w9oo")},m(i,s){V(i,e,s),t&&_(t,e,null),n=!0},p(i,s){if(1&s&&r!==(r=ur(i[0].personality.type))){if(t){ae();const l=t;M(l.$$.fragment,1,0,()=>{R(l,1)}),ce()}r?(t=un(r,{}),P(t.$$.fragment),$(t.$$.fragment,1),_(t,e,null)):t=null}},i(i){n||(t&&$(t.$$.fragment,i),n=!0)},o(i){t&&M(t.$$.fragment,i),n=!1},d(i){i&&B(e),t&&R(t)}}}function Mu(o){let e,t,n=o[0].label+"";return{c(){e=te("span"),t=qe(n),w(e,"slot","text")},m(r,i){V(r,e,i),N(e,t)},p(r,i){1&i&&n!==(n=r[0].label+"")&&je(t,n)},d(r){r&&B(e)}}}function Su(o){let e,t;return e=new Ka({props:{slot:"leftIcon",iconName:"rule"}}),{c(){P(e.$$.fragment)},m(n,r){_(e,n,r),t=!0},p:W,i(n){t||($(e.$$.fragment,n),t=!0)},o(n){M(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Ou(o){let e,t;return e=new wn({props:{slot:"leftIcon",iconName:"description"}}),{c(){P(e.$$.fragment)},m(n,r){_(e,n,r),t=!0},p:W,i(n){t||($(e.$$.fragment,n),t=!0)},o(n){M(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Eu(o){let e,t;return e=new wn({props:{slot:"leftIcon",iconName:"text_select_start"}}),{c(){P(e.$$.fragment)},m(n,r){_(e,n,r),t=!0},p:W,i(n){t||($(e.$$.fragment,n),t=!0)},o(n){M(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Tu(o){let e,t;return e=new wn({props:{slot:"leftIcon",iconName:"folder_managed"}}),{c(){P(e.$$.fragment)},m(n,r){_(e,n,r),t=!0},p:W,i(n){t||($(e.$$.fragment,n),t=!0)},o(n){M(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Nu(o){let e,t;return e=new wn({props:{slot:"leftIcon",iconName:"import_contacts"}}),{c(){P(e.$$.fragment)},m(n,r){_(e,n,r),t=!0},p:W,i(n){t||($(e.$$.fragment,n),t=!0)},o(n){M(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Lu(o){let e,t;return e=new wn({props:{slot:"leftIcon",iconName:"folder_open"}}),{c(){P(e.$$.fragment)},m(n,r){_(e,n,r),t=!0},p:W,i(n){t||($(e.$$.fragment,n),t=!0)},o(n){M(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Au(o){let e,t;return e=new wn({props:{slot:"leftIcon",iconName:"description"}}),{c(){P(e.$$.fragment)},m(n,r){_(e,n,r),t=!0},p:W,i(n){t||($(e.$$.fragment,n),t=!0)},o(n){M(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Iu(o){let e,t,n,r,i,s,l,a,c,d,h,u,f,p;const m=[$u,bu,xu,vu,wu,Cu,yu,gu,mu,fu],g=[];function y(C,v){return 1&v&&(e=null),1&v&&(t=null),1&v&&(n=null),1&v&&(r=null),1&v&&(i=null),1&v&&(s=null),1&v&&(l=null),1&v&&(a=null),1&v&&(c=null),1&v&&(d=null),e==null&&(e=!!sl(C[0])),e?0:(t==null&&(t=!!ll(C[0])),t?1:(n==null&&(n=!!al(C[0])),n?2:(r==null&&(r=!!cl(C[0])),r?3:(i==null&&(i=!!dl(C[0])),i?4:(s==null&&(s=!!hl(C[0])),s?5:(l==null&&(l=!!ul(C[0])),l?6:(a==null&&(a=!!Ln(C[0])),a?7:(c==null&&(c=!!pl(C[0])),c?8:(d==null&&(d=!!(nl(C[0])||uc(C[0])||fl(C[0]))),d?9:-1)))))))))}return~(h=y(o,-1))&&(u=g[h]=m[h](o)),{c(){u&&u.c(),f=dt()},m(C,v){~h&&g[h].m(C,v),V(C,f,v),p=!0},p(C,v){let b=h;h=y(C,v),h===b?~h&&g[h].p(C,v):(u&&(ae(),M(g[b],1,1,()=>{g[b]=null}),ce()),~h?(u=g[h],u?u.p(C,v):(u=g[h]=m[h](C),u.c()),$(u,1),u.m(f.parentNode,f)):u=null)},i(C){p||($(u),p=!0)},o(C){M(u),p=!1},d(C){C&&B(f),~h&&g[h].d(C)}}}function Du(o){let e,t,n;var r=o[3];function i(s,l){return{props:{highlight:s[2],onSelect:s[1],$$slots:{default:[Iu]},$$scope:{ctx:s}}}}return r&&(e=un(r,i(o))),{c(){e&&P(e.$$.fragment),t=dt()},m(s,l){e&&_(e,s,l),V(s,t,l),n=!0},p(s,[l]){if(8&l&&r!==(r=s[3])){if(e){ae();const a=e;M(a.$$.fragment,1,0,()=>{R(a,1)}),ce()}r?(e=un(r,i(s)),P(e.$$.fragment),$(e.$$.fragment,1),_(e,t.parentNode,t)):e=null}else if(r){const a={};4&l&&(a.highlight=s[2]),2&l&&(a.onSelect=s[1]),17&l&&(a.$$scope={dirty:l,ctx:s}),e.$set(a)}},i(s){n||(e&&$(e.$$.fragment,s),n=!0)},o(s){e&&M(e.$$.fragment,s),n=!1},d(s){s&&B(t),e&&R(e,s)}}}function Ru(o,e,t){let n,{item:r}=e,{onSelect:i}=e,{highlight:s}=e;return o.$$set=l=>{"item"in l&&t(0,r=l.item),"onSelect"in l&&t(1,i=l.onSelect),"highlight"in l&&t(2,s=l.highlight)},o.$$.update=()=>{1&o.$$.dirty&&(r.type==="breadcrumb-back"?t(3,n=ln.BreadcrumbBackItem):r.type==="breadcrumb"&&Ln(r)?t(3,n=ln.BreadcrumbItem):r.type!=="item"||Ln(r)||t(3,n=ln.Item))},[r,i,s,n]}class _u extends oe{constructor(e){super(),ie(this,e,Ru,Du,se,{item:0,onSelect:1,highlight:2})}}function Pu(o){let e,t;return{c(){e=F("svg"),t=F("path"),w(t,"fill-rule","evenodd"),w(t,"clip-rule","evenodd"),w(t,"d","M4.5 1C4.22386 1 4 1.22386 4 1.5C4 1.77614 4.22386 2 4.5 2C5.42215 2 6.0399 2.23054 6.42075 2.56379C6.79286 2.88939 7 3.36626 7 4V7H5.75C5.47386 7 5.25 7.22386 5.25 7.5C5.25 7.77614 5.47386 8 5.75 8H7V11C7 11.6337 6.79286 12.1106 6.42075 12.4362C6.0399 12.7695 5.42215 13 4.5 13C4.22386 13 4 13.2239 4 13.5C4 13.7761 4.22386 14 4.5 14C5.57785 14 6.4601 13.7305 7.07925 13.1888C7.24168 13.0467 7.38169 12.8896 7.5 12.7198C7.61832 12.8896 7.75832 13.0467 7.92075 13.1888C8.5399 13.7305 9.42215 14 10.5 14C10.7761 14 11 13.7761 11 13.5C11 13.2239 10.7761 13 10.5 13C9.57785 13 8.9601 12.7695 8.57925 12.4362C8.20714 12.1106 8 11.6337 8 11V8H9.25C9.52614 8 9.75 7.77614 9.75 7.5C9.75 7.22386 9.52614 7 9.25 7H8V4C8 3.36626 8.20714 2.88939 8.57925 2.56379C8.9601 2.23054 9.57785 2 10.5 2C10.7761 2 11 1.77614 11 1.5C11 1.22386 10.7761 1 10.5 1C9.42215 1 8.5399 1.26946 7.92075 1.81121C7.75832 1.95333 7.61832 2.11043 7.5 2.28023C7.38168 2.11043 7.24168 1.95333 7.07925 1.81121C6.4601 1.26946 5.57785 1 4.5 1Z"),w(t,"fill","currentColor"),w(e,"width","15"),w(e,"height","15"),w(e,"viewBox","0 0 15 15"),w(e,"fill","none"),w(e,"xmlns","http://www.w3.org/2000/svg")},m(n,r){V(n,e,r),N(e,t)},p:W,i:W,o:W,d(n){n&&B(e)}}}class zu extends oe{constructor(e){super(),ie(this,e,null,Pu,se,{})}}function Fu(o){let e,t;return e=new tl({props:{slot:"leftIcon",class:"c-guidelines-filespan-warning-icon"}}),{c(){P(e.$$.fragment)},m(n,r){_(e,n,r),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){M(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Bu(o){let e,t;return e=new mc({}),{c(){P(e.$$.fragment)},m(n,r){_(e,n,r),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){M(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Vu(o){let e,t,n,r,i,s,l,a,c,d;const h=[Bu,Fu],u=[];function f(p,m){return p[1].guidelinesOverLimit?1:0}return t=f(o),n=u[t]=h[t](o),{c(){e=te("div"),n.c(),r=le(),i=te("span"),s=qe(o[2]),w(i,"class","c-guidelines-filespan__text svelte-1jd2qvj"),w(i,"role","button"),w(i,"tabindex","0"),w(e,"class",l=hn(`c-guidelines-filespan ${o[0]}`)+" svelte-1jd2qvj")},m(p,m){V(p,e,m),u[t].m(e,null),N(e,r),N(e,i),N(i,s),a=!0,c||(d=[ve(i,"click",o[3]),ve(i,"keydown",ju)],c=!0)},p(p,m){let g=t;t=f(p),t!==g&&(ae(),M(u[g],1,1,()=>{u[g]=null}),ce(),n=u[t],n||(n=u[t]=h[t](p),n.c()),$(n,1),n.m(e,r)),(!a||4&m)&&je(s,p[2]),(!a||1&m&&l!==(l=hn(`c-guidelines-filespan ${p[0]}`)+" svelte-1jd2qvj"))&&w(e,"class",l)},i(p){a||($(n),a=!0)},o(p){M(n),a=!1},d(p){p&&B(e),u[t].d(),c=!1,pr(d)}}}function Hu(o){let e,t;return e=new Mt({props:{size:1,$$slots:{default:[Vu]},$$scope:{ctx:o}}}),{c(){P(e.$$.fragment)},m(n,r){_(e,n,r),t=!0},p(n,[r]){const i={};39&r&&(i.$$scope={dirty:r,ctx:n}),e.$set(i)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){M(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}const ju=()=>{};function qu(o,e,t){let n,{class:r=""}=e,{sourceFolder:i}=e;const s=nt("chatModel");return o.$$set=l=>{"class"in l&&t(0,r=l.class),"sourceFolder"in l&&t(1,i=l.sourceFolder)},o.$$.update=()=>{2&o.$$.dirty&&t(2,n=i.guidelinesOverLimit?`Workspace guidelines exceeded ${i.guidelinesLengthLimit} character limit`:"Edit workspace guidelines")},[r,i,n,()=>{s.extensionClient.openGuidelines(i.folderRoot)}]}class Ku extends oe{constructor(e){super(),ie(this,e,qu,Hu,se,{class:0,sourceFolder:1})}}function Wu(o){let e,t;return{c(){e=F("svg"),t=F("ellipse"),w(t,"cx","51"),w(t,"cy","4.5"),w(t,"rx","51"),w(t,"ry","4.5"),w(t,"fill","currentColor"),w(e,"width","102"),w(e,"height","9"),w(e,"viewBox","0 0 102 9"),w(e,"fill","none"),w(e,"xmlns","http://www.w3.org/2000/svg")},m(n,r){V(n,e,r),N(e,t)},p:W,i:W,o:W,d(n){n&&B(e)}}}class Ju extends oe{constructor(e){super(),ie(this,e,null,Wu,se,{})}}function Gs(o){let e,t,n;return t=new Ju({}),{c(){e=te("div"),P(t.$$.fragment),w(e,"class","c-augment-logo-animated__shadow svelte-hwxi20")},m(r,i){V(r,e,i),_(t,e,null),n=!0},i(r){n||($(t.$$.fragment,r),n=!0)},o(r){M(t.$$.fragment,r),n=!1},d(r){r&&B(e),R(t)}}}function Uu(o){let e,t,n,r;const i=o[8].default,s=Te(i,o,o[7],null),l=s||function(c){let d,h;return d=new gc({}),{c(){P(d.$$.fragment)},m(u,f){_(d,u,f),h=!0},i(u){h||($(d.$$.fragment,u),h=!0)},o(u){M(d.$$.fragment,u),h=!1},d(u){R(d,u)}}}();let a=o[0]&&Gs();return{c(){e=te("div"),t=te("div"),l&&l.c(),n=le(),a&&a.c(),w(t,"class","c-augment-logo-animated__icon svelte-hwxi20"),w(e,"class","c-augment-logo-animated svelte-hwxi20"),w(e,"style",o[2]),Oe(e,"c-augment-logo-animated--animated",o[1])},m(c,d){V(c,e,d),N(e,t),l&&l.m(t,null),N(e,n),a&&a.m(e,null),r=!0},p(c,[d]){s&&s.p&&(!r||128&d)&&Ne(s,i,c,c[7],r?Ae(i,c[7],d,null):Le(c[7]),null),c[0]?a?1&d&&$(a,1):(a=Gs(),a.c(),$(a,1),a.m(e,null)):a&&(ae(),M(a,1,1,()=>{a=null}),ce()),(!r||4&d)&&w(e,"style",c[2]),(!r||2&d)&&Oe(e,"c-augment-logo-animated--animated",c[1])},i(c){r||($(l,c),$(a),r=!0)},o(c){M(l,c),M(a),r=!1},d(c){c&&B(e),l&&l.d(c),a&&a.d()}}}function Gu(o,e,t){let n,r,{$$slots:i={},$$scope:s}=e,{heightPx:l=160}=e,{floatHeight:a=20}=e,{animationDuration:c=3}=e,{showShadow:d=!0}=e,{animated:h=!0}=e;return o.$$set=u=>{"heightPx"in u&&t(3,l=u.heightPx),"floatHeight"in u&&t(4,a=u.floatHeight),"animationDuration"in u&&t(5,c=u.animationDuration),"showShadow"in u&&t(0,d=u.showShadow),"animated"in u&&t(1,h=u.animated),"$$scope"in u&&t(7,s=u.$$scope)},o.$$.update=()=>{8&o.$$.dirty&&t(6,n=Math.round(.875*l)),120&o.$$.dirty&&t(2,r=`
    --augment-logo-height: ${l}px;
    --augment-logo-icon-size: ${n}px;
    --augment-logo-float-height: ${a}px;
    --animation-duration: ${c}s;
  `)},[d,h,r,l,a,c,n,s,i]}class Zu extends oe{constructor(e){super(),ie(this,e,Gu,Uu,se,{heightPx:3,floatHeight:4,animationDuration:5,showShadow:0,animated:1})}}function Yu(o){let e,t;return{c(){e=F("svg"),t=F("path"),w(t,"fill-rule","evenodd"),w(t,"clip-rule","evenodd"),w(t,"d","M14.5 2H9L8.65002 2.15002L8 2.79004L7.34998 2.15002L7 2H1.5L1 2.5V12.5L1.5 13H6.78998L7.65002 13.85H8.34998L9.21002 13H14.5L15 12.5V2.5L14.5 2ZM7.5 12.3199L7.32001 12.15L7 12H2V3H6.78998L7.53003 3.73999L7.5 12.3199ZM14 12H9L8.65002 12.15L8.51001 12.28V3.69995L9.21002 3H14V12ZM6 5H3V6H6V5ZM6 9H3V10H6V9ZM3 7H6V8H3V7ZM13 5H10V6H13V5ZM10 7H13V8H10V7ZM10 9H13V10H10V9Z"),w(t,"fill","currentColor"),w(e,"width","16"),w(e,"height","16"),w(e,"viewBox","0 0 16 16"),w(e,"fill","none"),w(e,"xmlns","http://www.w3.org/2000/svg")},m(n,r){V(n,e,r),N(e,t)},p:W,i:W,o:W,d(n){n&&B(e)}}}class Xu extends oe{constructor(e){super(),ie(this,e,null,Yu,se,{})}}function Qu(o){let e,t,n,r;return{c(){e=F("svg"),t=F("path"),n=F("path"),r=F("path"),w(t,"fill-rule","evenodd"),w(t,"clip-rule","evenodd"),w(t,"d","M7.70996 3H14.5L15.01 3.5V9V13.5L14.51 14H8.74284C8.99647 13.6929 9.21739 13.3578 9.40029 13H13.99V11.49L14 7.48999V5.98999H7.68994L7.67296 6.00697C7.39684 5.81162 7.10191 5.64108 6.79144 5.4986L7.14001 5.15002L7.48999 5H13.99L14 4.01001H7.5L7.14001 3.85999L6.29004 3.01001H2V5.59971C1.6461 5.78062 1.31438 5.99874 1.01001 6.24892V2.5L1.51001 2H6.51001L6.85999 2.15002L7.70996 3Z"),w(t,"fill","currentColor"),w(n,"d","M6 10.5C6 11.3284 5.32843 12 4.5 12C3.67157 12 3 11.3284 3 10.5C3 9.67157 3.67157 9 4.5 9C5.32843 9 6 9.67157 6 10.5Z"),w(n,"fill","currentColor"),w(r,"fill-rule","evenodd"),w(r,"clip-rule","evenodd"),w(r,"d","M8 10.5C8 12.433 6.433 14 4.5 14C2.567 14 1 12.433 1 10.5C1 8.567 2.567 7 4.5 7C6.433 7 8 8.567 8 10.5ZM4.5 13C5.88071 13 7 11.8807 7 10.5C7 9.11929 5.88071 8 4.5 8C3.11929 8 2 9.11929 2 10.5C2 11.8807 3.11929 13 4.5 13Z"),w(r,"fill","currentColor"),w(e,"width","16"),w(e,"height","16"),w(e,"viewBox","0 0 16 16"),w(e,"fill","none"),w(e,"xmlns","http://www.w3.org/2000/svg")},m(i,s){V(i,e,s),N(e,t),N(e,n),N(e,r)},p:W,i:W,o:W,d(i){i&&B(e)}}}class ep extends oe{constructor(e){super(),ie(this,e,null,Qu,se,{})}}function tp(o){let e,t=o[0].label+"";return{c(){e=qe(t)},m(n,r){V(n,e,r)},p(n,r){1&r&&t!==(t=n[0].label+"")&&je(e,t)},i:W,o:W,d(n){n&&B(e)}}}function np(o){let e,t,n,r;return e=new Ka({}),n=new Ie({props:{filepath:`${Jo}/${Uo}/${o[0].rule.path}`}}),{c(){P(e.$$.fragment),t=le(),P(n.$$.fragment)},m(i,s){_(e,i,s),V(i,t,s),_(n,i,s),r=!0},p(i,s){const l={};1&s&&(l.filepath=`${Jo}/${Uo}/${i[0].rule.path}`),n.$set(l)},i(i){r||($(e.$$.fragment,i),$(n.$$.fragment,i),r=!0)},o(i){M(e.$$.fragment,i),M(n.$$.fragment,i),r=!1},d(i){i&&B(t),R(e,i),R(n,i)}}}function rp(o){let e,t,n,r,i=o[0].task.taskTree.description&&o[0].task.taskTree.description.trim();t=new Mt({props:{size:2,weight:"bold",$$slots:{default:[up]},$$scope:{ctx:o}}});let s=i&&Zs(o);return{c(){e=te("div"),P(t.$$.fragment),n=le(),s&&s.c(),w(e,"class","c-mention-hover-contents__task svelte-p7en3g")},m(l,a){V(l,e,a),_(t,e,null),N(e,n),s&&s.m(e,null),r=!0},p(l,a){const c={};3&a&&(c.$$scope={dirty:a,ctx:l}),t.$set(c),1&a&&(i=l[0].task.taskTree.description&&l[0].task.taskTree.description.trim()),i?s?(s.p(l,a),1&a&&$(s,1)):(s=Zs(l),s.c(),$(s,1),s.m(e,null)):s&&(ae(),M(s,1,1,()=>{s=null}),ce())},i(l){r||($(t.$$.fragment,l),$(s),r=!0)},o(l){M(t.$$.fragment,l),M(s),r=!1},d(l){l&&B(e),R(t),s&&s.d()}}}function op(o){let e,t,n,r,i,s,l,a;return t=new Zu({props:{heightPx:32,floatHeight:4,animationDuration:2.25,$$slots:{default:[fp]},$$scope:{ctx:o}}}),i=new Mt({props:{size:2,weight:"medium",$$slots:{default:[mp]},$$scope:{ctx:o}}}),l=new Mt({props:{size:1,$$slots:{default:[gp]},$$scope:{ctx:o}}}),{c(){e=te("div"),P(t.$$.fragment),n=le(),r=te("div"),P(i.$$.fragment),s=le(),P(l.$$.fragment),w(e,"class","c-mention-hover-contents__personality-icon svelte-p7en3g"),w(r,"class","c-mention-hover-contents__personality svelte-p7en3g")},m(c,d){V(c,e,d),_(t,e,null),V(c,n,d),V(c,r,d),_(i,r,null),N(r,s),_(l,r,null),a=!0},p(c,d){const h={};3&d&&(h.$$scope={dirty:d,ctx:c}),t.$set(h);const u={};3&d&&(u.$$scope={dirty:d,ctx:c}),i.$set(u);const f={};3&d&&(f.$$scope={dirty:d,ctx:c}),l.$set(f)},i(c){a||($(t.$$.fragment,c),$(i.$$.fragment,c),$(l.$$.fragment,c),a=!0)},o(c){M(t.$$.fragment,c),M(i.$$.fragment,c),M(l.$$.fragment,c),a=!1},d(c){c&&(B(e),B(n),B(r)),R(t),R(i),R(l)}}}function ip(o){var i,s;let e,t,n,r;return e=new zu({}),n=new Ie({props:{filepath:`${o[0].selection.pathName}:L${(i=o[0].selection.fullRange)==null?void 0:i.startLineNumber}-${(s=o[0].selection.fullRange)==null?void 0:s.endLineNumber}`}}),{c(){P(e.$$.fragment),t=le(),P(n.$$.fragment)},m(l,a){_(e,l,a),V(l,t,a),_(n,l,a),r=!0},p(l,a){var d,h;const c={};1&a&&(c.filepath=`${l[0].selection.pathName}:L${(d=l[0].selection.fullRange)==null?void 0:d.startLineNumber}-${(h=l[0].selection.fullRange)==null?void 0:h.endLineNumber}`),n.$set(c)},i(l){r||($(e.$$.fragment,l),$(n.$$.fragment,l),r=!0)},o(l){M(e.$$.fragment,l),M(n.$$.fragment,l),r=!1},d(l){l&&B(t),R(e,l),R(n,l)}}}function sp(o){var r;let e,t,n=(o[0].userGuidelines.overLimit||((r=o[0].rulesAndGuidelinesState)==null?void 0:r.overLimit))&&Ys(o);return{c(){n&&n.c(),e=dt()},m(i,s){n&&n.m(i,s),V(i,e,s),t=!0},p(i,s){var l;i[0].userGuidelines.overLimit||(l=i[0].rulesAndGuidelinesState)!=null&&l.overLimit?n?(n.p(i,s),1&s&&$(n,1)):(n=Ys(i),n.c(),$(n,1),n.m(e.parentNode,e)):n&&(ae(),M(n,1,1,()=>{n=null}),ce())},i(i){t||($(n),t=!0)},o(i){M(n),t=!1},d(i){i&&B(e),n&&n.d(i)}}}function lp(o){let e,t,n,r,i,s,l,a;return n=new ep({}),i=new Ie({props:{class:"c-source-folder-item",filepath:o[0].sourceFolder.folderRoot}}),l=new Ku({props:{class:"guidelines-filespan",sourceFolder:o[0].sourceFolder}}),{c(){e=te("div"),t=te("div"),P(n.$$.fragment),r=le(),P(i.$$.fragment),s=le(),P(l.$$.fragment),w(t,"class","l-source-folder-name svelte-p7en3g"),w(e,"class","l-mention-hover-contents__source-folder")},m(c,d){V(c,e,d),N(e,t),_(n,t,null),N(t,r),_(i,t,null),N(e,s),_(l,e,null),a=!0},p(c,d){const h={};1&d&&(h.filepath=c[0].sourceFolder.folderRoot),i.$set(h);const u={};1&d&&(u.sourceFolder=c[0].sourceFolder),l.$set(u)},i(c){a||($(n.$$.fragment,c),$(i.$$.fragment,c),$(l.$$.fragment,c),a=!0)},o(c){M(n.$$.fragment,c),M(i.$$.fragment,c),M(l.$$.fragment,c),a=!1},d(c){c&&B(e),R(n),R(i),R(l)}}}function ap(o){let e,t,n,r;return e=new Xu({}),n=new Ie({props:{filepath:o[0].externalSource.name}}),{c(){P(e.$$.fragment),t=le(),P(n.$$.fragment)},m(i,s){_(e,i,s),V(i,t,s),_(n,i,s),r=!0},p(i,s){const l={};1&s&&(l.filepath=i[0].externalSource.name),n.$set(l)},i(i){r||($(e.$$.fragment,i),$(n.$$.fragment,i),r=!0)},o(i){M(e.$$.fragment,i),M(n.$$.fragment,i),r=!1},d(i){i&&B(t),R(e,i),R(n,i)}}}function cp(o){let e,t,n,r;return e=new yc({}),n=new Ie({props:{filepath:o[0].folder.pathName}}),{c(){P(e.$$.fragment),t=le(),P(n.$$.fragment)},m(i,s){_(e,i,s),V(i,t,s),_(n,i,s),r=!0},p(i,s){const l={};1&s&&(l.filepath=i[0].folder.pathName),n.$set(l)},i(i){r||($(e.$$.fragment,i),$(n.$$.fragment,i),r=!0)},o(i){M(e.$$.fragment,i),M(n.$$.fragment,i),r=!1},d(i){i&&B(t),R(e,i),R(n,i)}}}function dp(o){let e,t,n,r;return e=new ml({}),n=new Ie({props:{filepath:o[0].recentFile.pathName}}),{c(){P(e.$$.fragment),t=le(),P(n.$$.fragment)},m(i,s){_(e,i,s),V(i,t,s),_(n,i,s),r=!0},p(i,s){const l={};1&s&&(l.filepath=i[0].recentFile.pathName),n.$set(l)},i(i){r||($(e.$$.fragment,i),$(n.$$.fragment,i),r=!0)},o(i){M(e.$$.fragment,i),M(n.$$.fragment,i),r=!1},d(i){i&&B(t),R(e,i),R(n,i)}}}function hp(o){let e,t,n,r;return e=new ml({}),n=new Ie({props:{filepath:o[0].file.pathName}}),{c(){P(e.$$.fragment),t=le(),P(n.$$.fragment)},m(i,s){_(e,i,s),V(i,t,s),_(n,i,s),r=!0},p(i,s){const l={};1&s&&(l.filepath=i[0].file.pathName),n.$set(l)},i(i){r||($(e.$$.fragment,i),$(n.$$.fragment,i),r=!0)},o(i){M(e.$$.fragment,i),M(n.$$.fragment,i),r=!1},d(i){i&&B(t),R(e,i),R(n,i)}}}function up(o){let e,t=o[0].task.taskTree.name+"";return{c(){e=qe(t)},m(n,r){V(n,e,r)},p(n,r){1&r&&t!==(t=n[0].task.taskTree.name+"")&&je(e,t)},d(n){n&&B(e)}}}function Zs(o){let e,t;return e=new Mt({props:{size:1,$$slots:{default:[pp]},$$scope:{ctx:o}}}),{c(){P(e.$$.fragment)},m(n,r){_(e,n,r),t=!0},p(n,r){const i={};3&r&&(i.$$scope={dirty:r,ctx:n}),e.$set(i)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){M(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function pp(o){let e,t=o[0].task.taskTree.description.trim().replace(/\s+/g," ")+"";return{c(){e=qe(t)},m(n,r){V(n,e,r)},p(n,r){1&r&&t!==(t=n[0].task.taskTree.description.trim().replace(/\s+/g," ")+"")&&je(e,t)},d(n){n&&B(e)}}}function fp(o){let e,t,n;var r=ur(o[0].personality.type);return r&&(e=un(r,{})),{c(){e&&P(e.$$.fragment),t=dt()},m(i,s){e&&_(e,i,s),V(i,t,s),n=!0},p(i,s){if(1&s&&r!==(r=ur(i[0].personality.type))){if(e){ae();const l=e;M(l.$$.fragment,1,0,()=>{R(l,1)}),ce()}r?(e=un(r,{}),P(e.$$.fragment),$(e.$$.fragment,1),_(e,t.parentNode,t)):e=null}},i(i){n||(e&&$(e.$$.fragment,i),n=!0)},o(i){e&&M(e.$$.fragment,i),n=!1},d(i){i&&B(t),e&&R(e,i)}}}function mp(o){let e,t=o[0].label+"";return{c(){e=qe(t)},m(n,r){V(n,e,r)},p(n,r){1&r&&t!==(t=n[0].label+"")&&je(e,t)},d(n){n&&B(e)}}}function gp(o){let e,t=o[0].personality.description+"";return{c(){e=qe(t)},m(n,r){V(n,e,r)},p(n,r){1&r&&t!==(t=n[0].personality.description+"")&&je(e,t)},d(n){n&&B(e)}}}function Ys(o){let e,t,n,r;const i=[Cp,yp],s=[];function l(a,c){var d;return(d=a[0].rulesAndGuidelinesState)!=null&&d.overLimit?0:a[0].userGuidelines.overLimit?1:-1}return~(e=l(o))&&(t=s[e]=i[e](o)),{c(){t&&t.c(),n=dt()},m(a,c){~e&&s[e].m(a,c),V(a,n,c),r=!0},p(a,c){let d=e;e=l(a),e===d?~e&&s[e].p(a,c):(t&&(ae(),M(s[d],1,1,()=>{s[d]=null}),ce()),~e?(t=s[e],t?t.p(a,c):(t=s[e]=i[e](a),t.c()),$(t,1),t.m(n.parentNode,n)):t=null)},i(a){r||($(t),r=!0)},o(a){M(t),r=!1},d(a){a&&B(n),~e&&s[e].d(a)}}}function yp(o){let e,t;return e=new Mt({props:{size:1,$$slots:{default:[wp]},$$scope:{ctx:o}}}),{c(){P(e.$$.fragment)},m(n,r){_(e,n,r),t=!0},p(n,r){const i={};3&r&&(i.$$scope={dirty:r,ctx:n}),e.$set(i)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){M(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Cp(o){let e,t;return e=new Mt({props:{size:1,$$slots:{default:[vp]},$$scope:{ctx:o}}}),{c(){P(e.$$.fragment)},m(n,r){_(e,n,r),t=!0},p(n,r){const i={};3&r&&(i.$$scope={dirty:r,ctx:n}),e.$set(i)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){M(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function wp(o){let e,t=`Guidelines exceeded length limit of ${o[0].userGuidelines.lengthLimit} characters`;return{c(){e=qe(t)},m(n,r){V(n,e,r)},p(n,r){1&r&&t!==(t=`Guidelines exceeded length limit of ${n[0].userGuidelines.lengthLimit} characters`)&&je(e,t)},d(n){n&&B(e)}}}function vp(o){let e,t=`Rules and workspace guidelines (${o[0].rulesAndGuidelinesState.totalCharacterCount} chars)
          exceeded limit of ${o[0].rulesAndGuidelinesState.lengthLimit} characters, remove some rules
          or reduce the length of your guidelines.`;return{c(){e=qe(t)},m(n,r){V(n,e,r)},p(n,r){1&r&&t!==(t=`Rules and workspace guidelines (${n[0].rulesAndGuidelinesState.totalCharacterCount} chars)
          exceeded limit of ${n[0].rulesAndGuidelinesState.lengthLimit} characters, remove some rules
          or reduce the length of your guidelines.`)&&je(e,t)},d(n){n&&B(e)}}}function xp(o){let e,t,n,r,i,s,l,a,c,d,h,u,f,p;const m=[hp,dp,cp,ap,lp,sp,ip,op,rp,np,tp],g=[];function y(C,v){return 1&v&&(t=null),1&v&&(n=null),1&v&&(r=null),1&v&&(i=null),1&v&&(s=null),1&v&&(l=null),1&v&&(a=null),1&v&&(c=null),1&v&&(d=null),1&v&&(h=null),t==null&&(t=!(!C[0]||!sl(C[0]))),t?0:(n==null&&(n=!(!C[0]||!hl(C[0]))),n?1:(r==null&&(r=!(!C[0]||!ll(C[0]))),r?2:(i==null&&(i=!(!C[0]||!al(C[0]))),i?3:(s==null&&(s=!(!C[0]||!cl(C[0]))),s?4:(l==null&&(l=!(!C[0]||!fl(C[0]))),l?5:(a==null&&(a=!(!C[0]||!dl(C[0]))),a?6:(c==null&&(c=!(!C[0]||!pl(C[0]))),c?7:(d==null&&(d=!(!C[0]||!pc(C[0]))),d?8:(h==null&&(h=!(!C[0]||!ul(C[0]))),h?9:10)))))))))}return u=y(o,-1),f=g[u]=m[u](o),{c(){e=te("div"),f.c(),w(e,"class","c-mention-hover-contents svelte-p7en3g")},m(C,v){V(C,e,v),g[u].m(e,null),p=!0},p(C,[v]){let b=u;u=y(C,v),u===b?g[u].p(C,v):(ae(),M(g[b],1,1,()=>{g[b]=null}),ce(),f=g[u],f?f.p(C,v):(f=g[u]=m[u](C),f.c()),$(f,1),f.m(e,null))},i(C){p||($(f),p=!0)},o(C){M(f),p=!1},d(C){C&&B(e),g[u].d()}}}function bp(o,e,t){let{option:n}=e;return o.$$set=r=>{"option"in r&&t(0,n=r.option)},[n]}class $p extends oe{constructor(e){super(),ie(this,e,bp,xp,se,{option:0})}}function Xs(o,e,t){const n=o.slice();return n[15]=e[t],n}function Qs(o){let e,t;function n(){return o[8](o[15])}return e=new _u({props:{item:o[15],highlight:o[15]===o[14],onSelect:n}}),{c(){P(e.$$.fragment)},m(r,i){_(e,r,i),t=!0},p(r,i){o=r;const s={};4&i&&(s.item=o[15]),16388&i&&(s.highlight=o[15]===o[14]),4&i&&(s.onSelect=n),e.$set(s)},i(r){t||($(e.$$.fragment,r),t=!0)},o(r){M(e.$$.fragment,r),t=!1},d(r){R(e,r)}}}function kp(o){let e,t,n=Ko(o[2]),r=[];for(let s=0;s<n.length;s+=1)r[s]=Qs(Xs(o,n,s));const i=s=>M(r[s],1,1,()=>{r[s]=null});return{c(){for(let s=0;s<r.length;s+=1)r[s].c();e=dt()},m(s,l){for(let a=0;a<r.length;a+=1)r[a]&&r[a].m(s,l);V(s,e,l),t=!0},p(s,l){if(16420&l){let a;for(n=Ko(s[2]),a=0;a<n.length;a+=1){const c=Xs(s,n,a);r[a]?(r[a].p(c,l),$(r[a],1)):(r[a]=Qs(c),r[a].c(),$(r[a],1),r[a].m(e.parentNode,e))}for(ae(),a=n.length;a<r.length;a+=1)i(a);ce()}},i(s){if(!t){for(let l=0;l<n.length;l+=1)$(r[l]);t=!0}},o(s){r=r.filter(Boolean);for(let l=0;l<r.length;l+=1)M(r[l]);t=!1},d(s){s&&B(e),sc(r,s)}}}function Mp(o){let e,t;return e=new $p({props:{slot:"mentionable",option:o[13]}}),{c(){P(e.$$.fragment)},m(n,r){_(e,n,r),t=!0},p(n,r){const i={};8192&r&&(i.option=n[13]),e.$set(i)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){M(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Sp(o){let e,t,n,r;return e=new $o.Menu.Root({props:{mentionables:o[2],onQueryUpdate:o[4],onSelectMentionable:o[5],$$slots:{default:[kp,({activeItem:i})=>({14:i}),({activeItem:i})=>i?16384:0]},$$scope:{ctx:o}}}),n=new $o.ChipTooltip({props:{$$slots:{mentionable:[Mp,({mentionable:i})=>({13:i}),({mentionable:i})=>i?8192:0]},$$scope:{ctx:o}}}),{c(){P(e.$$.fragment),t=le(),P(n.$$.fragment)},m(i,s){_(e,i,s),V(i,t,s),_(n,i,s),r=!0},p(i,s){const l={};4&s&&(l.mentionables=i[2]),278532&s&&(l.$$scope={dirty:s,ctx:i}),e.$set(l);const a={};270336&s&&(a.$$scope={dirty:s,ctx:i}),n.$set(a)},i(i){r||($(e.$$.fragment,i),$(n.$$.fragment,i),r=!0)},o(i){M(e.$$.fragment,i),M(n.$$.fragment,i),r=!1},d(i){i&&B(t),R(e,i),R(n,i)}}}function Op(o){let e,t,n={triggerCharacter:"@",onMentionItemsUpdated:o[0],$$slots:{default:[Sp]},$$scope:{ctx:o}};return e=new $o.Root({props:n}),o[9](e),{c(){P(e.$$.fragment)},m(r,i){_(e,r,i),t=!0},p(r,[i]){const s={};1&i&&(s.onMentionItemsUpdated=r[0]),262148&i&&(s.$$scope={dirty:i,ctx:r}),e.$set(s)},i(r){t||($(e.$$.fragment,r),t=!0)},o(r){M(e.$$.fragment,r),t=!1},d(r){o[9](null),R(e,r)}}}function Ep(o,e,t){let n,{requestEditorFocus:r}=e,{onMentionItemsUpdated:i}=e;const s=nt("chatModel");if(!s)throw new Error("ChatModel not found in context");const l=new ko(s,d),a=l.displayItems;let c;function d(u){return!!c&&(c.insertMention(u),l.closeDropdown(),!0)}function h(u){const f=l.selectMentionable(u);return r(),f}return Ct(o,a,u=>t(2,n=u)),Pn(()=>{l.dispose()}),o.$$set=u=>{"requestEditorFocus"in u&&t(6,r=u.requestEditorFocus),"onMentionItemsUpdated"in u&&t(0,i=u.onMentionItemsUpdated)},[i,c,n,a,function(u){u===void 0?l.closeDropdown():(l.openDropdown(),l.userQuery.set(u))},h,r,u=>d(u),u=>h(u),function(u){Oo[u?"unshift":"push"](()=>{c=u,t(1,c)})}]}class Yp extends oe{constructor(e){super(),ie(this,e,Ep,Op,se,{requestEditorFocus:6,onMentionItemsUpdated:0,insertMentionNode:7})}get insertMentionNode(){return this.$$.ctx[7]}}const Tp=He.create({name:"placeholder",addOptions:()=>({emptyEditorClass:"is-editor-empty",emptyNodeClass:"is-empty",placeholder:"Write something …",showOnlyWhenEditable:!0,considerAnyAsEmpty:!1,showOnlyCurrent:!0,includeChildren:!1}),addProseMirrorPlugins(){return[new Fe({key:new ze("placeholder"),props:{decorations:({doc:o,selection:e})=>{var t;const n=this.editor.isEditable||!this.options.showOnlyWhenEditable,{anchor:r}=e,i=[];if(!n)return null;const{firstChild:s}=o.content,l=s&&s.type.isLeaf,a=s&&s.isAtom,c=!!this.options.considerAnyAsEmpty||s&&s.type.name===((t=o.type.contentMatch.defaultType)===null||t===void 0?void 0:t.name),d=o.content.childCount<=1&&s&&c&&s.nodeSize<=2&&(!l||!a);return o.descendants((h,u)=>{const f=r>=u&&r<=u+h.nodeSize,p=!h.isLeaf&&!h.childCount;if((f||!this.options.showOnlyCurrent)&&p){const m=[this.options.emptyNodeClass];d&&m.push(this.options.emptyEditorClass);const g=Pe.node(u,u+h.nodeSize,{class:m.join(" "),"data-placeholder":typeof this.options.placeholder=="function"?this.options.placeholder({editor:this.editor,node:h,pos:u,hasAnchor:f}):this.options.placeholder});i.push(g)}return this.options.includeChildren}),de.create(o,i)}}})]}});class Np{constructor(e){x(this,"_placeholderExtension");x(this,"_editor");x(this,"setPlaceholder",e=>{var t;this._placeholderExtension.options.placeholder=e,(t=this._editor)==null||t.view.updateState(this._editor.view.state)});x(this,"_onUpdate",e=>{this._editor=e});x(this,"_onDestroy",()=>{this._editor=void 0});const t=this._onUpdate.bind(this),n=this._onDestroy.bind(this);this._placeholderExtension=Tp.extend({placeholder:e,onUpdate(){var r;(r=this.parent)==null||r.call(this),t(this.editor)},onDestroy(){var r;(r=this.parent)==null||r.call(this),n()}})}get tipTapExtension(){return this._placeholderExtension}}function Lp(o,e,t){let{placeholder:n="Type something..."}=e;const r=new Np(n),i=nt(Ge.CONTEXT_KEY);return Pn(i.pluginManager.registerPlugin(r)),o.$$set=s=>{"placeholder"in s&&t(0,n=s.placeholder)},o.$$.update=()=>{1&o.$$.dirty&&r.setPlaceholder(n)},[n]}class Xp extends oe{constructor(e){super(),ie(this,e,Lp,null,se,{placeholder:0})}}const Xe=class Xe{constructor(e){x(this,"_tipTapExtension");x(this,"_keydownHandler",()=>!1);x(this,"updateOptions",e=>{this._options={...this._options,...e},this._keydownHandler=da(this._options.shortcuts)});x(this,"_handleKeyDown",(e,t)=>this._keydownHandler(e,t));this._options=e,this.updateOptions(this._options);const t=this._handleKeyDown,n=Xe._getNextPluginId(),r=new ze(n);this._tipTapExtension=He.create({name:n,addProseMirrorPlugins:()=>[new Fe({key:r,props:{handleKeyDown:t}})]})}get tipTapExtension(){return this._tipTapExtension}};x(Xe,"_sequenceId",0),x(Xe,"KEYBINDINGS_PLUGIN_KEY_BASE","augment-keybindings-plugin-{}"),x(Xe,"_getSequenceId",()=>Xe._sequenceId++),x(Xe,"_getNextPluginId",()=>{const e=Xe._getSequenceId().toString();return Xe.KEYBINDINGS_PLUGIN_KEY_BASE.replace("{}",e)});let Mo=Xe;function Ap(o,e,t){let{shortcuts:n={}}=e;const r=nt(Ge.CONTEXT_KEY),i=new Mo({shortcuts:n}),s=r.pluginManager.registerPlugin(i);return Pn(s),o.$$set=l=>{"shortcuts"in l&&t(0,n=l.shortcuts)},o.$$.update=()=>{1&o.$$.dirty&&i.updateOptions({shortcuts:n})},[n]}class Qp extends oe{constructor(e){super(),ie(this,e,Ap,null,se,{shortcuts:0})}}export{Yp as A,$p as C,He as E,Qp as K,Ot as N,Xp as P,Zp as R,G as T,Ge as a,ze as b,Fe as c,Ka as d,ko as e,_u as f,Gp as g,hu as h,Zu as i,Pt as m,Up as n};
