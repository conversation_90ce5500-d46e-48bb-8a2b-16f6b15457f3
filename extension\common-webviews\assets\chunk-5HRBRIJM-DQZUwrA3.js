import{_ as d,d as r,e as c,l as x}from"./AugmentMessage-C8cOeLWa.js";var $=d((t,e)=>{let i;return e==="sandbox"&&(i=r("#i"+t)),r(e==="sandbox"?i.nodes()[0].contentDocument.body:"body").select(`[id="${t}"]`)},"getDiagramElement"),m=d((t,e,i,o)=>{t.attr("class",i);const{width:a,height:n,x:h,y:g}=w(t,e);c(t,n,a,o);const s=l(h,g,a,n,e);t.attr("viewBox",s),x.debug(`viewBox configured: ${s} with padding: ${e}`)},"setupViewPortForSVG"),w=d((t,e)=>{var o;const i=((o=t.node())==null?void 0:o.getBBox())||{width:0,height:0,x:0,y:0};return{width:i.width+2*e,height:i.height+2*e,x:i.x,y:i.y}},"calculateDimensionsWithPadding"),l=d((t,e,i,o,a)=>`${t-a} ${e-a} ${i} ${o}`,"createViewBox");export{$ as g,m as s};
