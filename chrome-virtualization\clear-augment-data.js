// Chrome数据清理脚本 - 专门针对AugmentCode
// 使用方法：在Chrome控制台中运行此脚本

console.log('🧹 开始清理AugmentCode相关数据...');

// AugmentCode相关网址
const AUGMENT_URLS = [
    'https://app.augmentcode.com/',
    'https://login.augmentcode.com/',
    'https://www.augmentcode.com/',
    'https://api.augmentcode.com/',
    'https://*.augmentcode.com/'
];

// 1. 清理Cache Storage
async function clearCacheStorage() {
    console.log('🗂️ 清理Cache Storage...');
    try {
        const cacheNames = await caches.keys();
        for (const cacheName of cacheNames) {
            // 检查是否包含augmentcode
            if (cacheName.includes('augmentcode') || cacheName.includes('augment')) {
                await caches.delete(cacheName);
                console.log(`✅ 已删除缓存: ${cacheName}`);
            }
        }
    } catch (error) {
        console.log('❌ 清理Cache Storage失败:', error);
    }
}

// 2. 清理Session Storage
function clearSessionStorage() {
    console.log('📝 清理Session Storage...');
    try {
        const keys = Object.keys(sessionStorage);
        let clearedCount = 0;
        
        for (const key of keys) {
            if (key.includes('augmentcode') || key.includes('augment') || 
                sessionStorage.getItem(key)?.includes('augmentcode')) {
                sessionStorage.removeItem(key);
                clearedCount++;
            }
        }
        console.log(`✅ 已清理 ${clearedCount} 个Session Storage项`);
    } catch (error) {
        console.log('❌ 清理Session Storage失败:', error);
    }
}

// 3. 清理Local Storage
function clearLocalStorage() {
    console.log('💾 清理Local Storage...');
    try {
        const keys = Object.keys(localStorage);
        let clearedCount = 0;
        
        for (const key of keys) {
            if (key.includes('augmentcode') || key.includes('augment') || 
                localStorage.getItem(key)?.includes('augmentcode')) {
                localStorage.removeItem(key);
                clearedCount++;
            }
        }
        console.log(`✅ 已清理 ${clearedCount} 个Local Storage项`);
    } catch (error) {
        console.log('❌ 清理Local Storage失败:', error);
    }
}

// 4. 清理Cookies
function clearCookies() {
    console.log('🍪 清理Cookies...');
    try {
        const cookies = document.cookie.split(';');
        let clearedCount = 0;
        
        for (const cookie of cookies) {
            const [name] = cookie.split('=');
            const trimmedName = name.trim();
            
            if (trimmedName.includes('augmentcode') || trimmedName.includes('augment') ||
                trimmedName.includes('session') || trimmedName.includes('token') ||
                trimmedName.includes('auth') || trimmedName.includes('user')) {
                
                // 删除cookie
                document.cookie = `${trimmedName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
                document.cookie = `${trimmedName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.augmentcode.com`;
                document.cookie = `${trimmedName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=augmentcode.com`;
                clearedCount++;
            }
        }
        console.log(`✅ 已清理 ${clearedCount} 个Cookies`);
    } catch (error) {
        console.log('❌ 清理Cookies失败:', error);
    }
}

// 5. 清理IndexedDB
async function clearIndexedDB() {
    console.log('🗄️ 清理IndexedDB...');
    try {
        const databases = await window.indexedDB.databases();
        let clearedCount = 0;
        
        for (const db of databases) {
            if (db.name.includes('augmentcode') || db.name.includes('augment')) {
                await window.indexedDB.deleteDatabase(db.name);
                clearedCount++;
                console.log(`✅ 已删除数据库: ${db.name}`);
            }
        }
        console.log(`✅ 已清理 ${clearedCount} 个IndexedDB数据库`);
    } catch (error) {
        console.log('❌ 清理IndexedDB失败:', error);
    }
}

// 6. 清理Service Workers
async function clearServiceWorkers() {
    console.log('⚙️ 清理Service Workers...');
    try {
        if ('serviceWorker' in navigator) {
            const registrations = await navigator.serviceWorker.getRegistrations();
            let clearedCount = 0;
            
            for (const registration of registrations) {
                if (registration.scope.includes('augmentcode') || 
                    registration.active?.scriptURL.includes('augmentcode')) {
                    await registration.unregister();
                    clearedCount++;
                }
            }
            console.log(`✅ 已清理 ${clearedCount} 个Service Workers`);
        }
    } catch (error) {
        console.log('❌ 清理Service Workers失败:', error);
    }
}

// 7. 清理WebSQL (如果存在)
function clearWebSQL() {
    console.log('🗃️ 清理WebSQL...');
    try {
        if (window.openDatabase) {
            // WebSQL已被废弃，但某些旧版本可能仍在使用
            console.log('ℹ️ WebSQL已废弃，跳过清理');
        }
    } catch (error) {
        console.log('❌ 清理WebSQL失败:', error);
    }
}

// 8. 清理浏览器指纹相关数据
function clearFingerprintData() {
    console.log('👆 清理指纹相关数据...');
    try {
        // 清理Canvas指纹缓存
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (ctx) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }
        
        // 清理WebGL指纹缓存
        const canvas2 = document.createElement('canvas');
        const gl = canvas2.getContext('webgl') || canvas2.getContext('experimental-webgl');
        if (gl) {
            gl.clear(gl.COLOR_BUFFER_BIT);
        }
        
        console.log('✅ 已清理指纹相关数据');
    } catch (error) {
        console.log('❌ 清理指纹数据失败:', error);
    }
}

// 主清理函数
async function clearAllAugmentData() {
    console.log('🚀 开始全面清理AugmentCode数据...');
    console.log('=====================================');
    
    await clearCacheStorage();
    clearSessionStorage();
    clearLocalStorage();
    clearCookies();
    await clearIndexedDB();
    await clearServiceWorkers();
    clearWebSQL();
    clearFingerprintData();
    
    console.log('=====================================');
    console.log('🎉 AugmentCode数据清理完成！');
    console.log('💡 建议刷新页面以确保清理生效');
}

// 自动执行清理
clearAllAugmentData();

// 导出函数供手动调用
window.clearAugmentData = clearAllAugmentData; 