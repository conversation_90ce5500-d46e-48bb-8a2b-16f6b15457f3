import{a1 as h,a2 as F}from"./SpinnerAugment-VfHtkDdv.js";function x(r){const o=r-1;return o*o*o+1}function w(r,{delay:o=0,duration:d=400,easing:p=F}={}){const i=+getComputedStyle(r).opacity;return{delay:o,duration:d,easing:p,css:a=>"opacity: "+a*i}}function C(r,{delay:o=0,duration:d=400,easing:p=x,x:i=0,y:a=0,opacity:l=0}={}){const s=getComputedStyle(r),$=+s.opacity,e=s.transform==="none"?"":s.transform,n=$*(1-l),[y,g]=h(i),[u,m]=h(a);return{delay:o,duration:d,easing:p,css:(c,f)=>`
			transform: ${e} translate(${(1-c)*y}${g}, ${(1-c)*u}${m});
			opacity: ${$-n*f}`}}function S(r,{delay:o=0,duration:d=400,easing:p=x,axis:i="y"}={}){const a=getComputedStyle(r),l=+a.opacity,s=i==="y"?"height":"width",$=parseFloat(a[s]),e=i==="y"?["top","bottom"]:["left","right"],n=e.map(t=>`${t[0].toUpperCase()}${t.slice(1)}`),y=parseFloat(a[`padding${n[0]}`]),g=parseFloat(a[`padding${n[1]}`]),u=parseFloat(a[`margin${n[0]}`]),m=parseFloat(a[`margin${n[1]}`]),c=parseFloat(a[`border${n[0]}Width`]),f=parseFloat(a[`border${n[1]}Width`]);return{delay:o,duration:d,easing:p,css:t=>`overflow: hidden;opacity: ${Math.min(20*t,1)*l};${s}: ${t*$}px;padding-${e[0]}: ${t*y}px;padding-${e[1]}: ${t*g}px;margin-${e[0]}: ${t*u}px;margin-${e[1]}: ${t*m}px;border-${e[0]}-width: ${t*c}px;border-${e[1]}-width: ${t*f}px;`}}export{w as a,x as c,C as f,S as s};
