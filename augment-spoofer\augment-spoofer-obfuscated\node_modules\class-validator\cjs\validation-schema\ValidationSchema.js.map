{"version": 3, "file": "ValidationSchema.js", "sourceRoot": "", "sources": ["../../../src/validation-schema/ValidationSchema.ts"], "names": [], "mappings": "", "sourcesContent": ["/**\n * Validation schema is a decorator-free way of validation of your objects.\n * Also using validation schemas makes this library to be easily used with es6/es5.\n */\nexport interface ValidationSchema {\n  /**\n   * Schema name. This is required, because we tell validator to validate by this schema using its name.\n   */\n  name: string;\n\n  /**\n   * Validated properties.\n   */\n  properties: {\n    /**\n     * Name of the object's property to be validated which holds an array of validation constraints.\n     */\n    [propertyName: string]: {\n      /**\n       * Validation type. Should be one of the ValidationTypes value.\n       */\n      type: string;\n\n      /**\n       * Validator name.\n       */\n      name?: string;\n\n      /**\n       * Constraints set by validation type.\n       */\n      constraints?: any[];\n\n      /**\n       * Error message used to be used on validation fail.\n       * You can use \"$value\" to use value that was failed by validation.\n       * You can use \"$constraint1\" and \"$constraint2\" keys in the message string,\n       * and they will be replaced with constraint values if they exist.\n       * Message can be either string, either a function that returns a string.\n       * Second option allows to use values and custom messages depend of them.\n       */\n      message?: string | ((value?: any, constraint1?: any, constraint2?: any) => string);\n\n      /**\n       * Specifies if validated value is an array and each of its item must be validated.\n       */\n      each?: boolean;\n\n      /**\n       * Indicates if validation must be performed always, no matter of validation groups used.\n       */\n      always?: boolean;\n\n      /**\n       * Validation groups used for this validation.\n       */\n      groups?: string[];\n\n      /**\n       * Specific validation type options.\n       */\n      options?: any;\n    }[];\n  };\n}\n"]}