{"version": 3, "file": "ArrayUnique.js", "sourceRoot": "", "sources": ["../../../../src/decorator/array/ArrayUnique.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,MAAM,YAAY,GAAG,aAAa,CAAC;AAG1C;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,KAAgB,EAAE,UAAkC;IAC9E,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;QAAE,OAAO,KAAK,CAAC;IAExC,IAAI,UAAU,EAAE,CAAC;QACf,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1D,CAAC;IAED,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;IAClE,OAAO,KAAK,CAAC,MAAM,KAAK,WAAW,CAAC,MAAM,CAAC;AAC7C,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,WAAW,CACzB,mBAAkE,EAClE,iBAAqC;IAErC,MAAM,UAAU,GAAG,OAAO,mBAAmB,KAAK,UAAU,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,SAAS,CAAC;IAC/F,MAAM,OAAO,GAAG,OAAO,mBAAmB,KAAK,UAAU,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,iBAAiB,CAAC;IAEpG,OAAO,UAAU,CACf;QACE,IAAI,EAAE,YAAY;QAClB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,UAAU,CAAC;YAClE,cAAc,EAAE,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,yCAAyC,EAAE,OAAO,CAAC;SAC5G;KACF,EACD,OAAO,CACR,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const ARRAY_UNIQUE = 'arrayUnique';\nexport type ArrayUniqueIdentifier<T = any> = (o: T) => any;\n\n/**\n * Checks if all array's values are unique. Comparison for objects is reference-based.\n * If null or undefined is given then this function returns false.\n */\nexport function arrayUnique(array: unknown[], identifier?: ArrayUniqueIdentifier): boolean {\n  if (!Array.isArray(array)) return false;\n\n  if (identifier) {\n    array = array.map(o => (o != null ? identifier(o) : o));\n  }\n\n  const uniqueItems = array.filter((a, b, c) => c.indexOf(a) === b);\n  return array.length === uniqueItems.length;\n}\n\n/**\n * Checks if all array's values are unique. Comparison for objects is reference-based.\n * If null or undefined is given then this function returns false.\n */\nexport function ArrayUnique<T = any>(\n  identifierOrOptions?: ArrayUniqueIdentifier<T> | ValidationOptions,\n  validationOptions?: ValidationOptions\n): PropertyDecorator {\n  const identifier = typeof identifierOrOptions === 'function' ? identifierOrOptions : undefined;\n  const options = typeof identifierOrOptions !== 'function' ? identifierOrOptions : validationOptions;\n\n  return ValidateBy(\n    {\n      name: ARRAY_UNIQUE,\n      validator: {\n        validate: (value, args): boolean => arrayUnique(value, identifier),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + \"All $property's elements must be unique\", options),\n      },\n    },\n    options\n  );\n}\n"]}