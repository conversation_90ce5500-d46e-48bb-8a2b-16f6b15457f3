# 🎭 Augment Environment Spoofer

一个高级的VS Code扩展，用于**动态随机化虚拟化**AugmentCode插件的环境检测。

## 🎯 **功能特性**

### **动态随机化身份**
- ✅ **每次启动**都生成全新的虚拟身份
- ✅ **用户名**、**工作区**、**GitHub账号**完全随机
- ✅ **设备ID**、**会话ID**动态生成
- ✅ **手动刷新**身份功能

### **全方位环境伪装**
- 🌍 **环境变量劫持** - 伪装用户名、路径等
- 🔐 **GitHub认证劫持** - 虚拟GitHub用户和Token
- 🌐 **网络请求劫持** - 修改API请求中的设备信息
- 📁 **工作区信息伪装** - 虚拟项目路径和结构

### **智能反检测**
- 🚫 **阻止环境变量修改** - 保护伪装不被覆盖
- 🔄 **实时劫持** - 动态拦截各种API调用
- 🎲 **随机性极高** - 每次都是完全不同的身份

## 🚀 **安装和使用**

### **1. 编译扩展**
```bash
cd augment-spoofer
npm install
npm run compile
```

### **2. 安装到VS Code**
```bash
# 方法1: 直接复制到扩展目录
cp -r augment-spoofer ~/.vscode/extensions/

# 方法2: 使用VSIX打包
npm install -g vsce
vsce package
code --install-extension augment-spoofer-1.0.0.vsix
```

### **3. 启用扩展**
1. 重启VS Code
2. 扩展会自动激活并显示虚拟身份
3. 使用命令面板控制扩展

## 🎮 **命令说明**

### **Toggle Augment Spoofer**
- **功能**: 切换扩展的启用/停用状态
- **快捷键**: `Ctrl+Shift+P` → 输入 "Toggle Augment Spoofer"

### **Show Spoofer Status**
- **功能**: 显示当前伪装状态和虚拟身份信息
- **快捷键**: `Ctrl+Shift+P` → 输入 "Show Spoofer Status"

### **Refresh Virtual Identity**
- **功能**: 手动刷新虚拟身份（生成新的随机身份）
- **快捷键**: `Ctrl+Shift+P` → 输入 "Refresh Virtual Identity"

## 🔧 **工作原理**

### **1. 身份生成器**
```typescript
// 每次启动生成全新身份
const identity = {
    user: "dev_user_1234",           // 随机用户名
    workspace: "CodeLab",            // 随机项目名
    github: "script_master_567",     // 随机GitHub账号
    device: "a1b2c3d4..."            // 随机设备ID
};
```

### **2. 环境劫持**
```typescript
// 劫持环境变量
process.env.USERNAME = "random_user_1234";
process.env.USERPROFILE = "C:\\Users\\<USER>