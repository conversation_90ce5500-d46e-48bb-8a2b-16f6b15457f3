import * as vscode from 'vscode';
import { DynamicIdentityGenerator } from './utils/random-generator';

export class GitHubAuthSpoofer {
    private identityGenerator = DynamicIdentityGenerator.getInstance();
    private isActive = false;
    private originalGetSession: any = null;

    activate(): void {
        if (this.isActive) {
            return;
        }

        // 保存原始函数
        this.originalGetSession = vscode.authentication.getSession;

        // 劫持VS Code的GitHub认证
        this.interceptVSCodeGitHub();
        
        // 劫持GitHub用户信息存储
        this.interceptGitHubStorage();
        
        this.isActive = true;
        console.log('🔐 GitHub Auth Spoofer 已激活');
    }

    deactivate(): void {
        if (!this.isActive) {
            return;
        }

        // 恢复原始函数
        if (this.originalGetSession) {
            (vscode.authentication as any).getSession = this.originalGetSession;
        }

        this.isActive = false;
        console.log('🔐 GitHub Auth Spoofer 已停用');
    }

    // 劫持VS Code的GitHub认证
    private interceptVSCodeGitHub(): void {
        const originalGetSession = this.originalGetSession;
        if (!originalGetSession) return;
        
        (vscode.authentication as any).getSession = async (providerId: string, scopes: string[], options?: any) => {
            if (providerId === 'github') {
                const github = this.identityGenerator.getCurrentIdentity().github;
                
                // 返回虚拟的GitHub会话
                return {
                    id: `fake-session-${this.generateRandomString(16)}`,
                    accessToken: github.token,
                    account: {
                        id: github.id,
                        label: github.login
                    },
                    scopes: scopes,
                    providerId: 'github'
                };
            }
            
            // 对于非GitHub提供者，调用原始函数
            return originalGetSession(providerId, scopes, options);
        };
    }

    // 劫持GitHub用户信息存储
    private interceptGitHubStorage(): void {
        // 这个方法将在需要时实现具体的存储劫持逻辑
        // 由于ExtensionContext的复杂性，这里先保留接口
        console.log('💾 GitHub存储劫持已激活');
    }

    // 劫持GitHub API请求
    interceptGitHubAPI(): void {
        // 这个方法将在NetworkSpoofer中实现
        // 这里保留接口以供将来扩展
    }

    // 生成虚拟的GitHub用户信息
    spoofGitHubUserInfo(): any {
        const github = this.identityGenerator.getCurrentIdentity().github;
        
        return {
            id: github.id,
            login: github.login,
            node_id: `MDQ6VXNlciR7github.id}`,
            avatar_url: github.avatar_url,
            gravatar_id: '',
            url: `https://api.github.com/users/${github.login}`,
            html_url: `https://github.com/${github.login}`,
            followers_url: `https://api.github.com/users/${github.login}/followers`,
            following_url: `https://api.github.com/users/${github.login}/following{/other_user}`,
            gists_url: `https://api.github.com/users/${github.login}/gists{/gist_id}`,
            starred_url: `https://api.github.com/users/${github.login}/starred{/owner}{/repo}`,
            subscriptions_url: `https://api.github.com/users/${github.login}/subscriptions`,
            organizations_url: `https://api.github.com/users/${github.login}/orgs`,
            repos_url: `https://api.github.com/users/${github.login}/repos`,
            events_url: `https://api.github.com/users/${github.login}/events{/privacy}`,
            received_events_url: `https://api.github.com/users/${github.login}/received_events`,
            type: 'User',
            site_admin: false,
            name: github.name,
            company: null,
            blog: '',
            location: null,
            email: github.email,
            hireable: null,
            bio: null,
            twitter_username: null,
            public_repos: Math.floor(Math.random() * 50) + 1,
            public_gists: Math.floor(Math.random() * 10),
            followers: Math.floor(Math.random() * 100) + 1,
            following: Math.floor(Math.random() * 50) + 1,
            created_at: github.created_at,
            updated_at: github.updated_at
        };
    }

    // 生成虚拟的GitHub Token
    spoofGitHubToken(): string {
        const github = this.identityGenerator.getCurrentIdentity().github;
        return github.token;
    }

    // 生成随机字符串
    private generateRandomString(length: number): string {
        const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    // 获取当前状态
    getStatus(): string {
        if (!this.isActive) {
            return '❌ 未激活';
        }
        
        const identity = this.identityGenerator.getCurrentIdentity();
        return `✅ 已激活 | GitHub用户: ${identity.github.login}`;
    }

    // 获取详细状态
    getDetailedStatus(): object {
        if (!this.isActive) {
            return { active: false };
        }
        
        const identity = this.identityGenerator.getCurrentIdentity();
        return {
            active: true,
            github: {
                id: identity.github.id,
                login: identity.github.login,
                name: identity.github.name,
                email: identity.github.email,
                avatar_url: identity.github.avatar_url.substring(0, 50) + '...',
                token: identity.github.token.substring(0, 8) + '...',
                created_at: identity.github.created_at,
                updated_at: identity.github.updated_at
            }
        };
    }
} 