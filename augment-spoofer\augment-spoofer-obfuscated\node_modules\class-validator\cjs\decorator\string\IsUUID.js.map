{"version": 3, "file": "IsUUID.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsUUID.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,kEAAmD;AAGtC,QAAA,OAAO,GAAG,QAAQ,CAAC;AAEhC;;;GAGG;AACH,SAAgB,MAAM,CAAC,KAAc,EAAE,OAAiC;IACtE,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,gBAAe,EAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACtE,CAAC;AAFD,wBAEC;AAED;;;GAGG;AACH,SAAgB,MAAM,CAAC,OAAiC,EAAE,iBAAqC;IAC7F,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,eAAO;QACb,WAAW,EAAE,CAAC,OAAO,CAAC;QACtB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YACvE,cAAc,EAAE,IAAA,yBAAY,EAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,0BAA0B,EAAE,iBAAiB,CAAC;SACvG;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAZD,wBAYC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isUuidValidator from 'validator/lib/isUUID';\nimport * as ValidatorJS from 'validator';\n\nexport const IS_UUID = 'isUuid';\n\n/**\n * Checks if the string is a UUID (version 3, 4 or 5).\n * If given value is not a string, then it returns false.\n */\nexport function isUUID(value: unknown, version?: ValidatorJS.UUIDVersion): boolean {\n  return typeof value === 'string' && isUuidValidator(value, version);\n}\n\n/**\n * Checks if the string is a UUID (version 3, 4 or 5).\n * If given value is not a string, then it returns false.\n */\nexport function IsUUID(version?: ValidatorJS.UUIDVersion, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_UUID,\n      constraints: [version],\n      validator: {\n        validate: (value, args): boolean => isUUID(value, args?.constraints[0]),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be a UUID', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}