// 🚀 AugmentCode Chrome虚拟化 - 快速启动脚本
// 使用方法：在Chrome控制台中运行此脚本，一键执行所有虚拟化操作

console.log('🚀 AugmentCode Chrome虚拟化快速启动...');
console.log('=====================================');

// 检查是否在正确的页面
function checkPage() {
    const currentUrl = window.location.href;
    const augmentUrls = [
        'app.augmentcode.com',
        'login.augmentcode.com', 
        'www.augmentcode.com'
    ];
    
    const isAugmentPage = augmentUrls.some(url => currentUrl.includes(url));
    
    if (!isAugmentPage) {
        console.log('⚠️  当前页面不是AugmentCode网站');
        console.log('💡 建议访问: https://app.augmentcode.com/');
        console.log('💡 或者: https://login.augmentcode.com/');
        return false;
    }
    
    console.log('✅ 当前页面: ' + currentUrl);
    return true;
}

// 快速数据清理函数
async function quickDataClean() {
    console.log('🧹 快速数据清理...');
    
    // 清理localStorage
    const localStorageKeys = Object.keys(localStorage);
    let clearedLocal = 0;
    for (const key of localStorageKeys) {
        if (key.includes('augment') || localStorage.getItem(key)?.includes('augment')) {
            localStorage.removeItem(key);
            clearedLocal++;
        }
    }
    
    // 清理sessionStorage
    const sessionStorageKeys = Object.keys(sessionStorage);
    let clearedSession = 0;
    for (const key of sessionStorageKeys) {
        if (key.includes('augment') || sessionStorage.getItem(key)?.includes('augment')) {
            sessionStorage.removeItem(key);
            clearedSession++;
        }
    }
    
    // 清理cookies
    const cookies = document.cookie.split(';');
    let clearedCookies = 0;
    for (const cookie of cookies) {
        const [name] = cookie.split('=');
        const trimmedName = name.trim();
        if (trimmedName.includes('augment') || trimmedName.includes('session') || 
            trimmedName.includes('token') || trimmedName.includes('auth')) {
            document.cookie = `${trimmedName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
            clearedCookies++;
        }
    }
    
    console.log(`✅ 清理完成: ${clearedLocal}个localStorage, ${clearedSession}个sessionStorage, ${clearedCookies}个cookies`);
}

// 快速指纹虚拟化函数
function quickFingerprintSpoof() {
    console.log('🎭 快速指纹虚拟化...');
    
    // 生成随机指纹
    const fingerprints = [
        {
            userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            platform: "Win32",
            language: "zh-CN,zh;q=0.9,en;q=0.8",
            screenWidth: 1920,
            screenHeight: 1080
        },
        {
            userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            platform: "MacIntel",
            language: "en-US,en;q=0.9",
            screenWidth: 1440,
            screenHeight: 900
        },
        {
            userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            platform: "Win32",
            language: "ja-JP,ja;q=0.9,en;q=0.8",
            screenWidth: 1366,
            screenHeight: 768
        }
    ];
    
    const randomFingerprint = fingerprints[Math.floor(Math.random() * fingerprints.length)];
    
    // 虚拟化navigator属性
    Object.defineProperty(navigator, 'userAgent', {
        get: () => randomFingerprint.userAgent,
        configurable: true
    });
    
    Object.defineProperty(navigator, 'platform', {
        get: () => randomFingerprint.platform,
        configurable: true
    });
    
    Object.defineProperty(navigator, 'language', {
        get: () => randomFingerprint.language.split(',')[0],
        configurable: true
    });
    
    Object.defineProperty(navigator, 'languages', {
        get: () => randomFingerprint.language.split(','),
        configurable: true
    });
    
    // 虚拟化screen属性
    Object.defineProperty(screen, 'width', {
        get: () => randomFingerprint.screenWidth,
        configurable: true
    });
    
    Object.defineProperty(screen, 'height', {
        get: () => randomFingerprint.screenHeight,
        configurable: true
    });
    
    Object.defineProperty(screen, 'availWidth', {
        get: () => randomFingerprint.screenWidth,
        configurable: true
    });
    
    Object.defineProperty(screen, 'availHeight', {
        get: () => randomFingerprint.screenHeight,
        configurable: true
    });
    
    console.log(`✅ 指纹虚拟化完成: ${randomFingerprint.userAgent}`);
}

// 快速网络监控函数
function quickNetworkMonitor() {
    console.log('📡 快速网络监控...');
    
    // 拦截fetch请求
    const originalFetch = window.fetch;
    window.fetch = async (input, init) => {
        const url = typeof input === 'string' ? input : input.url;
        
        if (url.includes('augmentcode')) {
            console.log(`🌐 AugmentCode请求: ${url}`);
            if (init && init.headers) {
                console.log('📋 请求头:', init.headers);
            }
        }
        
        return originalFetch(input, init);
    };
    
    // 拦截XMLHttpRequest
    const originalOpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(method, url, ...args) {
        if (url.includes('augmentcode')) {
            console.log(`🌐 AugmentCode XHR: ${method} ${url}`);
        }
        return originalOpen.call(this, method, url, ...args);
    };
    
    console.log('✅ 网络监控已激活');
}

// 快速IP检测函数
async function quickIPCheck() {
    console.log('🌍 快速IP检测...');
    
    try {
        const response = await fetch('https://httpbin.org/ip');
        const data = await response.json();
        console.log(`✅ 当前IP: ${data.origin}`);
        return data.origin;
    } catch (error) {
        console.log('❌ IP检测失败:', error.message);
        return null;
    }
}

// 主函数：一键执行所有操作
async function quickStart() {
    console.log('🚀 开始一键虚拟化...');
    console.log('=====================================');
    
    // 1. 检查页面
    if (!checkPage()) {
        console.log('⚠️  请在AugmentCode页面运行此脚本');
        return;
    }
    
    // 2. 数据清理
    await quickDataClean();
    
    // 3. 指纹虚拟化
    quickFingerprintSpoof();
    
    // 4. 网络监控
    quickNetworkMonitor();
    
    // 5. IP检测
    await quickIPCheck();
    
    console.log('=====================================');
    console.log('🎉 一键虚拟化完成！');
    console.log('💡 现在可以安全地进行注册操作');
    console.log('📊 所有AugmentCode的网络请求都会被监控');
    console.log('=====================================');
    
    // 显示当前状态
    console.log('📋 当前虚拟化状态:');
    console.log(`   User-Agent: ${navigator.userAgent}`);
    console.log(`   平台: ${navigator.platform}`);
    console.log(`   语言: ${navigator.language}`);
    console.log(`   屏幕: ${screen.width}x${screen.height}`);
    console.log(`   localStorage项目: ${Object.keys(localStorage).length}`);
    console.log(`   sessionStorage项目: ${Object.keys(sessionStorage).length}`);
    console.log(`   Cookies: ${document.cookie ? document.cookie.split(';').length : 0}个`);
}

// 验证函数
function verifyVirtualization() {
    console.log('🔍 验证虚拟化效果...');
    
    const checks = {
        userAgent: navigator.userAgent !== 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        localStorage: Object.keys(localStorage).filter(key => key.includes('augment')).length === 0,
        sessionStorage: Object.keys(sessionStorage).filter(key => key.includes('augment')).length === 0,
        cookies: !document.cookie.includes('augment')
    };
    
    console.log('📊 验证结果:');
    console.log(`   User-Agent虚拟化: ${checks.userAgent ? '✅' : '❌'}`);
    console.log(`   localStorage清理: ${checks.localStorage ? '✅' : '❌'}`);
    console.log(`   sessionStorage清理: ${checks.sessionStorage ? '✅' : '❌'}`);
    console.log(`   Cookies清理: ${checks.cookies ? '✅' : '❌'}`);
    
    const allPassed = Object.values(checks).every(check => check);
    console.log(`\n🎯 总体结果: ${allPassed ? '✅ 虚拟化成功' : '❌ 虚拟化失败'}`);
    
    return allPassed;
}

// 重置函数
function resetVirtualization() {
    console.log('🔄 重置虚拟化...');
    
    // 重新加载页面以重置所有虚拟化
    window.location.reload();
}

// 导出到全局对象
window.quickStart = quickStart;
window.verifyVirtualization = verifyVirtualization;
window.resetVirtualization = resetVirtualization;

// 显示使用说明
console.log('=====================================');
console.log('🚀 快速启动脚本使用说明');
console.log('=====================================');
console.log('📋 可用命令:');
console.log('  quickStart() - 一键执行所有虚拟化操作');
console.log('  verifyVirtualization() - 验证虚拟化效果');
console.log('  resetVirtualization() - 重置虚拟化（刷新页面）');
console.log('=====================================');

// 自动执行快速启动
console.log('💡 正在自动执行快速启动...');
quickStart(); 