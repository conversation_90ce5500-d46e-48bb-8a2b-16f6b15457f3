// 🦁 AugmentCode Brave浏览器虚拟化 - 快速启动脚本
// 使用方法：在Brave控制台中运行此脚本，一键执行所有虚拟化操作

console.log('🦁 AugmentCode Brave浏览器虚拟化快速启动...');
console.log('=====================================');

// 检查是否在正确的页面
function checkPage() {
    const currentUrl = window.location.href;
    const augmentUrls = [
        'app.augmentcode.com',
        'login.augmentcode.com', 
        'www.augmentcode.com'
    ];
    
    const isAugmentPage = augmentUrls.some(url => currentUrl.includes(url));
    
    if (!isAugmentPage) {
        console.log('⚠️  当前页面不是AugmentCode网站');
        console.log('💡 建议访问: https://app.augmentcode.com/');
        console.log('💡 或者: https://login.augmentcode.com/');
        return false;
    }
    
    console.log('✅ 当前页面: ' + currentUrl);
    return true;
}

// 检测Brave特有功能
function detectBraveFeatures() {
    console.log('🦁 检测Brave特有功能...');
    
    const braveFeatures = {
        shields: false,
        tor: false,
        cryptoWallet: false,
        ipfs: false
    };
    
    // 检测Brave Shields
    if (typeof window.navigator.brave !== 'undefined') {
        braveFeatures.shields = true;
        console.log('✅ 检测到Brave Shields');
    }
    
    // 检测Tor模式
    if (window.location.hostname.endsWith('.onion') || 
        document.cookie.includes('tor') || 
        navigator.userAgent.includes('Tor')) {
        braveFeatures.tor = true;
        console.log('✅ 检测到Tor模式');
    }
    
    // 检测Brave钱包
    if (typeof window.ethereum !== 'undefined' && 
        window.ethereum.isBraveWallet) {
        braveFeatures.cryptoWallet = true;
        console.log('✅ 检测到Brave钱包');
    }
    
    // 检测IPFS
    if (window.location.protocol === 'ipfs:' || 
        window.location.hostname.includes('ipfs')) {
        braveFeatures.ipfs = true;
        console.log('✅ 检测到IPFS');
    }
    
    return braveFeatures;
}

// 快速数据清理函数（Brave增强版）
async function quickDataClean() {
    console.log('🧹 快速数据清理（Brave增强版）...');
    
    // 清理localStorage
    const localStorageKeys = Object.keys(localStorage);
    let clearedLocal = 0;
    for (const key of localStorageKeys) {
        if (key.includes('augment') || 
            key.includes('brave') || 
            key.includes('shields') ||
            localStorage.getItem(key)?.includes('augment')) {
            localStorage.removeItem(key);
            clearedLocal++;
        }
    }
    
    // 清理sessionStorage
    const sessionStorageKeys = Object.keys(sessionStorage);
    let clearedSession = 0;
    for (const key of sessionStorageKeys) {
        if (key.includes('augment') || 
            key.includes('brave') || 
            key.includes('shields') ||
            sessionStorage.getItem(key)?.includes('augment')) {
            sessionStorage.removeItem(key);
            clearedSession++;
        }
    }
    
    // 清理cookies（包括Brave特有cookies）
    const cookies = document.cookie.split(';');
    let clearedCookies = 0;
    for (const cookie of cookies) {
        const [name] = cookie.split('=');
        const trimmedName = name.trim();
        if (trimmedName.includes('augment') || 
            trimmedName.includes('brave') ||
            trimmedName.includes('shields') ||
            trimmedName.includes('session') || 
            trimmedName.includes('token') || 
            trimmedName.includes('auth')) {
            document.cookie = `${trimmedName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
            clearedCookies++;
        }
    }
    
    console.log(`✅ 清理完成: ${clearedLocal}个localStorage, ${clearedSession}个sessionStorage, ${clearedCookies}个cookies`);
}

// 快速指纹虚拟化函数（Brave优化版）
function quickFingerprintSpoof() {
    console.log('🎭 快速指纹虚拟化（Brave优化版）...');
    
    // 生成随机指纹（包含Brave特有标识）
    const fingerprints = [
        {
            userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            platform: "Win32",
            language: "zh-CN,zh;q=0.9,en;q=0.8",
            screenWidth: 1920,
            screenHeight: 1080,
            braveSpecific: false
        },
        {
            userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
            platform: "Win32",
            language: "en-US,en;q=0.9",
            screenWidth: 1366,
            screenHeight: 768,
            braveSpecific: false
        },
        {
            userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            platform: "MacIntel",
            language: "en-US,en;q=0.9",
            screenWidth: 1440,
            screenHeight: 900,
            braveSpecific: false
        }
    ];
    
    const randomFingerprint = fingerprints[Math.floor(Math.random() * fingerprints.length)];
    
    // 虚拟化navigator属性
    Object.defineProperty(navigator, 'userAgent', {
        get: () => randomFingerprint.userAgent,
        configurable: true
    });
    
    Object.defineProperty(navigator, 'platform', {
        get: () => randomFingerprint.platform,
        configurable: true
    });
    
    Object.defineProperty(navigator, 'language', {
        get: () => randomFingerprint.language.split(',')[0],
        configurable: true
    });
    
    Object.defineProperty(navigator, 'languages', {
        get: () => randomFingerprint.language.split(','),
        configurable: true
    });
    
    // 虚拟化screen属性
    Object.defineProperty(screen, 'width', {
        get: () => randomFingerprint.screenWidth,
        configurable: true
    });
    
    Object.defineProperty(screen, 'height', {
        get: () => randomFingerprint.screenHeight,
        configurable: true
    });
    
    Object.defineProperty(screen, 'availWidth', {
        get: () => randomFingerprint.screenWidth,
        configurable: true
    });
    
    Object.defineProperty(screen, 'availHeight', {
        get: () => randomFingerprint.screenHeight,
        configurable: true
    });
    
    // 虚拟化Brave特有属性
    if (typeof window.navigator.brave !== 'undefined') {
        Object.defineProperty(navigator, 'brave', {
            get: () => ({
                isBrave: () => false, // 伪装成非Brave浏览器
                onAvailable: () => {},
                onUpdate: () => {}
            }),
            configurable: true
        });
    }
    
    console.log(`✅ 指纹虚拟化完成: ${randomFingerprint.userAgent}`);
}

// 快速网络监控函数（Brave增强版）
function quickNetworkMonitor() {
    console.log('📡 快速网络监控（Brave增强版）...');
    
    // 拦截fetch请求
    const originalFetch = window.fetch;
    window.fetch = async (input, init) => {
        const url = typeof input === 'string' ? input : input.url;
        
        if (url.includes('augmentcode')) {
            console.log(`🌐 AugmentCode请求: ${url}`);
            if (init && init.headers) {
                console.log('📋 请求头:', init.headers);
            }
            
            // 检查是否通过Tor
            if (url.includes('.onion')) {
                console.log('🕸️  检测到Tor请求');
            }
        }
        
        return originalFetch(input, init);
    };
    
    // 拦截XMLHttpRequest
    const originalOpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(method, url, ...args) {
        if (url.includes('augmentcode')) {
            console.log(`🌐 AugmentCode XHR: ${method} ${url}`);
        }
        return originalOpen.call(this, method, url, ...args);
    };
    
    // 监控Brave Shields活动
    if (typeof window.navigator.brave !== 'undefined') {
        console.log('🛡️  Brave Shields监控已激活');
    }
    
    console.log('✅ 网络监控已激活');
}

// 快速IP检测函数（支持Tor）
async function quickIPCheck() {
    console.log('🌍 快速IP检测（支持Tor）...');
    
    try {
        const response = await fetch('https://httpbin.org/ip');
        const data = await response.json();
        
        // 检查是否通过Tor
        const isTor = window.location.hostname.endsWith('.onion') || 
                     data.origin.includes('127.0.0.1') ||
                     navigator.userAgent.includes('Tor');
        
        if (isTor) {
            console.log(`🕸️  当前IP (Tor): ${data.origin}`);
        } else {
            console.log(`✅ 当前IP: ${data.origin}`);
        }
        
        return { ip: data.origin, isTor: isTor };
    } catch (error) {
        console.log('❌ IP检测失败:', error.message);
        return null;
    }
}

// 主函数：一键执行所有操作
async function braveQuickStart() {
    console.log('🚀 开始Brave一键虚拟化...');
    console.log('=====================================');
    
    // 1. 检查页面
    if (!checkPage()) {
        console.log('⚠️  请在AugmentCode页面运行此脚本');
        return;
    }
    
    // 2. 检测Brave特有功能
    const braveFeatures = detectBraveFeatures();
    
    // 3. 数据清理
    await quickDataClean();
    
    // 4. 指纹虚拟化
    quickFingerprintSpoof();
    
    // 5. 网络监控
    quickNetworkMonitor();
    
    // 6. IP检测
    const ipInfo = await quickIPCheck();
    
    console.log('=====================================');
    console.log('🎉 Brave一键虚拟化完成！');
    console.log('💡 现在可以安全地进行注册操作');
    console.log('📊 所有AugmentCode的网络请求都会被监控');
    console.log('🦁 Brave特有功能已优化');
    console.log('=====================================');
    
    // 显示当前状态
    console.log('📋 当前虚拟化状态:');
    console.log(`   User-Agent: ${navigator.userAgent}`);
    console.log(`   平台: ${navigator.platform}`);
    console.log(`   语言: ${navigator.language}`);
    console.log(`   屏幕: ${screen.width}x${screen.height}`);
    console.log(`   localStorage项目: ${Object.keys(localStorage).length}`);
    console.log(`   sessionStorage项目: ${Object.keys(sessionStorage).length}`);
    console.log(`   Cookies: ${document.cookie ? document.cookie.split(';').length : 0}个`);
    
    if (ipInfo) {
        console.log(`   IP地址: ${ipInfo.ip} ${ipInfo.isTor ? '(Tor)' : ''}`);
    }
    
    // 显示Brave特有功能状态
    console.log('🦁 Brave特有功能状态:');
    console.log(`   Shields: ${braveFeatures.shields ? '✅ 已检测' : '❌ 未检测'}`);
    console.log(`   Tor模式: ${braveFeatures.tor ? '✅ 已启用' : '❌ 未启用'}`);
    console.log(`   钱包: ${braveFeatures.cryptoWallet ? '✅ 已检测' : '❌ 未检测'}`);
    console.log(`   IPFS: ${braveFeatures.ipfs ? '✅ 已检测' : '❌ 未检测'}`);
}

// 验证函数（Brave增强版）
function verifyBraveVirtualization() {
    console.log('🔍 验证Brave虚拟化效果...');
    
    const checks = {
        userAgent: navigator.userAgent !== 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        localStorage: Object.keys(localStorage).filter(key => key.includes('augment') || key.includes('brave')).length === 0,
        sessionStorage: Object.keys(sessionStorage).filter(key => key.includes('augment') || key.includes('brave')).length === 0,
        cookies: !document.cookie.includes('augment') && !document.cookie.includes('brave'),
        braveDetection: typeof navigator.brave === 'undefined' || !navigator.brave.isBrave()
    };
    
    console.log('📊 验证结果:');
    console.log(`   User-Agent虚拟化: ${checks.userAgent ? '✅' : '❌'}`);
    console.log(`   localStorage清理: ${checks.localStorage ? '✅' : '❌'}`);
    console.log(`   sessionStorage清理: ${checks.sessionStorage ? '✅' : '❌'}`);
    console.log(`   Cookies清理: ${checks.cookies ? '✅' : '❌'}`);
    console.log(`   Brave检测隐藏: ${checks.braveDetection ? '✅' : '❌'}`);
    
    const allPassed = Object.values(checks).every(check => check);
    console.log(`\n🎯 总体结果: ${allPassed ? '✅ 虚拟化成功' : '❌ 虚拟化失败'}`);
    
    return allPassed;
}

// 重置函数
function resetBraveVirtualization() {
    console.log('🔄 重置Brave虚拟化...');
    
    // 重新加载页面以重置所有虚拟化
    window.location.reload();
}

// 导出到全局对象
window.braveQuickStart = braveQuickStart;
window.verifyBraveVirtualization = verifyBraveVirtualization;
window.resetBraveVirtualization = resetBraveVirtualization;

// 显示使用说明
console.log('=====================================');
console.log('🦁 Brave快速启动脚本使用说明');
console.log('=====================================');
console.log('📋 可用命令:');
console.log('  braveQuickStart() - 一键执行所有Brave虚拟化操作');
console.log('  verifyBraveVirtualization() - 验证Brave虚拟化效果');
console.log('  resetBraveVirtualization() - 重置虚拟化（刷新页面）');
console.log('=====================================');
console.log('🦁 Brave特有功能:');
console.log('  - 自动检测Brave Shields');
console.log('  - 支持Tor模式检测');
console.log('  - 隐藏Brave浏览器标识');
console.log('  - 清理Brave特有数据');
console.log('=====================================');

// 自动执行快速启动
console.log('💡 正在自动执行Brave快速启动...');
braveQuickStart(); 