{"version": 3, "file": "IsISRC.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsISRC.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,kEAAmD;AAEtC,QAAA,OAAO,GAAG,QAAQ,CAAC;AAEhC;;;GAGG;AACH,SAAgB,MAAM,CAAC,KAAc;IACnC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,gBAAe,EAAC,KAAK,CAAC,CAAC;AAC7D,CAAC;AAFD,wBAEC;AAED;;;GAGG;AACH,SAAgB,MAAM,CAAC,iBAAqC;IAC1D,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,eAAO;QACb,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC;YACjD,cAAc,EAAE,IAAA,yBAAY,EAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,2BAA2B,EAAE,iBAAiB,CAAC;SACxG;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAXD,wBAWC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isISRCValidator from 'validator/lib/isISRC';\n\nexport const IS_ISRC = 'isISRC';\n\n/**\n * Check if the string is a ISRC.\n * If given value is not a string, then it returns false.\n */\nexport function isISRC(value: unknown): boolean {\n  return typeof value === 'string' && isISRCValidator(value);\n}\n\n/**\n * Check if the string is a ISRC.\n * If given value is not a string, then it returns false.\n */\nexport function IsISRC(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_ISRC,\n      validator: {\n        validate: (value, args): boolean => isISRC(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be an ISRC', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}