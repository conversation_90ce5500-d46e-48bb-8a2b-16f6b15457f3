{"version": 3, "file": "IsIn.js", "sourceRoot": "", "sources": ["../../../../src/decorator/common/IsIn.ts"], "names": [], "mappings": ";;;AACA,qDAAgE;AAEnD,QAAA,KAAK,GAAG,MAAM,CAAC;AAE5B;;GAEG;AACH,SAAgB,IAAI,CAAC,KAAc,EAAE,cAAkC;IACrE,OAAO,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,aAAa,KAAK,KAAK,CAAC,CAAC;AACxG,CAAC;AAFD,oBAEC;AAED;;GAEG;AACH,SAAgB,IAAI,CAAC,MAAsB,EAAE,iBAAqC;IAChF,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,aAAK;QACX,WAAW,EAAE,CAAC,MAAM,CAAC;QACrB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YACrE,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,6DAA6D,EACxF,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAfD,oBAeC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const IS_IN = 'isIn';\n\n/**\n * Checks if given value is in a array of allowed values.\n */\nexport function isIn(value: unknown, possibleValues: readonly unknown[]): boolean {\n  return Array.isArray(possibleValues) && possibleValues.some(possibleValue => possibleValue === value);\n}\n\n/**\n * Checks if given value is in a array of allowed values.\n */\nexport function IsIn(values: readonly any[], validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_IN,\n      constraints: [values],\n      validator: {\n        validate: (value, args): boolean => isIn(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be one of the following values: $constraint1',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}