{"version": 3, "file": "convert-to-array.util.js", "sourceRoot": "", "sources": ["../../../src/utils/convert-to-array.util.ts"], "names": [], "mappings": ";;;AAAA;;GAEG;AACH,SAAgB,cAAc,CAAI,GAAoC;IACpE,IAAI,GAAG,YAAY,GAAG,EAAE,CAAC;QACvB,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;IAClC,CAAC;IACD,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACpD,CAAC;AALD,wCAKC", "sourcesContent": ["/**\n * Convert Map, Set to Array\n */\nexport function convertToArray<T>(val: Array<T> | Set<T> | Map<any, T>): Array<T> {\n  if (val instanceof Map) {\n    return Array.from(val.values());\n  }\n  return Array.isArray(val) ? val : Array.from(val);\n}\n"]}