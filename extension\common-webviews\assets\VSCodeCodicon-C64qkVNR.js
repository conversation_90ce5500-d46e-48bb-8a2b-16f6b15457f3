import{S as e,i as l,s as d,n as t,d as u,h as r,c as p,F as f}from"./SpinnerAugment-VfHtkDdv.js";function m(a){let c,o;return{c(){c=f("span"),r(c,"class",o="codicon codicon-"+a[0]+" "+a[1])},m(s,n){p(s,c,n)},p(s,[n]){3&n&&o!==(o="codicon codicon-"+s[0]+" "+s[1])&&r(c,"class",o)},i:t,o:t,d(s){s&&u(c)}}}function h(a,c,o){let{icon:s}=c,{class:n=""}=c;return a.$$set=i=>{"icon"in i&&o(0,s=i.icon),"class"in i&&o(1,n=i.class)},[s,n]}class $ extends e{constructor(c){super(),l(this,c,h,m,d,{icon:0,class:1})}}export{$ as V};
