{"version": 3, "file": "Matches.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/Matches.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,oEAAqD;AAExC,QAAA,OAAO,GAAG,SAAS,CAAC;AAQjC,SAAgB,OAAO,CAAC,KAAa,EAAE,OAAwB,EAAE,SAAkB;IACjF,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,iBAAgB,EAAC,KAAK,EAAE,OAAyB,EAAE,SAAS,CAAC,CAAC;AACpG,CAAC;AAFD,0BAEC;AAQD,SAAgB,OAAO,CACrB,OAAwB,EACxB,4BAAyD,EACzD,iBAAqC;IAErC,IAAI,SAAiB,CAAC;IACtB,IAAI,4BAA4B,IAAI,4BAA4B,YAAY,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzG,iBAAiB,GAAG,4BAA4B,CAAC;IACnD,CAAC;SAAM,CAAC;QACN,SAAS,GAAG,4BAAsC,CAAC;IACrD,CAAC;IAED,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,eAAO;QACb,WAAW,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC;QACjC,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YAC9F,cAAc,EAAE,IAAA,yBAAY,EAC1B,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE,CAAC,UAAU,GAAG,sDAAsD,EACzF,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AA1BD,0BA0BC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport matchesValidator from 'validator/lib/matches';\n\nexport const MATCHES = 'matches';\n\n/**\n * Checks if string matches the pattern. Either matches('foo', /foo/i).\n * If given value is not a string, then it returns false.\n */\nexport function matches(value: string, pattern: RegExp): boolean;\nexport function matches(value: string, pattern: string, modifiers: string): boolean;\nexport function matches(value: string, pattern: RegExp | string, modifiers?: string): boolean {\n  return typeof value === 'string' && matchesValidator(value, pattern as unknown as any, modifiers);\n}\n\n/**\n * Checks if string matches the pattern. Either matches('foo', /foo/i)\n * If given value is not a string, then it returns false.\n */\nexport function Matches(pattern: RegExp, validationOptions?: ValidationOptions): PropertyDecorator;\nexport function Matches(pattern: string, modifiers?: string, validationOptions?: ValidationOptions): PropertyDecorator;\nexport function Matches(\n  pattern: RegExp | string,\n  modifiersOrAnnotationOptions?: string | ValidationOptions,\n  validationOptions?: ValidationOptions\n): PropertyDecorator {\n  let modifiers: string;\n  if (modifiersOrAnnotationOptions && modifiersOrAnnotationOptions instanceof Object && !validationOptions) {\n    validationOptions = modifiersOrAnnotationOptions;\n  } else {\n    modifiers = modifiersOrAnnotationOptions as string;\n  }\n\n  return ValidateBy(\n    {\n      name: MATCHES,\n      constraints: [pattern, modifiers],\n      validator: {\n        validate: (value, args): boolean => matches(value, args?.constraints[0], args?.constraints[1]),\n        defaultMessage: buildMessage(\n          (eachPrefix, args) => eachPrefix + '$property must match $constraint1 regular expression',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}