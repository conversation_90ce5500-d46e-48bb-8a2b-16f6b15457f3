# 🎭 AugmentCode Chrome虚拟化工具包

专门针对AugmentCode注册检测的浏览器虚拟化解决方案，包含数据清理、指纹虚拟化、代理切换等功能。

## 📁 工具包内容

### 1. `clear-augment-data.js` - 浏览器数据清理脚本
- **功能**: 清理Chrome中所有AugmentCode相关的缓存、cookies、localStorage等
- **使用方法**: 在Chrome控制台中运行
- **清理内容**:
  - Cache Storage
  - Session Storage  
  - Local Storage
  - Cookies
  - IndexedDB
  - Service Workers
  - WebSQL
  - 浏览器指纹数据

### 2. `fingerprint-spoofer.js` - 浏览器指纹虚拟化脚本
- **功能**: 虚拟化浏览器指纹，让每次访问都像新用户
- **使用方法**: 在Chrome控制台中运行
- **虚拟化内容**:
  - User-Agent
  - 屏幕分辨率
  - 时区信息
  - WebGL信息
  - Canvas指纹
  - 字体信息
  - 音频指纹
  - 网络信息

### 3. `clear-chrome-data.bat` - Windows批处理清理脚本
- **功能**: 一键清理Chrome用户数据目录
- **使用方法**: 以管理员权限运行
- **清理内容**:
  - 所有Profile的缓存
  - Local/Session Storage
  - IndexedDB
  - Cookies
  - 登录数据
  - Web数据
  - 偏好设置中的AugmentCode数据
  - Service Workers

### 4. `proxy-switcher.js` - 代理切换工具
- **功能**: 自动切换代理服务器，更换IP地址
- **使用方法**: 在Chrome控制台中运行
- **功能特性**:
  - 代理服务器管理
  - IP地址检测
  - 网络请求拦截
  - 自动化流程

## 🚀 完整使用流程

### 方案一：手动操作（推荐）

1. **准备阶段**
   ```bash
   # 1. 关闭所有Chrome窗口
   # 2. 以管理员权限运行批处理脚本
   clear-chrome-data.bat
   ```

2. **启动Chrome无痕模式**
   - 按 `Ctrl+Shift+N` 启动无痕模式
   - 访问 `https://app.augmentcode.com/`

3. **运行数据清理脚本**
   ```javascript
   // 在Chrome控制台中运行
   // 复制 clear-augment-data.js 的内容并粘贴执行
   ```

4. **运行指纹虚拟化脚本**
   ```javascript
   // 在Chrome控制台中运行
   // 复制 fingerprint-spoofer.js 的内容并粘贴执行
   ```

5. **运行代理切换工具**
   ```javascript
   // 在Chrome控制台中运行
   // 复制 proxy-switcher.js 的内容并粘贴执行
   
   // 添加你的代理服务器
   proxyManager.addProxy('your-proxy-host', 8080, 'http');
   
   // 自动切换代理
   automationTools.autoSwitchProxy();
   ```

6. **开始注册**
   - 现在可以安全地注册AugmentCode账户
   - 所有数据都是虚拟化的

### 方案二：自动化操作

```javascript
// 一键执行完整流程
automationTools.automateRegistration();
```

## 🔧 高级配置

### 自定义代理服务器

```javascript
// 添加HTTP代理
proxyManager.addProxy('proxy.example.com', 8080, 'http');

// 添加HTTPS代理
proxyManager.addProxy('proxy.example.com', 443, 'https');

// 添加SOCKS代理
proxyManager.addProxy('proxy.example.com', 1080, 'socks5');
```

### 自定义浏览器指纹

```javascript
// 修改指纹生成器中的参数
const userAgents = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    // 添加更多User-Agent
];

const screenResolutions = [
    "1920x1080",
    "1366x768",
    // 添加更多分辨率
];
```

### 网络监控

```javascript
// 激活网络监控
networkInterceptor.activate();

// 查看拦截统计
console.log(networkInterceptor.getStats());

// 重置统计
networkInterceptor.resetStats();
```

## 📊 监控和验证

### 检查虚拟化效果

```javascript
// 检查当前IP
ipDetector.detectIP();

// 检查IP历史
console.log(ipDetector.getIPHistory());

// 检查当前代理
console.log(proxyManager.getCurrentProxy());
```

### 验证数据清理

```javascript
// 检查localStorage
console.log(Object.keys(localStorage));

// 检查sessionStorage
console.log(Object.keys(sessionStorage));

// 检查cookies
console.log(document.cookie);
```

## ⚠️ 注意事项

### 安全提醒
1. **代理服务器**: 示例中的代理服务器是虚构的，请使用真实的代理服务
2. **数据备份**: 清理脚本会删除所有Chrome数据，请提前备份重要信息
3. **法律合规**: 请确保使用符合当地法律法规的代理服务

### 技术限制
1. **浏览器限制**: 某些指纹虚拟化可能被浏览器安全策略阻止
2. **代理稳定性**: 免费代理可能不稳定，建议使用付费服务
3. **检测技术**: AugmentCode可能使用其他检测技术，本工具不能保证100%有效

### 最佳实践
1. **定期清理**: 每次注册前都运行清理脚本
2. **多工具配合**: 结合VPN、虚拟机等其他工具
3. **环境隔离**: 使用独立的浏览器Profile或虚拟机
4. **时间间隔**: 在不同注册之间留出足够的时间间隔

## 🛠️ 故障排除

### 常见问题

**Q: 清理脚本运行后没有效果？**
A: 确保以管理员权限运行批处理脚本，并完全关闭Chrome

**Q: 指纹虚拟化不生效？**
A: 某些网站可能检测到JavaScript注入，尝试使用浏览器扩展

**Q: 代理切换失败？**
A: 检查代理服务器是否可用，尝试手动测试连接

**Q: 仍然被检测到？**
A: AugmentCode可能使用其他检测技术，考虑使用虚拟机方案

### 调试命令

```javascript
// 检查所有工具状态
console.log('代理管理器:', proxyManager);
console.log('网络拦截器:', networkInterceptor.getStats());
console.log('IP检测器:', ipDetector.getIPHistory());

// 手动测试各项功能
proxyManager.testProxy(proxyManager.getCurrentProxy());
ipDetector.checkIPChange();
```

## 📞 技术支持

如果遇到问题，请检查：
1. Chrome版本是否最新
2. 是否以管理员权限运行
3. 代理服务器是否可用
4. 网络连接是否正常

## 🔄 更新日志

- **v1.0.0**: 初始版本，包含基础清理和虚拟化功能
- 支持Chrome数据清理
- 支持浏览器指纹虚拟化
- 支持代理切换
- 支持网络监控

---

**免责声明**: 本工具仅供学习和研究使用，请遵守相关法律法规和服务条款。 