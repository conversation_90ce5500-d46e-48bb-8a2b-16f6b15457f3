import{k as d,l as v,m as e,o as p,t as m,p as g,q as x,r as q}from"./SpinnerAugment-VfHtkDdv.js";function C(o,c){const u=c.token={};function n(r,t,k,b){if(c.token!==u)return;c.resolved=b;let s=c.ctx;k!==void 0&&(s=s.slice(),s[k]=b);const l=r&&(c.current=r)(s);let f=!1;c.block&&(c.blocks?c.blocks.forEach((h,i)=>{i!==t&&h&&(p(),m(h,1,1,()=>{c.blocks[i]===h&&(c.blocks[i]=null)}),g())}):c.block.d(1),l.c(),x(l,1),l.m(c.mount(),c.anchor),f=!0),c.block=l,c.blocks&&(c.blocks[t]=l),f&&q()}if(d(o)){const r=v();if(o.then(t=>{e(r),n(c.then,1,c.value,t),e(null)},t=>{if(e(r),n(c.catch,2,c.error,t),e(null),!c.hasCatch)throw t}),c.current!==c.pending)return n(c.pending,0),!0}else{if(c.current!==c.then)return n(c.then,1,c.value,o),!0;c.resolved=o}}function E(o,c,u){const n=c.slice(),{resolved:r}=o;o.current===o.then&&(n[o.value]=r),o.current===o.catch&&(n[o.error]=r),o.block.p(n,u)}export{C as h,E as u};
