// 浏览器指纹虚拟化脚本 - 专门针对AugmentCode
// 使用方法：在Chrome控制台中运行此脚本

console.log('🎭 开始虚拟化浏览器指纹...');

// 生成随机指纹数据
function generateRandomFingerprint() {
    const userAgents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36"
    ];
    
    const screenResolutions = [
        "1920x1080",
        "1366x768", 
        "1440x900",
        "1536x864",
        "1280x720"
    ];
    
    const timezones = [
        "Asia/Shanghai",
        "America/New_York",
        "Europe/London",
        "Asia/Tokyo",
        "Australia/Sydney"
    ];
    
    const languages = [
        "zh-CN,zh;q=0.9,en;q=0.8",
        "en-US,en;q=0.9",
        "ja-JP,ja;q=0.9,en;q=0.8",
        "ko-KR,ko;q=0.9,en;q=0.8",
        "fr-FR,fr;q=0.9,en;q=0.8"
    ];
    
    const webglVendors = [
        "Intel Inc.",
        "NVIDIA Corporation",
        "AMD",
        "Apple Inc.",
        "Google Inc. (Intel)"
    ];
    
    const webglRenderers = [
        "Intel Iris OpenGL Engine",
        "NVIDIA GeForce GTX 1060/PCIe/SSE2",
        "AMD Radeon RX 580",
        "Apple M1 Pro",
        "Intel(R) UHD Graphics 620"
    ];
    
    return {
        userAgent: userAgents[Math.floor(Math.random() * userAgents.length)],
        screenResolution: screenResolutions[Math.floor(Math.random() * screenResolutions.length)],
        timezone: timezones[Math.floor(Math.random() * timezones.length)],
        language: languages[Math.floor(Math.random() * languages.length)],
        webglVendor: webglVendors[Math.floor(Math.random() * webglVendors.length)],
        webglRenderer: webglRenderers[Math.floor(Math.random() * webglRenderers.length)],
        platform: "Win32",
        hardwareConcurrency: Math.floor(Math.random() * 8) + 4, // 4-12核
        deviceMemory: Math.floor(Math.random() * 8) + 4 // 4-12GB
    };
}

// 虚拟化User-Agent
function spoofUserAgent() {
    console.log('🌐 虚拟化User-Agent...');
    const fingerprint = generateRandomFingerprint();
    
    // 劫持navigator.userAgent
    Object.defineProperty(navigator, 'userAgent', {
        get: function() {
            return fingerprint.userAgent;
        },
        configurable: true
    });
    
    // 劫持navigator.platform
    Object.defineProperty(navigator, 'platform', {
        get: function() {
            return fingerprint.platform;
        },
        configurable: true
    });
    
    // 劫持navigator.language
    Object.defineProperty(navigator, 'language', {
        get: function() {
            return fingerprint.language.split(',')[0];
        },
        configurable: true
    });
    
    // 劫持navigator.languages
    Object.defineProperty(navigator, 'languages', {
        get: function() {
            return fingerprint.language.split(',');
        },
        configurable: true
    });
    
    // 劫持navigator.hardwareConcurrency
    Object.defineProperty(navigator, 'hardwareConcurrency', {
        get: function() {
            return fingerprint.hardwareConcurrency;
        },
        configurable: true
    });
    
    // 劫持navigator.deviceMemory
    Object.defineProperty(navigator, 'deviceMemory', {
        get: function() {
            return fingerprint.deviceMemory;
        },
        configurable: true
    });
    
    console.log(`✅ User-Agent已虚拟化为: ${fingerprint.userAgent}`);
}

// 虚拟化屏幕信息
function spoofScreenInfo() {
    console.log('🖥️ 虚拟化屏幕信息...');
    const fingerprint = generateRandomFingerprint();
    const [width, height] = fingerprint.screenResolution.split('x').map(Number);
    
    // 劫持screen.width
    Object.defineProperty(screen, 'width', {
        get: function() {
            return width;
        },
        configurable: true
    });
    
    // 劫持screen.height
    Object.defineProperty(screen, 'height', {
        get: function() {
            return height;
        },
        configurable: true
    });
    
    // 劫持screen.availWidth
    Object.defineProperty(screen, 'availWidth', {
        get: function() {
            return width;
        },
        configurable: true
    });
    
    // 劫持screen.availHeight
    Object.defineProperty(screen, 'availHeight', {
        get: function() {
            return height;
        },
        configurable: true
    });
    
    console.log(`✅ 屏幕分辨率已虚拟化为: ${fingerprint.screenResolution}`);
}

// 虚拟化时区信息
function spoofTimezone() {
    console.log('🕐 虚拟化时区信息...');
    const fingerprint = generateRandomFingerprint();
    
    // 劫持Intl.DateTimeFormat
    const originalDateTimeFormat = Intl.DateTimeFormat;
    Intl.DateTimeFormat = function(locales, options) {
        if (options && options.timeZone) {
            options.timeZone = fingerprint.timezone;
        }
        return new originalDateTimeFormat(locales, options);
    };
    
    // 劫持Date.prototype.getTimezoneOffset
    const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
    Date.prototype.getTimezoneOffset = function() {
        // 根据虚拟时区返回偏移量
        const timezoneOffsets = {
            "Asia/Shanghai": -480,
            "America/New_York": 300,
            "Europe/London": 0,
            "Asia/Tokyo": -540,
            "Australia/Sydney": -600
        };
        return timezoneOffsets[fingerprint.timezone] || originalGetTimezoneOffset.call(this);
    };
    
    console.log(`✅ 时区已虚拟化为: ${fingerprint.timezone}`);
}

// 虚拟化WebGL信息
function spoofWebGL() {
    console.log('🎮 虚拟化WebGL信息...');
    const fingerprint = generateRandomFingerprint();
    
    // 劫持WebGL上下文
    const originalGetContext = HTMLCanvasElement.prototype.getContext;
    HTMLCanvasElement.prototype.getContext = function(contextType, contextAttributes) {
        const context = originalGetContext.call(this, contextType, contextAttributes);
        
        if (contextType === 'webgl' || contextType === 'experimental-webgl') {
            // 劫持getParameter方法
            const originalGetParameter = context.getParameter;
            context.getParameter = function(parameter) {
                // 虚拟化WebGL参数
                switch (parameter) {
                    case context.VENDOR:
                        return fingerprint.webglVendor;
                    case context.RENDERER:
                        return fingerprint.webglRenderer;
                    case context.VERSION:
                        return "WebGL 1.0";
                    case context.SHADING_LANGUAGE_VERSION:
                        return "WebGL GLSL ES 1.0";
                    default:
                        return originalGetParameter.call(this, parameter);
                }
            };
        }
        
        return context;
    };
    
    console.log(`✅ WebGL已虚拟化为: ${fingerprint.webglVendor} - ${fingerprint.webglRenderer}`);
}

// 虚拟化Canvas指纹
function spoofCanvasFingerprint() {
    console.log('🎨 虚拟化Canvas指纹...');
    
    // 劫持Canvas的toDataURL方法
    const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
    HTMLCanvasElement.prototype.toDataURL = function(type, quality) {
        // 添加随机噪声到Canvas
        const ctx = this.getContext('2d');
        if (ctx) {
            // 在Canvas上添加微小的随机像素
            const imageData = ctx.getImageData(0, 0, this.width, this.height);
            const data = imageData.data;
            
            // 随机修改一些像素值
            for (let i = 0; i < data.length; i += 4) {
                if (Math.random() < 0.01) { // 1%的概率修改像素
                    data[i] = Math.floor(Math.random() * 256);     // R
                    data[i + 1] = Math.floor(Math.random() * 256); // G
                    data[i + 2] = Math.floor(Math.random() * 256); // B
                    data[i + 3] = Math.floor(Math.random() * 256); // A
                }
            }
            
            ctx.putImageData(imageData, 0, 0);
        }
        
        return originalToDataURL.call(this, type, quality);
    };
    
    console.log('✅ Canvas指纹已虚拟化');
}

// 虚拟化字体信息
function spoofFontList() {
    console.log('🔤 虚拟化字体信息...');
    
    // 劫持document.fonts.check方法
    if (document.fonts && document.fonts.check) {
        const originalCheck = document.fonts.check;
        document.fonts.check = function(font, text) {
            // 随机返回字体可用性
            return Math.random() > 0.5;
        };
    }
    
    console.log('✅ 字体信息已虚拟化');
}

// 虚拟化音频指纹
function spoofAudioFingerprint() {
    console.log('🎵 虚拟化音频指纹...');
    
    // 劫持AudioContext
    if (window.AudioContext || window.webkitAudioContext) {
        const AudioContextClass = window.AudioContext || window.webkitAudioContext;
        const originalAudioContext = AudioContextClass;
        
        window.AudioContext = window.webkitAudioContext = function() {
            const context = new originalAudioContext();
            
            // 劫持createOscillator方法
            const originalCreateOscillator = context.createOscillator;
            context.createOscillator = function() {
                const oscillator = originalCreateOscillator.call(this);
                
                // 添加随机频率偏移
                const originalFrequency = oscillator.frequency;
                Object.defineProperty(oscillator.frequency, 'value', {
                    get: function() {
                        return originalFrequency.value + (Math.random() - 0.5) * 0.1;
                    },
                    set: function(value) {
                        originalFrequency.value = value;
                    }
                });
                
                return oscillator;
            };
            
            return context;
        };
    }
    
    console.log('✅ 音频指纹已虚拟化');
}

// 虚拟化网络信息
function spoofNetworkInfo() {
    console.log('🌍 虚拟化网络信息...');
    
    // 劫持navigator.connection
    if (navigator.connection) {
        const connectionTypes = ['wifi', '4g', '3g', '2g'];
        const randomType = connectionTypes[Math.floor(Math.random() * connectionTypes.length)];
        
        Object.defineProperty(navigator.connection, 'effectiveType', {
            get: function() {
                return randomType;
            },
            configurable: true
        });
        
        Object.defineProperty(navigator.connection, 'downlink', {
            get: function() {
                return Math.random() * 10 + 5; // 5-15 Mbps
            },
            configurable: true
        });
    }
    
    console.log('✅ 网络信息已虚拟化');
}

// 主虚拟化函数
function spoofAllFingerprints() {
    console.log('🚀 开始全面虚拟化浏览器指纹...');
    console.log('=====================================');
    
    spoofUserAgent();
    spoofScreenInfo();
    spoofTimezone();
    spoofWebGL();
    spoofCanvasFingerprint();
    spoofFontList();
    spoofAudioFingerprint();
    spoofNetworkInfo();
    
    console.log('=====================================');
    console.log('🎉 浏览器指纹虚拟化完成！');
    console.log('💡 现在你的浏览器看起来像全新的用户');
    
    // 显示当前虚拟指纹信息
    const fingerprint = generateRandomFingerprint();
    console.log('📊 当前虚拟指纹信息:');
    console.log(`   User-Agent: ${fingerprint.userAgent}`);
    console.log(`   屏幕分辨率: ${fingerprint.screenResolution}`);
    console.log(`   时区: ${fingerprint.timezone}`);
    console.log(`   语言: ${fingerprint.language}`);
    console.log(`   WebGL: ${fingerprint.webglVendor} - ${fingerprint.webglRenderer}`);
}

// 自动执行虚拟化
spoofAllFingerprints();

// 导出函数供手动调用
window.spoofFingerprint = spoofAllFingerprints; 