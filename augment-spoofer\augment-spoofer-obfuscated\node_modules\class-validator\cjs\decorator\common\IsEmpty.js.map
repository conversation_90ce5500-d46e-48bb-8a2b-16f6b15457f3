{"version": 3, "file": "IsEmpty.js", "sourceRoot": "", "sources": ["../../../../src/decorator/common/IsEmpty.ts"], "names": [], "mappings": ";;;AACA,qDAAgE;AAEnD,QAAA,QAAQ,GAAG,SAAS,CAAC;AAElC;;GAEG;AACH,SAAgB,OAAO,CAAC,KAAc;IACpC,OAAO,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,CAAC;AAC/D,CAAC;AAFD,0BAEC;AAED;;GAEG;AACH,SAAgB,OAAO,CAAC,iBAAqC;IAC3D,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,gBAAQ;QACd,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;YAClD,cAAc,EAAE,IAAA,yBAAY,EAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,yBAAyB,EAAE,iBAAiB,CAAC;SACtG;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAXD,0BAWC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const IS_EMPTY = 'isEmpty';\n\n/**\n * Checks if given value is empty (=== '', === null, === undefined).\n */\nexport function isEmpty(value: unknown): boolean {\n  return value === '' || value === null || value === undefined;\n}\n\n/**\n * Checks if given value is empty (=== '', === null, === undefined).\n */\nexport function IsEmpty(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_EMPTY,\n      validator: {\n        validate: (value, args): boolean => isEmpty(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be empty', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}