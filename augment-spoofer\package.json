{"name": "augment-spoofer", "displayName": "Augment Environment Spoofer", "description": "Advanced environment virtualization for AugmentCode plugin", "version": "1.0.0", "publisher": "augment-spoofer", "engines": {"vscode": "^1.74.0"}, "enabledApiProposals": ["telemetry", "authentication", "resolvers"], "repository": {"type": "git", "url": "https://github.com/augment-spoofer/augment-spoofer.git"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "augment-spoofer.toggle", "title": "Toggle Augment Spoofer", "category": "Augment Spoofer"}, {"command": "augment-spoofer.status", "title": "Show Spoofer Status", "category": "Augment Spoofer"}, {"command": "augment-spoofer.refresh", "title": "Refresh Virtual Identity", "category": "Augment Spoofer"}, {"command": "augment-spoofer.monitor", "title": "Show Virtualization Monitor", "category": "Augment Spoofer"}, {"command": "augment-spoofer.verify", "title": "Verify Virtualization Status", "category": "Augment Spoofer"}], "menus": {"commandPalette": [{"command": "augment-spoofer.toggle", "when": "true"}, {"command": "augment-spoofer.status", "when": "true"}, {"command": "augment-spoofer.refresh", "when": "true"}, {"command": "augment-spoofer.monitor", "when": "true"}, {"command": "augment-spoofer.verify", "when": "true"}]}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "package": "vsce package", "install-local": "code --install-extension augment-spoofer-1.0.0.vsix"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "typescript": "^5.0.0"}}