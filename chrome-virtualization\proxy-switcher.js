// 代理切换工具 - 帮助更换IP地址
// 使用方法：在Chrome控制台中运行此脚本

console.log('🌐 代理切换工具启动...');

// 免费代理服务器列表（示例）
const FREE_PROXIES = [
    // 注意：这些是示例代理，实际使用时需要替换为有效的代理
    // 格式：{ host: '代理服务器地址', port: 端口, protocol: '协议' }
    { host: 'proxy1.example.com', port: 8080, protocol: 'http' },
    { host: 'proxy2.example.com', port: 3128, protocol: 'http' },
    { host: 'proxy3.example.com', port: 80, protocol: 'http' }
];

// 代理配置管理
class ProxyManager {
    constructor() {
        this.currentProxy = null;
        this.proxyList = [...FREE_PROXIES];
        this.proxyIndex = 0;
    }

    // 获取下一个代理
    getNextProxy() {
        if (this.proxyList.length === 0) {
            console.log('❌ 没有可用的代理服务器');
            return null;
        }

        this.currentProxy = this.proxyList[this.proxyIndex];
        this.proxyIndex = (this.proxyIndex + 1) % this.proxyList.length;
        
        console.log(`✅ 切换到代理: ${this.currentProxy.protocol}://${this.currentProxy.host}:${this.currentProxy.port}`);
        return this.currentProxy;
    }

    // 获取当前代理
    getCurrentProxy() {
        return this.currentProxy;
    }

    // 添加自定义代理
    addProxy(host, port, protocol = 'http') {
        this.proxyList.push({ host, port, protocol });
        console.log(`✅ 已添加代理: ${protocol}://${host}:${port}`);
    }

    // 清除所有代理
    clearProxies() {
        this.proxyList = [];
        this.currentProxy = null;
        console.log('✅ 已清除所有代理');
    }

    // 测试代理连接
    async testProxy(proxy) {
        return new Promise((resolve) => {
            const testUrl = 'https://httpbin.org/ip';
            const xhr = new XMLHttpRequest();
            
            xhr.timeout = 10000; // 10秒超时
            
            xhr.onload = function() {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        console.log(`✅ 代理测试成功: ${response.origin}`);
                        resolve(true);
                    } catch (e) {
                        console.log('❌ 代理测试失败: 响应格式错误');
                        resolve(false);
                    }
                } else {
                    console.log(`❌ 代理测试失败: HTTP ${xhr.status}`);
                    resolve(false);
                }
            };
            
            xhr.onerror = function() {
                console.log('❌ 代理测试失败: 网络错误');
                resolve(false);
            };
            
            xhr.ontimeout = function() {
                console.log('❌ 代理测试失败: 超时');
                resolve(false);
            };
            
            // 注意：这里只是示例，实际代理设置需要通过浏览器扩展或系统设置
            xhr.open('GET', testUrl, true);
            xhr.send();
        });
    }
}

// 创建代理管理器实例
const proxyManager = new ProxyManager();

// 网络请求拦截器
class NetworkInterceptor {
    constructor() {
        this.isActive = false;
        this.interceptedRequests = 0;
    }

    // 激活网络拦截
    activate() {
        if (this.isActive) {
            console.log('⚠️  网络拦截器已经激活');
            return;
        }

        this.isActive = true;
        this.interceptFetch();
        this.interceptXHR();
        
        console.log('✅ 网络拦截器已激活');
        console.log('💡 所有发往AugmentCode的请求都会被记录');
    }

    // 停用网络拦截
    deactivate() {
        this.isActive = false;
        console.log('✅ 网络拦截器已停用');
    }

    // 拦截fetch请求
    interceptFetch() {
        const originalFetch = window.fetch;
        
        window.fetch = async (input, init) => {
            const url = typeof input === 'string' ? input : input.url;
            
            if (this.isActive && url.includes('augmentcode')) {
                this.interceptedRequests++;
                console.log(`🌐 拦截到AugmentCode请求 #${this.interceptedRequests}: ${url}`);
                
                // 记录请求头
                if (init && init.headers) {
                    console.log('📋 请求头:', init.headers);
                }
                
                // 记录请求体
                if (init && init.body) {
                    console.log('📦 请求体:', init.body);
                }
            }
            
            return originalFetch(input, init);
        };
    }

    // 拦截XMLHttpRequest
    interceptXHR() {
        const originalOpen = XMLHttpRequest.prototype.open;
        const originalSend = XMLHttpRequest.prototype.send;
        
        XMLHttpRequest.prototype.open = function(method, url, ...args) {
            if (proxyManager.isActive && url.includes('augmentcode')) {
                console.log(`🌐 拦截到AugmentCode XHR请求: ${method} ${url}`);
            }
            return originalOpen.call(this, method, url, ...args);
        };
        
        XMLHttpRequest.prototype.send = function(data) {
            if (proxyManager.isActive && data) {
                console.log('📦 XHR请求体:', data);
            }
            return originalSend.call(this, data);
        };
    }

    // 获取拦截统计
    getStats() {
        return {
            isActive: this.isActive,
            interceptedRequests: this.interceptedRequests
        };
    }

    // 重置统计
    resetStats() {
        this.interceptedRequests = 0;
        console.log('✅ 已重置拦截统计');
    }
}

// 创建网络拦截器实例
const networkInterceptor = new NetworkInterceptor();

// IP地址检测工具
class IPDetector {
    constructor() {
        this.currentIP = null;
        this.ipHistory = [];
    }

    // 检测当前IP地址
    async detectIP() {
        try {
            const response = await fetch('https://httpbin.org/ip');
            const data = await response.json();
            
            this.currentIP = data.origin;
            this.ipHistory.push({
                ip: this.currentIP,
                timestamp: new Date().toISOString()
            });
            
            console.log(`🌍 当前IP地址: ${this.currentIP}`);
            return this.currentIP;
        } catch (error) {
            console.log('❌ IP检测失败:', error.message);
            return null;
        }
    }

    // 获取IP历史记录
    getIPHistory() {
        return this.ipHistory;
    }

    // 清除IP历史记录
    clearIPHistory() {
        this.ipHistory = [];
        console.log('✅ 已清除IP历史记录');
    }

    // 检查IP是否发生变化
    async checkIPChange() {
        const oldIP = this.currentIP;
        const newIP = await this.detectIP();
        
        if (oldIP && newIP && oldIP !== newIP) {
            console.log(`🔄 IP地址已变化: ${oldIP} → ${newIP}`);
            return true;
        } else if (oldIP === newIP) {
            console.log(`ℹ️  IP地址未变化: ${newIP}`);
            return false;
        }
        
        return null;
    }
}

// 创建IP检测器实例
const ipDetector = new IPDetector();

// 自动化工具
class AutomationTools {
    constructor() {
        this.isRunning = false;
    }

    // 自动切换代理
    async autoSwitchProxy() {
        if (this.isRunning) {
            console.log('⚠️  自动切换已在进行中');
            return;
        }

        this.isRunning = true;
        console.log('🔄 开始自动切换代理...');

        while (this.isRunning) {
            const proxy = proxyManager.getNextProxy();
            if (proxy) {
                console.log(`🔄 切换到代理: ${proxy.host}:${proxy.port}`);
                
                // 等待一段时间让代理生效
                await this.sleep(5000);
                
                // 检测IP变化
                const ipChanged = await ipDetector.checkIPChange();
                if (ipChanged) {
                    console.log('✅ IP地址已成功切换');
                    break;
                } else {
                    console.log('❌ IP地址未变化，尝试下一个代理');
                }
            } else {
                console.log('❌ 没有更多可用代理');
                break;
            }
        }

        this.isRunning = false;
    }

    // 停止自动切换
    stopAutoSwitch() {
        this.isRunning = false;
        console.log('✅ 已停止自动切换');
    }

    // 延时函数
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 完整的注册流程自动化
    async automateRegistration() {
        console.log('🤖 开始自动化注册流程...');
        
        // 1. 清理浏览器数据
        console.log('🧹 步骤1: 清理浏览器数据');
        if (typeof window.clearAugmentData === 'function') {
            await window.clearAugmentData();
        }
        
        // 2. 虚拟化浏览器指纹
        console.log('🎭 步骤2: 虚拟化浏览器指纹');
        if (typeof window.spoofFingerprint === 'function') {
            window.spoofFingerprint();
        }
        
        // 3. 切换代理
        console.log('🌐 步骤3: 切换代理');
        await this.autoSwitchProxy();
        
        // 4. 激活网络监控
        console.log('📡 步骤4: 激活网络监控');
        networkInterceptor.activate();
        
        console.log('🎉 自动化设置完成！');
        console.log('💡 现在可以访问AugmentCode进行注册了');
    }
}

// 创建自动化工具实例
const automationTools = new AutomationTools();

// 导出到全局对象
window.proxyManager = proxyManager;
window.networkInterceptor = networkInterceptor;
window.ipDetector = ipDetector;
window.automationTools = automationTools;

// 显示使用说明
console.log('=====================================');
console.log('🌐 代理切换工具使用说明');
console.log('=====================================');
console.log('📋 可用命令:');
console.log('  proxyManager.getNextProxy() - 切换到下一个代理');
console.log('  proxyManager.getCurrentProxy() - 获取当前代理');
console.log('  proxyManager.addProxy(host, port, protocol) - 添加自定义代理');
console.log('  ipDetector.detectIP() - 检测当前IP地址');
console.log('  ipDetector.checkIPChange() - 检查IP是否变化');
console.log('  networkInterceptor.activate() - 激活网络监控');
console.log('  networkInterceptor.deactivate() - 停用网络监控');
console.log('  automationTools.autoSwitchProxy() - 自动切换代理');
console.log('  automationTools.automateRegistration() - 完整自动化流程');
console.log('=====================================');

// 自动检测当前IP
ipDetector.detectIP(); 