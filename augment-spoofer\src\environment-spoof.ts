import * as vscode from 'vscode';
import { DynamicIdentityGenerator } from './utils/random-generator';

export class EnvironmentSpoofer {
    private identityGenerator = DynamicIdentityGenerator.getInstance();
    private isActive = false;
    private originalProcessEnv: any = null;
    private originalVscodeEnv: any = null;

    activate(): void {
        if (this.isActive) {
            return;
        }

        // 保存原始对象引用
        this.originalProcessEnv = process.env;
        this.originalVscodeEnv = vscode.env;

        // 劫持process.env
        this.spoofProcessEnv();
        
        // 劫持vscode.env
        this.spoofVscodeEnv();
        
        // 劫持地理位置和时区信息
        this.spoofGeolocation();
        
        // 劫持workspace信息
        this.spoofWorkspaceInfo();
        
        this.isActive = true;
        console.log('🌍 Environment Spoofer 已激活');
    }

    deactivate(): void {
        if (!this.isActive) {
            return;
        }

        try {
            // 恢复原始对象
            if (this.originalProcessEnv) {
                process.env = this.originalProcessEnv;
            }
            
            // 注意：vscode.env 是只读的，无法恢复，只能清理虚拟环境对象
            (this as any).virtualEnv = null;
            
            console.log('🌍 Environment Spoofer 已停用');
        } catch (error) {
            console.warn('⚠️ Environment Spoofer 停用失败:', error);
        }

        this.isActive = false;
    }

    // 劫持process.env
    private spoofProcessEnv(): void {
        const identity = this.identityGenerator.getCurrentIdentity();
        const user = identity.user;

        process.env = new Proxy(this.originalProcessEnv, {
            get(target: any, prop: string | symbol): any {
                // 只处理字符串属性
                if (typeof prop !== 'string') {
                    return target[prop];
                }

                const key = prop.toUpperCase();
                
                switch (key) {
                    case 'USERNAME':
                        return user.username;
                    case 'USERPROFILE':
                        return user.profilePath;
                    case 'HOMEPATH':
                        return `\\Users\\${user.username}`;
                    case 'HOMEDRIVE':
                        return 'C:';
                    case 'APPDATA':
                        return `${user.profilePath}\\AppData\\Roaming`;
                    case 'LOCALAPPDATA':
                        return `${user.profilePath}\\AppData\\Local`;
                    case 'TEMP':
                        return `${user.profilePath}\\AppData\\Local\\Temp`;
                    case 'TMP':
                        return `${user.profilePath}\\AppData\\Local\\Temp`;
                    default:
                        return target[prop];
                }
            },
            set(target: any, prop: string | symbol, value: any): boolean {
                // 只处理字符串属性
                if (typeof prop !== 'string') {
                    target[prop] = value;
                    return true;
                }

                const key = prop.toUpperCase();
                
                // 阻止修改被劫持的环境变量
                const protectedKeys = ['USERNAME', 'USERPROFILE', 'HOMEPATH', 'HOMEDRIVE'];
                if (protectedKeys.includes(key)) {
                    console.log(`🚫 阻止修改环境变量: ${key}`);
                    return true; // 假装设置成功
                }
                
                target[prop] = value;
                return true;
            }
        });
    }

    // 劫持vscode.env
    private spoofVscodeEnv(): void {
        const identity = this.identityGenerator.getCurrentIdentity();
        const device = identity.device;
        const user = identity.user;
        const country = identity.country;

        try {
            console.log('🎭 VSCode环境伪装已启用（只读模式）');
            console.log(`🔧 虚拟 machineId: ${device.machineId}`);
            console.log(`🔧 虚拟 sessionId: ${device.sessionId}`);
            console.log(`🔧 虚拟 appRoot: ${user.profilePath}\\AppData\\Local\\Programs\\Microsoft VS Code`);
            console.log(`🌍 虚拟国家: ${country.country} (${country.countryCode})`);
            console.log(`⏰ 虚拟时区: ${country.timezone}`);
            console.log(`🗣️ 虚拟语言: ${country.language}`);
            
            // 注意：由于vscode.env的属性大多是只读的，我们不能直接修改它们
            // 这里只是记录虚拟值，实际的拦截需要在网络层或其他地方进行
            
            // 创建一个虚拟环境对象供其他组件使用
            (this as any).virtualEnv = {
                machineId: device.machineId,
                sessionId: device.sessionId,
                appName: 'Visual Studio Code',
                appRoot: `${user.profilePath}\\AppData\\Local\\Programs\\Microsoft VS Code`,
                language: country.language,
                uiKind: 1, // Desktop
                // 添加地理位置信息
                country: country.country,
                countryCode: country.countryCode,
                timezone: country.timezone,
                locale: country.locale
            };
            
        } catch (error) {
            console.warn('⚠️ VSCode环境伪装设置失败:', error);
        }
    }

    // 劫持地理位置和时区信息
    private spoofGeolocation(): void {
        const identity = this.identityGenerator.getCurrentIdentity();
        const country = identity.country;

        try {
            // 劫持时区相关的JavaScript API
            if (typeof Intl !== 'undefined' && Intl.DateTimeFormat) {
                const originalResolvedOptions = Intl.DateTimeFormat.prototype.resolvedOptions;
                Intl.DateTimeFormat.prototype.resolvedOptions = function() {
                    const options = originalResolvedOptions.call(this);
                    return {
                        ...options,
                        timeZone: country.timezone,
                        locale: country.locale
                    };
                };
            }

            // 劫持语言检测
            if (typeof navigator !== 'undefined') {
                Object.defineProperty(navigator, 'language', {
                    get: () => country.language,
                    configurable: true
                });
                
                Object.defineProperty(navigator, 'languages', {
                    get: () => [country.language, 'en'],
                    configurable: true
                });
            }

            // 劫持时区偏移量
            const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
            Date.prototype.getTimezoneOffset = function() {
                // 根据虚拟时区计算偏移量（这里简化处理）
                const timezoneOffsets: {[key: string]: number} = {
                    'America/New_York': 300,    // UTC-5
                    'America/Toronto': 300,     // UTC-5
                    'Europe/London': 0,         // UTC+0
                    'Europe/Berlin': -60,       // UTC+1
                    'Europe/Paris': -60,        // UTC+1
                    'Asia/Tokyo': -540,         // UTC+9
                    'Australia/Sydney': -660,   // UTC+11
                    'Asia/Kolkata': -330,       // UTC+5:30
                    'America/Sao_Paulo': 180,   // UTC-3
                    'Asia/Shanghai': -480       // UTC+8
                };
                
                return timezoneOffsets[country.timezone] || 0;
            };

            console.log(`🌍 地理位置伪装已启用: ${country.country}`);
            console.log(`⏰ 时区伪装已启用: ${country.timezone}`);
            
        } catch (error) {
            console.warn('⚠️ 地理位置伪装失败:', error);
        }
    }

    // 劫持workspace信息
    private spoofWorkspaceInfo(): void {
        const identity = this.identityGenerator.getCurrentIdentity();
        const workspace = identity.workspace;

        // 劫持vscode.workspace的相关方法
        const originalGetWorkspaceFolder = vscode.workspace.getWorkspaceFolder;
        
        (vscode.workspace as any).getWorkspaceFolder = (uri: vscode.Uri) => {
            // 返回虚拟的workspace信息
            return {
                uri: vscode.Uri.file(workspace.root),
                name: workspace.workspaceName,
                index: 0
            };
        };

        // 劫持workspace.workspaceFolders
        Object.defineProperty(vscode.workspace, 'workspaceFolders', {
            get: () => {
                return workspace.workspaceFolders.map((folder, index) => ({
                    uri: vscode.Uri.file(folder.uri),
                    name: folder.name,
                    index: index
                }));
            },
            configurable: true
        });

        // 劫持workspace.rootPath (已废弃但可能仍在使用)
        Object.defineProperty(vscode.workspace, 'rootPath', {
            get: () => workspace.root,
            configurable: true
        });
    }

    // 获取当前状态
    getStatus(): string {
        if (!this.isActive) {
            return '❌ 未激活';
        }
        
        const identity = this.identityGenerator.getCurrentIdentity();
        return `✅ 已激活 | 用户: ${identity.user.username} | 工作区: ${identity.workspace.workspaceName}`;
    }

    // 获取详细状态
    getDetailedStatus(): object {
        if (!this.isActive) {
            return { active: false };
        }
        
        const identity = this.identityGenerator.getCurrentIdentity();
        return {
            active: true,
            spoofedEnvVars: {
                USERNAME: identity.user.username,
                USERPROFILE: identity.user.profilePath,
                HOMEPATH: `\\Users\\${identity.user.username}`,
                APPDATA: `${identity.user.profilePath}\\AppData\\Roaming`
            },
            spoofedVscodeEnv: {
                machineId: identity.device.machineId.substring(0, 8) + '...',
                sessionId: identity.device.sessionId.substring(0, 8) + '...',
                appName: 'Visual Studio Code'
            },
            spoofedWorkspace: {
                name: identity.workspace.workspaceName,
                root: identity.workspace.root
            }
        };
    }

    // 获取虚拟环境值
    getVirtualEnv(): any {
        return (this as any).virtualEnv || {};
    }

    // 获取虚拟 machineId
    getVirtualMachineId(): string {
        const virtualEnv = this.getVirtualEnv();
        return virtualEnv.machineId || '';
    }

    // 获取虚拟 sessionId
    getVirtualSessionId(): string {
        const virtualEnv = this.getVirtualEnv();
        return virtualEnv.sessionId || '';
    }
} 