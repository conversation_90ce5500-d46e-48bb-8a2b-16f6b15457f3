{"version": 3, "file": "IsLocale.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsLocale.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,sEAAuD;AAE1C,QAAA,SAAS,GAAG,UAAU,CAAC;AAEpC;;;GAGG;AACH,SAAgB,QAAQ,CAAC,KAAc;IACrC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,kBAAiB,EAAC,KAAK,CAAC,CAAC;AAC/D,CAAC;AAFD,4BAEC;AAED;;;GAGG;AACH,SAAgB,QAAQ,CAAC,iBAAqC;IAC5D,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,iBAAS;QACf,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC;YACnD,cAAc,EAAE,IAAA,yBAAY,EAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,0BAA0B,EAAE,iBAAiB,CAAC;SACvG;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAXD,4BAWC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isLocaleValidator from 'validator/lib/isLocale';\n\nexport const IS_LOCALE = 'isLocale';\n\n/**\n * Check if the string is a locale.\n * If given value is not a string, then it returns false.\n */\nexport function isLocale(value: unknown): boolean {\n  return typeof value === 'string' && isLocaleValidator(value);\n}\n\n/**\n * Check if the string is a locale.\n * If given value is not a string, then it returns false.\n */\nexport function IsLocale(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_LOCALE,\n      validator: {\n        validate: (value, args): boolean => isLocale(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be locale', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}