var K=Object.defineProperty;var R=(a,o,t)=>o in a?K(a,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[o]=t;var u=(a,o,t)=>R(a,typeof o!="symbol"?o+"":o,t);import{A as w,z as X,ao as Y,y as b,S as D,i as F,s as z,d as g,t as h,q as f,o as O,p as j,c as m,N as S,al as L,W as y,D as x,E as T,F as C,G as N,h as I,J as q,K as A,L as G,M as H,Y as M,X as J,ad as _}from"./SpinnerAugment-VfHtkDdv.js";import"./index-6WVCg-U8.js";const $=class ${constructor(){u(this,"_monaco",w(null));u(this,"_isLoading",w(!0));u(this,"_error",w(null));u(this,"isReady",Y([this._monaco,this._isLoading,this._error],([o,t,n])=>o!==null&&!t&&n===null));X($.CONTEXT_KEY,this),this._initializeMonaco()}async _initializeMonaco(){var o;try{if(this._isLoading.set(!0),!((o=window.augmentDeps)!=null&&o.monaco))throw new Error("Monaco is not available. Make sure monaco-bootstrap.js is included in your HTML.");const t=await window.augmentDeps.monaco;this._monaco.set(t),this._isLoading.set(!1)}catch(t){this._error.set(t instanceof Error?t:new Error(String(t))),this._isLoading.set(!1),console.error("Failed to load Monaco:",t)}}get monaco(){return this._monaco}get isLoading(){return this._isLoading}get error(){return this._error}static getContext(){const o=b($.CONTEXT_KEY);if(!o)throw new Error("Monaco context not found. Make sure you're using MonacoProvider.Root as a parent component.");return o}};u($,"CONTEXT_KEY","augment-monaco-provider");let E=$;function P(a){let o;const t=a[7].default,n=q(t,a,a[8],null);return{c(){n&&n.c()},m(s,r){n&&n.m(s,r),o=!0},p(s,r){n&&n.p&&(!o||256&r)&&A(n,t,s,s[8],o?H(t,s[8],r,null):G(s[8]),null)},i(s){o||(f(n,s),o=!0)},o(s){h(n,s),o=!1},d(s){n&&n.d(s)}}}function W(a){let o,t,n;return t=new y({props:{size:1,color:"error",$$slots:{default:[Q]},$$scope:{ctx:a}}}),{c(){o=C("div"),N(t.$$.fragment),I(o,"class","c-monaco-provider__error svelte-kfknmo")},m(s,r){m(s,o,r),T(t,o,null),n=!0},p(s,r){const i={};260&r&&(i.$$scope={dirty:r,ctx:s}),t.$set(i)},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){h(t.$$.fragment,s),n=!1},d(s){s&&g(o),x(t)}}}function B(a){let o,t,n;return t=new y({props:{size:1,color:"neutral",$$slots:{default:[U]},$$scope:{ctx:a}}}),{c(){o=C("div"),N(t.$$.fragment),I(o,"class","c-monaco-provider__loading svelte-kfknmo")},m(s,r){m(s,o,r),T(t,o,null),n=!0},p(s,r){const i={};256&r&&(i.$$scope={dirty:r,ctx:s}),t.$set(i)},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){h(t.$$.fragment,s),n=!1},d(s){s&&g(o),x(t)}}}function Q(a){let o,t,n=a[2].message+"";return{c(){o=M("Failed to load Monaco Editor: "),t=M(n)},m(s,r){m(s,o,r),m(s,t,r)},p(s,r){4&r&&n!==(n=s[2].message+"")&&J(t,n)},d(s){s&&(g(o),g(t))}}}function U(a){let o;return{c(){o=M("Loading Monaco Editor...")},m(t,n){m(t,o,n)},d(t){t&&g(o)}}}function V(a){let o,t,n,s;const r=[B,W,P],i=[];function p(e,c){return e[1]&&e[0]?0:e[2]?1:e[3]?2:-1}return~(o=p(a))&&(t=i[o]=r[o](a)),{c(){t&&t.c(),n=S()},m(e,c){~o&&i[o].m(e,c),m(e,n,c),s=!0},p(e,[c]){let d=o;o=p(e),o===d?~o&&i[o].p(e,c):(t&&(O(),h(i[d],1,1,()=>{i[d]=null}),j()),~o?(t=i[o],t?t.p(e,c):(t=i[o]=r[o](e),t.c()),f(t,1),t.m(n.parentNode,n)):t=null)},i(e){s||(f(t),s=!0)},o(e){h(t),s=!1},d(e){e&&g(n),~o&&i[o].d(e)}}}function Z(a,o,t){let n,s,r,{$$slots:i={},$$scope:p}=o,{showLoadingIndicator:e=!0}=o;const c=new E,d=c.isLoading;L(a,d,l=>t(1,n=l));const v=c.error;L(a,v,l=>t(2,s=l));const k=c.isReady;return L(a,k,l=>t(3,r=l)),a.$$set=l=>{"showLoadingIndicator"in l&&t(0,e=l.showLoadingIndicator),"$$scope"in l&&t(8,p=l.$$scope)},[e,n,s,r,d,v,k,i,p]}const tt=new Map([[_.light,"hc-light"],[_.dark,"hc-black"]]),ot=new Map([[_.light,"vs"],[_.dark,"vs-dark"]]);function rt(a,o){if(!a)return"";let t=ot;return o==="high-contrast"&&(t=tt),t.get(a)||""}const et={Root:class extends D{constructor(a){super(),F(this,a,Z,V,z,{showLoadingIndicator:0})}}};export{ot as C,et as M,E as a,rt as g};
