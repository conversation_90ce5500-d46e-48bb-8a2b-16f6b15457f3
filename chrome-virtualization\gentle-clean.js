// 🧹 AugmentCode温和数据清理脚本 - 只清理AugmentCode相关数据
// 使用方法：在Brave/Chrome控制台中运行此脚本
// 特点：只清理AugmentCode相关数据，保留其他网站的所有数据

console.log('🧹 AugmentCode温和数据清理开始...');
console.log('💡 只清理AugmentCode相关数据，保留其他网站数据');

// AugmentCode相关网址
const AUGMENT_URLS = [
    'app.augmentcode.com',
    'login.augmentcode.com', 
    'www.augmentcode.com',
    'api.augmentcode.com'
];

// 检查是否包含AugmentCode相关
function isAugmentRelated(key, value) {
    const augmentKeywords = [
        'augmentcode',
        'augment',
        'claude',
        'sonnet',
        'anthropic'
    ];
    
    const keyLower = key.toLowerCase();
    const valueLower = (value || '').toLowerCase();
    
    return augmentKeywords.some(keyword => 
        keyLower.includes(keyword) || valueLower.includes(keyword)
    );
}

// 1. 温和清理Cache Storage
async function gentleClearCacheStorage() {
    console.log('🗂️  温和清理Cache Storage...');
    try {
        const cacheNames = await caches.keys();
        let clearedCount = 0;
        
        for (const cacheName of cacheNames) {
            // 只清理包含augmentcode的缓存
            if (cacheName.includes('augmentcode') || cacheName.includes('augment')) {
                await caches.delete(cacheName);
                clearedCount++;
                console.log(`✅ 已删除AugmentCode缓存: ${cacheName}`);
            }
        }
        
        if (clearedCount === 0) {
            console.log('ℹ️  没有发现AugmentCode相关的缓存');
        } else {
            console.log(`✅ 已清理 ${clearedCount} 个AugmentCode相关缓存`);
        }
    } catch (error) {
        console.log('❌ 清理Cache Storage失败:', error);
    }
}

// 2. 温和清理Session Storage
function gentleClearSessionStorage() {
    console.log('📝 温和清理Session Storage...');
    try {
        const keys = Object.keys(sessionStorage);
        let clearedCount = 0;
        
        for (const key of keys) {
            const value = sessionStorage.getItem(key);
            if (isAugmentRelated(key, value)) {
                sessionStorage.removeItem(key);
                clearedCount++;
            }
        }
        
        if (clearedCount === 0) {
            console.log('ℹ️  没有发现AugmentCode相关的Session Storage数据');
        } else {
            console.log(`✅ 已清理 ${clearedCount} 个AugmentCode相关的Session Storage项`);
        }
    } catch (error) {
        console.log('❌ 清理Session Storage失败:', error);
    }
}

// 3. 温和清理Local Storage
function gentleClearLocalStorage() {
    console.log('💾 温和清理Local Storage...');
    try {
        const keys = Object.keys(localStorage);
        let clearedCount = 0;
        
        for (const key of keys) {
            const value = localStorage.getItem(key);
            if (isAugmentRelated(key, value)) {
                localStorage.removeItem(key);
                clearedCount++;
            }
        }
        
        if (clearedCount === 0) {
            console.log('ℹ️  没有发现AugmentCode相关的Local Storage数据');
        } else {
            console.log(`✅ 已清理 ${clearedCount} 个AugmentCode相关的Local Storage项`);
        }
    } catch (error) {
        console.log('❌ 清理Local Storage失败:', error);
    }
}

// 4. 温和清理Cookies
function gentleClearCookies() {
    console.log('🍪 温和清理Cookies...');
    try {
        const cookies = document.cookie.split(';');
        let clearedCount = 0;
        
        for (const cookie of cookies) {
            const [name] = cookie.split('=');
            const trimmedName = name.trim();
            
            // 只清理AugmentCode相关的cookies
            if (isAugmentRelated(trimmedName, '')) {
                // 删除cookie（尝试多个域名）
                document.cookie = `${trimmedName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
                document.cookie = `${trimmedName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.augmentcode.com`;
                document.cookie = `${trimmedName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=augmentcode.com`;
                clearedCount++;
            }
        }
        
        if (clearedCount === 0) {
            console.log('ℹ️  没有发现AugmentCode相关的Cookies');
        } else {
            console.log(`✅ 已清理 ${clearedCount} 个AugmentCode相关的Cookies`);
        }
    } catch (error) {
        console.log('❌ 清理Cookies失败:', error);
    }
}

// 5. 温和清理IndexedDB
async function gentleClearIndexedDB() {
    console.log('🗄️  温和清理IndexedDB...');
    try {
        const databases = await window.indexedDB.databases();
        let clearedCount = 0;
        
        for (const db of databases) {
            if (isAugmentRelated(db.name, '')) {
                await window.indexedDB.deleteDatabase(db.name);
                clearedCount++;
                console.log(`✅ 已删除AugmentCode数据库: ${db.name}`);
            }
        }
        
        if (clearedCount === 0) {
            console.log('ℹ️  没有发现AugmentCode相关的IndexedDB数据库');
        } else {
            console.log(`✅ 已清理 ${clearedCount} 个AugmentCode相关的IndexedDB数据库`);
        }
    } catch (error) {
        console.log('❌ 清理IndexedDB失败:', error);
    }
}

// 6. 温和清理Service Workers
async function gentleClearServiceWorkers() {
    console.log('⚙️  温和清理Service Workers...');
    try {
        if ('serviceWorker' in navigator) {
            const registrations = await navigator.serviceWorker.getRegistrations();
            let clearedCount = 0;
            
            for (const registration of registrations) {
                if (registration.scope.includes('augmentcode') || 
                    registration.active?.scriptURL.includes('augmentcode')) {
                    await registration.unregister();
                    clearedCount++;
                }
            }
            
            if (clearedCount === 0) {
                console.log('ℹ️  没有发现AugmentCode相关的Service Workers');
            } else {
                console.log(`✅ 已清理 ${clearedCount} 个AugmentCode相关的Service Workers`);
            }
        }
    } catch (error) {
        console.log('❌ 清理Service Workers失败:', error);
    }
}

// 7. 温和清理浏览器指纹相关数据
function gentleClearFingerprintData() {
    console.log('👆 温和清理指纹相关数据...');
    try {
        // 只清理Canvas指纹缓存（不影响其他网站）
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (ctx) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }
        
        // 只清理WebGL指纹缓存（不影响其他网站）
        const canvas2 = document.createElement('canvas');
        const gl = canvas2.getContext('webgl') || canvas2.getContext('experimental-webgl');
        if (gl) {
            gl.clear(gl.COLOR_BUFFER_BIT);
        }
        
        console.log('✅ 已清理指纹相关数据');
    } catch (error) {
        console.log('❌ 清理指纹数据失败:', error);
    }
}

// 8. 显示清理统计
function showCleanupStats() {
    console.log('📊 清理统计:');
    console.log('=====================================');
    
    // 统计剩余数据
    const remainingLocalStorage = Object.keys(localStorage).length;
    const remainingSessionStorage = Object.keys(sessionStorage).length;
    const remainingCookies = document.cookie ? document.cookie.split(';').length : 0;
    
    console.log(`📝 剩余Local Storage项目: ${remainingLocalStorage}`);
    console.log(`📝 剩余Session Storage项目: ${remainingSessionStorage}`);
    console.log(`🍪 剩余Cookies: ${remainingCookies}`);
    
    // 显示一些剩余的数据示例（不显示敏感信息）
    if (remainingLocalStorage > 0) {
        const sampleKeys = Object.keys(localStorage).slice(0, 3);
        console.log(`📋 Local Storage示例: ${sampleKeys.join(', ')}${remainingLocalStorage > 3 ? '...' : ''}`);
    }
    
    if (remainingCookies > 0) {
        const sampleCookies = document.cookie.split(';').slice(0, 3).map(c => c.split('=')[0].trim());
        console.log(`🍪 Cookies示例: ${sampleCookies.join(', ')}${remainingCookies > 3 ? '...' : ''}`);
    }
    
    console.log('=====================================');
    console.log('✅ 温和清理完成！其他网站数据已保留');
}

// 主清理函数
async function gentleCleanup() {
    console.log('🚀 开始AugmentCode温和数据清理...');
    console.log('=====================================');
    console.log('💡 只清理AugmentCode相关数据，保留其他网站数据');
    console.log('=====================================');
    
    await gentleClearCacheStorage();
    gentleClearSessionStorage();
    gentleClearLocalStorage();
    gentleClearCookies();
    await gentleClearIndexedDB();
    await gentleClearServiceWorkers();
    gentleClearFingerprintData();
    
    console.log('=====================================');
    showCleanupStats();
    console.log('💡 建议刷新页面以确保清理生效');
}

// 验证清理效果
function verifyGentleCleanup() {
    console.log('🔍 验证温和清理效果...');
    
    const checks = {
        localStorage: Object.keys(localStorage).filter(key => isAugmentRelated(key, '')).length === 0,
        sessionStorage: Object.keys(sessionStorage).filter(key => isAugmentRelated(key, '')).length === 0,
        cookies: !document.cookie.split(';').some(cookie => {
            const [name] = cookie.split('=');
            return isAugmentRelated(name.trim(), '');
        })
    };
    
    console.log('📊 验证结果:');
    console.log(`   AugmentCode localStorage: ${checks.localStorage ? '✅ 已清理' : '❌ 仍有残留'}`);
    console.log(`   AugmentCode sessionStorage: ${checks.sessionStorage ? '✅ 已清理' : '❌ 仍有残留'}`);
    console.log(`   AugmentCode cookies: ${checks.cookies ? '✅ 已清理' : '❌ 仍有残留'}`);
    
    const allPassed = Object.values(checks).every(check => check);
    console.log(`\n🎯 总体结果: ${allPassed ? '✅ 温和清理成功' : '❌ 清理不完整'}`);
    
    return allPassed;
}

// 自动执行清理
gentleCleanup();

// 导出函数供手动调用
window.gentleCleanup = gentleCleanup;
window.verifyGentleCleanup = verifyGentleCleanup;

// 显示使用说明
console.log('=====================================');
console.log('🧹 温和清理脚本使用说明');
console.log('=====================================');
console.log('📋 可用命令:');
console.log('  gentleCleanup() - 执行温和清理');
console.log('  verifyGentleCleanup() - 验证清理效果');
console.log('=====================================');
console.log('💡 特点: 只清理AugmentCode相关数据');
console.log('💡 特点: 保留其他网站的所有数据');
console.log('💡 特点: 安全，不会影响其他网站');
console.log('====================================='); 