import * as vscode from 'vscode';
import { DynamicIdentityGenerator } from './utils/random-generator';

export class VirtualizationMonitor {
    private identityGenerator = DynamicIdentityGenerator.getInstance();
    private isActive = false;
    private outputChannel: vscode.OutputChannel;

    constructor() {
        this.outputChannel = vscode.window.createOutputChannel('Augment Spoofer Monitor');
    }

    activate(): void {
        if (this.isActive) {
            return;
        }

        this.isActive = true;
        this.outputChannel.show();
        this.outputChannel.appendLine('🎭 Augment Spoofer Monitor 已激活');
        this.outputChannel.appendLine('=====================================');
        
        // 显示当前虚拟身份
        this.logCurrentIdentity();
        
        // 开始监控
        this.startMonitoring();
        
        console.log('🔍 Virtualization Monitor 已激活');
    }

    deactivate(): void {
        this.isActive = false;
        this.outputChannel.hide();
        console.log('🔍 Virtualization Monitor 已停用');
    }

    private logCurrentIdentity(): void {
        const identity = this.identityGenerator.getCurrentIdentity();
        
        this.outputChannel.appendLine('🆔 当前虚拟身份:');
        this.outputChannel.appendLine(`👤 用户: ${identity.user.username}`);
        this.outputChannel.appendLine(`📁 工作区: ${identity.workspace.workspaceName}`);
        this.outputChannel.appendLine(`🔐 GitHub: ${identity.github.login}`);
        this.outputChannel.appendLine(`🆔 设备ID: ${identity.device.machineId.substring(0, 8)}...`);
        this.outputChannel.appendLine(`⏰ 生成时间: ${new Date(identity.timestamp).toLocaleString()}`);
        this.outputChannel.appendLine('');
    }

    private startMonitoring(): void {
        // 监控环境变量访问
        this.monitorEnvironmentVariables();
        
        // 监控VS Code环境访问
        this.monitorVSCodeEnvironment();
        
        // 监控网络请求
        this.monitorNetworkRequests();
        
        // 监控GitHub认证
        this.monitorGitHubAuth();
    }

    private monitorEnvironmentVariables(): void {
        const originalEnv = process.env;
        const identity = this.identityGenerator.getCurrentIdentity();
        
        // 创建代理来监控环境变量访问
        const envProxy = new Proxy(originalEnv, {
            get(target: any, prop: string | symbol): any {
                // 只处理字符串属性
                if (typeof prop !== 'string') {
                    return target[prop];
                }
                
                const key = prop;
                const value = target[prop];
                
                // 记录关键环境变量的访问
                const criticalKeys = ['USERNAME', 'USERPROFILE', 'HOMEPATH', 'APPDATA', 'LOCALAPPDATA'];
                if (criticalKeys.includes(key)) {
                    console.log(`🔍 环境变量访问: ${key} = ${value}`);
                    
                    // 检查是否返回的是虚拟值
                    if (key === 'USERNAME' && value === identity.user.username) {
                        console.log('✅ 虚拟化成功: USERNAME 返回虚拟值');
                    } else if (key === 'USERPROFILE' && value === identity.user.profilePath) {
                        console.log('✅ 虚拟化成功: USERPROFILE 返回虚拟值');
                    }
                }
                
                return value;
            }
        });

        (process as any).env = envProxy;
    }

    private monitorVSCodeEnvironment(): void {
        const originalEnv = vscode.env;
        const identity = this.identityGenerator.getCurrentIdentity();
        
        try {
            // 注意：vscode.env 是只读的，无法直接替换
            // 改为监控访问模式，而不是替换整个对象
            console.log('🔍 VS Code环境监控已启用（只读模式）');
            console.log(`📋 监控目标 machineId: ${identity.device.machineId.substring(0, 8)}...`);
            console.log(`📋 监控目标 sessionId: ${identity.device.sessionId.substring(0, 8)}...`);
            
            // 可以添加定期检查逻辑，而不是直接替换对象
            setInterval(() => {
                const currentMachineId = vscode.env.machineId;
                if (currentMachineId === identity.device.machineId) {
                    console.log('✅ 虚拟化验证: machineId 匹配虚拟值');
                } else {
                    console.log('⚠️ 虚拟化验证: machineId 不匹配，可能需要重新激活');
                }
            }, 30000); // 每30秒检查一次
            
        } catch (error) {
            console.warn('⚠️ VS Code环境监控启动失败:', error);
        }
    }

    private monitorNetworkRequests(): void {
        const originalFetch = (global as any).fetch;
        
        (global as any).fetch = async (input: any, init?: any) => {
            const url = typeof input === 'string' ? input : input.url;
            
            // 监控AugmentCode相关的网络请求
            if (url.includes('augmentcode') || url.includes('api.augmentcode')) {
                console.log(`🔍 检测到AugmentCode网络请求: ${url}`);
                
                // 检查请求头中的虚拟信息
                if (init?.headers) {
                    const identity = this.identityGenerator.getCurrentIdentity();
                    
                    console.log('📋 请求头信息:');
                    console.log(`  X-Machine-ID: ${init.headers['X-Machine-ID']}`);
                    console.log(`  X-User-Name: ${init.headers['X-User-Name']}`);
                    console.log(`  X-GitHub-User: ${init.headers['X-GitHub-User']}`);
                    
                    // 验证是否使用了虚拟信息
                    if (init.headers['X-Machine-ID'] === identity.device.machineId) {
                        console.log('✅ 虚拟化成功: 网络请求使用虚拟设备ID');
                    }
                    if (init.headers['X-User-Name'] === identity.user.username) {
                        console.log('✅ 虚拟化成功: 网络请求使用虚拟用户名');
                    }
                    if (init.headers['X-GitHub-User'] === identity.github.login) {
                        console.log('✅ 虚拟化成功: 网络请求使用虚拟GitHub用户');
                    }
                }
            }
            
            return originalFetch(input, init);
        };
    }

    private monitorGitHubAuth(): void {
        const originalGetSession = vscode.authentication.getSession;
        
        (vscode.authentication as any).getSession = async (providerId: string, scopes: string[]) => {
            if (providerId === 'github') {
                console.log('🔍 检测到GitHub认证请求');
                
                const result = await originalGetSession(providerId, scopes);
                
                if (result) {
                    console.log('📋 GitHub认证信息:');
                    console.log(`  用户ID: ${result.account?.id}`);
                    console.log(`  用户名: ${result.account?.label}`);
                    console.log(`  Token: ${result.accessToken?.substring(0, 10)}...`);
                    
                    const identity = this.identityGenerator.getCurrentIdentity();
                    
                    // 验证是否返回虚拟信息
                    if (result.account?.label === identity.github.login) {
                        console.log('✅ 虚拟化成功: GitHub认证返回虚拟用户');
                    }
                }
                
                return result;
            }
            return originalGetSession(providerId, scopes);
        };
    }

    // 手动验证当前状态
    verifyVirtualization(): void {
        const identity = this.identityGenerator.getCurrentIdentity();
        
        this.outputChannel.appendLine('🔍 虚拟化验证报告');
        this.outputChannel.appendLine('=====================================');
        
        // 验证环境变量
        this.outputChannel.appendLine('🌍 环境变量验证:');
        this.outputChannel.appendLine(`  期望 USERNAME: ${identity.user.username}`);
        this.outputChannel.appendLine(`  实际 USERNAME: ${process.env.USERNAME}`);
        this.outputChannel.appendLine(`  状态: ${process.env.USERNAME === identity.user.username ? '✅ 虚拟化成功' : '❌ 虚拟化失败'}`);
        
        // 验证VS Code环境
        this.outputChannel.appendLine('🔧 VS Code环境验证:');
        this.outputChannel.appendLine(`  期望 machineId: ${identity.device.machineId.substring(0, 8)}...`);
        this.outputChannel.appendLine(`  实际 machineId: ${vscode.env.machineId?.substring(0, 8)}...`);
        this.outputChannel.appendLine(`  状态: ${vscode.env.machineId === identity.device.machineId ? '✅ 虚拟化成功' : '❌ 虚拟化失败'}`);
        
        // 验证工作区
        this.outputChannel.appendLine('📁 工作区验证:');
        this.outputChannel.appendLine(`  期望工作区: ${identity.workspace.workspaceName}`);
        this.outputChannel.appendLine(`  实际工作区: ${vscode.workspace.name || '未设置'}`);
        
        this.outputChannel.appendLine('');
        this.outputChannel.appendLine('💡 提示: 如果看到"虚拟化失败"，请检查扩展是否正确激活');
    }

    // 显示监控面板
    showMonitor(): void {
        this.outputChannel.show();
    }

    // 清除监控日志
    clearLogs(): void {
        this.outputChannel.clear();
        this.outputChannel.appendLine('🎭 Augment Spoofer Monitor 日志已清除');
        this.logCurrentIdentity();
    }

    getStatus(): string {
        if (!this.isActive) {
            return '❌ 未激活';
        }
        return '✅ 已激活 - 监控中';
    }
} 