import{S as z,i as T,s as I,a as g,n as B,d as w,b as C,g as F,u as P,v as j,w as q,x as D,f as J,H as K,j as H,y as ps,z as us,J as O,t as x,q as $,K as Q,L as V,M as G,$ as f,o as R,p as W,h as b,Q as es,c as _,e as L,ag as hs,F as k,V as is,a0 as M,I as ms,al as N,A as os,ao as fs,N as $s,aa as gs,D as X,ae as ws,E as Y,G as Z}from"./SpinnerAugment-VfHtkDdv.js";import{I as xs}from"./IconButtonAugment-BlRCK7lJ.js";function ys(n){let s,e,o=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},n[0]],l={};for(let t=0;t<o.length;t+=1)l=g(l,o[t]);return{c(){s=J("svg"),e=new K(!0),this.h()},l(t){s=j(t,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=q(s);e=D(c,!0),c.forEach(w),this.h()},h(){e.a=null,C(s,l)},m(t,c){P(t,s,c),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M248.4 84.3c1.6-2.7 4.5-4.3 7.6-4.3s6 1.6 7.6 4.3L461.9 410c1.4 2.3 2.1 4.9 2.1 7.5 0 8-6.5 14.5-14.5 14.5h-387c-8 0-14.5-6.5-14.5-14.5 0-2.7.7-5.3 2.1-7.5zm-41-25L9.1 385c-6 9.8-9.1 21-9.1 32.5C0 452 28 480 62.5 480h387c34.5 0 62.5-28 62.5-62.5 0-11.5-3.2-22.7-9.1-32.5L304.6 59.3C294.3 42.4 275.9 32 256 32s-38.3 10.4-48.6 27.3M288 368a32 32 0 1 0-64 0 32 32 0 1 0 64 0m-8-184c0-13.3-10.7-24-24-24s-24 10.7-24 24v96c0 13.3 10.7 24 24 24s24-10.7 24-24z"/>',s)},p(t,[c]){C(s,l=F(o,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&c&&t[0]]))},i:B,o:B,d(t){t&&w(s)}}}function vs(n,s,e){return n.$$set=o=>{e(0,s=g(g({},s),H(o)))},[s=H(s)]}class qs extends z{constructor(s){super(),T(this,s,vs,ys,I,{})}}function bs(n){let s,e,o=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},n[0]],l={};for(let t=0;t<o.length;t+=1)l=g(l,o[t]);return{c(){s=J("svg"),e=new K(!0),this.h()},l(t){s=j(t,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=q(s);e=D(c,!0),c.forEach(w),this.h()},h(){e.a=null,C(s,l)},m(t,c){P(t,s,c),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M239 498.7c-8.8 7-21.2 7-30 0l-160-128c-10.4-8.3-12-23.4-3.7-33.7s23.4-12 33.7-3.8l145 116 145-116c10.3-8.3 25.5-6.6 33.7 3.8s6.6 25.5-3.7 33.7zm160-357.4c10.4 8.3 12 23.4 3.8 33.7s-23.4 12-33.7 3.7L224 62.7l-145 116c-10.4 8.3-25.5 6.6-33.7-3.7s-6.6-25.5 3.7-33.7l160-128c8.8-7 21.2-7 30 0z"/>',s)},p(t,[c]){C(s,l=F(o,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&c&&t[0]]))},i:B,o:B,d(t){t&&w(s)}}}function ks(n,s,e){return n.$$set=o=>{e(0,s=g(g({},s),H(o)))},[s=H(s)]}class Hs extends z{constructor(s){super(),T(this,s,ks,bs,I,{})}}const rs=Symbol("collapsible");function Ss(){return ps(rs)}function _s(n,s){const{onStuck:e,onUnstuck:o,offset:l=0}=s,t=document.createElement("div");t.style.position="absolute",t.style.top=l?`${l}px`:"0",t.style.height="1px",t.style.width="100%",t.style.pointerEvents="none",t.style.opacity="0",t.style.zIndex="-1";const c=n.parentNode;if(!c)return{update:()=>{},destroy:()=>{}};window.getComputedStyle(c).position==="static"&&(c.style.position="relative"),c.insertBefore(t,n);const i=new IntersectionObserver(([a])=>{a.isIntersecting?o==null||o():e==null||e()},{threshold:0,rootMargin:"-1px 0px 0px 0px"});return i.observe(t),{update(a){s.onStuck=a.onStuck,s.onUnstuck=a.onUnstuck,a.offset!==void 0&&a.offset!==l&&(t.style.top=`${a.offset}px`)},destroy(){i.disconnect(),t.remove()}}}const Bs=n=>({}),ns=n=>({}),Cs=n=>({}),cs=n=>({});function ls(n){let s,e,o,l;const t=n[14].default,c=O(t,n,n[13],null);let i=n[9].footer&&as(n);return{c(){s=k("div"),c&&c.c(),e=is(),i&&i.c(),o=$s(),b(s,"class","c-collapsible__body svelte-gbhym3")},m(a,d){_(a,s,d),c&&c.m(s,null),_(a,e,d),i&&i.m(a,d),_(a,o,d),l=!0},p(a,d){c&&c.p&&(!l||8192&d)&&Q(c,t,a,a[13],l?G(t,a[13],d,null):V(a[13]),null),a[9].footer?i?(i.p(a,d),512&d&&$(i,1)):(i=as(a),i.c(),$(i,1),i.m(o.parentNode,o)):i&&(R(),x(i,1,1,()=>{i=null}),W())},i(a){l||($(c,a),$(i),l=!0)},o(a){x(c,a),x(i),l=!1},d(a){a&&(w(s),w(e),w(o)),c&&c.d(a),i&&i.d(a)}}}function as(n){let s,e;const o=n[14].footer,l=O(o,n,n[13],ns);return{c(){s=k("footer"),l&&l.c(),b(s,"class","c-collapsible__footer svelte-gbhym3")},m(t,c){_(t,s,c),l&&l.m(s,null),e=!0},p(t,c){l&&l.p&&(!e||8192&c)&&Q(l,o,t,t[13],e?G(o,t[13],c,Bs):V(t[13]),ns)},i(t){e||($(l,t),e=!0)},o(t){x(l,t),e=!1},d(t){t&&w(s),l&&l.d(t)}}}function Ls(n){let s,e,o,l,t,c,i,a,d,y,v;const S=n[14].header,m=O(S,n,n[13],cs);let u=n[4]&&n[5]&&ls(n);return{c(){s=k("div"),e=k("header"),o=k("div"),m&&m.c(),t=is(),c=k("div"),i=k("div"),u&&u.c(),b(o,"class","c-collapsible__header-inner svelte-gbhym3"),f(o,"is-collapsed",n[3]),f(o,"is-header-stuck",n[0]),f(o,"has-header-padding",n[2]>0),b(e,"class","c-collapsible__header svelte-gbhym3"),f(e,"is-sticky",n[1]),b(i,"class","c-collapsible__content-inner svelte-gbhym3"),b(c,"class","c-collapsible__content svelte-gbhym3"),f(c,"is-collapsed",n[3]),b(s,"class",a="c-collapsible "+n[6]+" svelte-gbhym3"),f(s,"is-collapsed",n[3]),f(s,"is-expandable",n[4]),es(s,"--sticky-header-top",`${n[2]}px`)},m(r,p){_(r,s,p),L(s,e),L(e,o),m&&m.m(o,null),L(s,t),L(s,c),L(c,i),u&&u.m(i,null),d=!0,y||(v=hs(l=_s.call(null,e,{offset:-n[2],onStuck:n[15],onUnstuck:n[16]})),y=!0)},p(r,[p]){m&&m.p&&(!d||8192&p)&&Q(m,S,r,r[13],d?G(S,r[13],p,Cs):V(r[13]),cs),(!d||8&p)&&f(o,"is-collapsed",r[3]),(!d||1&p)&&f(o,"is-header-stuck",r[0]),(!d||4&p)&&f(o,"has-header-padding",r[2]>0),l&&gs(l.update)&&5&p&&l.update.call(null,{offset:-r[2],onStuck:r[15],onUnstuck:r[16]}),(!d||2&p)&&f(e,"is-sticky",r[1]),r[4]&&r[5]?u?(u.p(r,p),48&p&&$(u,1)):(u=ls(r),u.c(),$(u,1),u.m(i,null)):u&&(R(),x(u,1,1,()=>{u=null}),W()),(!d||8&p)&&f(c,"is-collapsed",r[3]),(!d||64&p&&a!==(a="c-collapsible "+r[6]+" svelte-gbhym3"))&&b(s,"class",a),(!d||72&p)&&f(s,"is-collapsed",r[3]),(!d||80&p)&&f(s,"is-expandable",r[4]),4&p&&es(s,"--sticky-header-top",`${r[2]}px`)},i(r){d||($(m,r),$(u),d=!0)},o(r){x(m,r),x(u),d=!1},d(r){r&&w(s),m&&m.d(r),u&&u.d(),y=!1,v()}}}function zs(n,s,e){let o;const l=["collapsed","stickyHeader","expandable","isHeaderStuck","stickyHeaderTop","toggle"];let t,c,i=M(s,l),{$$slots:a={},$$scope:d}=s;const y=ms(a);let{collapsed:v=!1}=s,{stickyHeader:S=!1}=s,{expandable:m=!0}=s,{isHeaderStuck:u=!1}=s,{stickyHeaderTop:r=-.5}=s;const p=os(v);N(n,p,h=>e(3,t=h));const ds=fs(p,h=>h),E=os(m);N(n,E,h=>e(4,c=h));let U,A=!1;function ss(h){m?p.set(h):p.set(!0)}const ts=function(){ss(!t)};return us(rs,{collapsed:ds,setCollapsed:ss,toggle:ts,expandable:E}),n.$$set=h=>{s=g(g({},s),H(h)),e(22,i=M(s,l)),"collapsed"in h&&e(10,v=h.collapsed),"stickyHeader"in h&&e(1,S=h.stickyHeader),"expandable"in h&&e(11,m=h.expandable),"isHeaderStuck"in h&&e(0,u=h.isHeaderStuck),"stickyHeaderTop"in h&&e(2,r=h.stickyHeaderTop),"$$scope"in h&&e(13,d=h.$$scope)},n.$$.update=()=>{16&n.$$.dirty&&e(11,m=c),2048&n.$$.dirty&&E.set(m),8&n.$$.dirty&&e(10,v=t),1024&n.$$.dirty&&p.set(v),2048&n.$$.dirty&&(m||p.set(!0)),1024&n.$$.dirty&&(v?(clearTimeout(U),U=setTimeout(()=>{e(5,A=!1)},200)):(clearTimeout(U),e(5,A=!0))),e(6,{class:o}=i,o)},[u,S,r,t,c,A,o,p,E,y,v,m,ts,d,a,()=>{e(0,u=!0)},()=>{e(0,u=!1)}]}class Ds extends z{constructor(s){super(),T(this,s,zs,Ls,I,{collapsed:10,stickyHeader:1,expandable:11,isHeaderStuck:0,stickyHeaderTop:2,toggle:12})}get toggle(){return this.$$.ctx[12]}}function Ts(n){let s,e,o=[{xmlns:"http://www.w3.org/2000/svg"},{width:"8"},{height:"10"},{"data-ds-icon":"fa"},{viewBox:"0 0 8 10"},n[0]],l={};for(let t=0;t<o.length;t+=1)l=g(l,o[t]);return{c(){s=J("svg"),e=new K(!0),this.h()},l(t){s=j(t,"svg",{xmlns:!0,width:!0,height:!0,"data-ds-icon":!0,viewBox:!0});var c=q(s);e=D(c,!0),c.forEach(w),this.h()},h(){e.a=null,C(s,l)},m(t,c){P(t,s,c),e.m('<path d="M4.451 6.357a.42.42 0 0 0-.527 0L1.11 8.607a.42.42 0 0 0-.065.592.424.424 0 0 0 .593.067l2.548-2.04 2.55 2.04a.423.423 0 0 0 .527-.66zm2.813-4.965a.422.422 0 1 0-.526-.658l-2.55 2.04-2.55-2.04a.421.421 0 1 0-.527.658l2.813 2.25a.42.42 0 0 0 .527 0z"/>',s)},p(t,[c]){C(s,l=F(o,[{xmlns:"http://www.w3.org/2000/svg"},{width:"8"},{height:"10"},{"data-ds-icon":"fa"},{viewBox:"0 0 8 10"},1&c&&t[0]]))},i:B,o:B,d(t){t&&w(s)}}}function Is(n,s,e){return n.$$set=o=>{e(0,s=g(g({},s),H(o)))},[s=H(s)]}class Es extends z{constructor(s){super(),T(this,s,Is,Ts,I,{})}}function Ms(n){let s,e;return s=new Es({}),{c(){Z(s.$$.fragment)},m(o,l){Y(s,o,l),e=!0},i(o){e||($(s.$$.fragment,o),e=!0)},o(o){x(s.$$.fragment,o),e=!1},d(o){X(s,o)}}}function Fs(n){let s,e;return s=new Hs({}),{c(){Z(s.$$.fragment)},m(o,l){Y(s,o,l),e=!0},i(o){e||($(s.$$.fragment,o),e=!0)},o(o){x(s.$$.fragment,o),e=!1},d(o){X(s,o)}}}function Us(n){let s,e,o,l;const t=[Fs,Ms],c=[];function i(a,d){return a[0]?0:1}return e=i(n),o=c[e]=t[e](n),{c(){s=k("span"),o.c(),b(s,"class","c-collapse-button-augment__icon svelte-hw7s17")},m(a,d){_(a,s,d),c[e].m(s,null),l=!0},p(a,d){let y=e;e=i(a),e!==y&&(R(),x(c[y],1,1,()=>{c[y]=null}),W(),o=c[e],o||(o=c[e]=t[e](a),o.c()),$(o,1),o.m(s,null))},i(a){l||($(o),l=!0)},o(a){x(o),l=!1},d(a){a&&w(s),c[e].d()}}}function As(n){let s,e;const o=[{variant:"ghost-block"},{color:"neutral"},{size:1},n[3]];let l={$$slots:{default:[Us]},$$scope:{ctx:n}};for(let t=0;t<o.length;t+=1)l=g(l,o[t]);return s=new xs({props:l}),s.$on("click",n[2]),{c(){Z(s.$$.fragment)},m(t,c){Y(s,t,c),e=!0},p(t,[c]){const i=8&c?F(o,[o[0],o[1],o[2],ws(t[3])]):{};33&c&&(i.$$scope={dirty:c,ctx:t}),s.$set(i)},i(t){e||($(s.$$.fragment,t),e=!0)},o(t){x(s.$$.fragment,t),e=!1},d(t){X(s,t)}}}function Ns(n,s,e){const o=[];let l,t=M(s,o);const{collapsed:c,setCollapsed:i}=Ss();return N(n,c,a=>e(0,l=a)),n.$$set=a=>{s=g(g({},s),H(a)),e(3,t=M(s,o))},[l,c,function(){i(!l)},t]}class Js extends z{constructor(s){super(),T(this,s,Ns,As,I,{})}}export{Hs as A,Ds as C,qs as T,Js as a,Es as b,Ss as g};
