@echo off
chcp 65001 >nul
echo 🧹 AugmentCode Chrome数据清理工具
echo =====================================

:: 检查是否以管理员权限运行
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ 已获得管理员权限
) else (
    echo ⚠️  建议以管理员权限运行以获得最佳效果
)

:: 获取Chrome用户数据目录
set "CHROME_DATA_DIR=%LOCALAPPDATA%\Google\Chrome\User Data"
echo 📁 Chrome用户数据目录: %CHROME_DATA_DIR%

:: 检查目录是否存在
if not exist "%CHROME_DATA_DIR%" (
    echo ❌ Chrome用户数据目录不存在
    pause
    exit /b 1
)

echo.
echo 🔍 开始清理AugmentCode相关数据...
echo =====================================

:: 1. 清理Default Profile的缓存
echo 🗂️  清理Default Profile缓存...
if exist "%CHROME_DATA_DIR%\Default\Cache" (
    rmdir /s /q "%CHROME_DATA_DIR%\Default\Cache" 2>nul
    echo ✅ 已清理Default Profile缓存
) else (
    echo ℹ️  Default Profile缓存目录不存在
)

:: 2. 清理所有Profile的缓存
for /d %%i in ("%CHROME_DATA_DIR%\Profile *") do (
    if exist "%%i\Cache" (
        echo 🗂️  清理 %%i 缓存...
        rmdir /s /q "%%i\Cache" 2>nul
        echo ✅ 已清理 %%i 缓存
    )
)

:: 3. 清理Default Profile的Local Storage
echo 💾 清理Default Profile Local Storage...
if exist "%CHROME_DATA_DIR%\Default\Local Storage" (
    rmdir /s /q "%CHROME_DATA_DIR%\Default\Local Storage" 2>nul
    echo ✅ 已清理Default Profile Local Storage
) else (
    echo ℹ️  Default Profile Local Storage目录不存在
)

:: 4. 清理所有Profile的Local Storage
for /d %%i in ("%CHROME_DATA_DIR%\Profile *") do (
    if exist "%%i\Local Storage" (
        echo 💾 清理 %%i Local Storage...
        rmdir /s /q "%%i\Local Storage" 2>nul
        echo ✅ 已清理 %%i Local Storage
    )
)

:: 5. 清理Default Profile的Session Storage
echo 📝 清理Default Profile Session Storage...
if exist "%CHROME_DATA_DIR%\Default\Session Storage" (
    rmdir /s /q "%CHROME_DATA_DIR%\Default\Session Storage" 2>nul
    echo ✅ 已清理Default Profile Session Storage
) else (
    echo ℹ️  Default Profile Session Storage目录不存在
)

:: 6. 清理所有Profile的Session Storage
for /d %%i in ("%CHROME_DATA_DIR%\Profile *") do (
    if exist "%%i\Session Storage" (
        echo 📝 清理 %%i Session Storage...
        rmdir /s /q "%%i\Session Storage" 2>nul
        echo ✅ 已清理 %%i Session Storage
    )
)

:: 7. 清理Default Profile的IndexedDB
echo 🗄️  清理Default Profile IndexedDB...
if exist "%CHROME_DATA_DIR%\Default\IndexedDB" (
    rmdir /s /q "%CHROME_DATA_DIR%\Default\IndexedDB" 2>nul
    echo ✅ 已清理Default Profile IndexedDB
) else (
    echo ℹ️  Default Profile IndexedDB目录不存在
)

:: 8. 清理所有Profile的IndexedDB
for /d %%i in ("%CHROME_DATA_DIR%\Profile *") do (
    if exist "%%i\IndexedDB" (
        echo 🗄️  清理 %%i IndexedDB...
        rmdir /s /q "%%i\IndexedDB" 2>nul
        echo ✅ 已清理 %%i IndexedDB
    )
)

:: 9. 清理Default Profile的Cookies
echo 🍪 清理Default Profile Cookies...
if exist "%CHROME_DATA_DIR%\Default\Cookies" (
    del /f /q "%CHROME_DATA_DIR%\Default\Cookies" 2>nul
    echo ✅ 已清理Default Profile Cookies
) else (
    echo ℹ️  Default Profile Cookies文件不存在
)

:: 10. 清理所有Profile的Cookies
for /d %%i in ("%CHROME_DATA_DIR%\Profile *") do (
    if exist "%%i\Cookies" (
        echo 🍪 清理 %%i Cookies...
        del /f /q "%%i\Cookies" 2>nul
        echo ✅ 已清理 %%i Cookies
    )
)

:: 11. 清理Default Profile的Login Data
echo 🔐 清理Default Profile登录数据...
if exist "%CHROME_DATA_DIR%\Default\Login Data" (
    del /f /q "%CHROME_DATA_DIR%\Default\Login Data" 2>nul
    echo ✅ 已清理Default Profile登录数据
) else (
    echo ℹ️  Default Profile登录数据文件不存在
)

:: 12. 清理所有Profile的Login Data
for /d %%i in ("%CHROME_DATA_DIR%\Profile *") do (
    if exist "%%i\Login Data" (
        echo 🔐 清理 %%i 登录数据...
        del /f /q "%%i\Login Data" 2>nul
        echo ✅ 已清理 %%i 登录数据
    )
)

:: 13. 清理Default Profile的Web Data
echo 🌐 清理Default Profile Web数据...
if exist "%CHROME_DATA_DIR%\Default\Web Data" (
    del /f /q "%CHROME_DATA_DIR%\Default\Web Data" 2>nul
    echo ✅ 已清理Default Profile Web数据
) else (
    echo ℹ️  Default Profile Web数据文件不存在
)

:: 14. 清理所有Profile的Web Data
for /d %%i in ("%CHROME_DATA_DIR%\Profile *") do (
    if exist "%%i\Web Data" (
        echo 🌐 清理 %%i Web数据...
        del /f /q "%%i\Web Data" 2>nul
        echo ✅ 已清理 %%i Web数据
    )
)

:: 15. 清理Default Profile的Preferences文件中的AugmentCode相关设置
echo ⚙️  清理Default Profile偏好设置中的AugmentCode数据...
if exist "%CHROME_DATA_DIR%\Default\Preferences" (
    echo 正在清理Preferences文件中的AugmentCode数据...
    powershell -Command "(Get-Content '%CHROME_DATA_DIR%\Default\Preferences' -Raw) -replace '\"augmentcode[^\"]*\":[^,}]*[,}]?', '' | Set-Content '%CHROME_DATA_DIR%\Default\Preferences'"
    echo ✅ 已清理Default Profile偏好设置
) else (
    echo ℹ️  Default Profile偏好设置文件不存在
)

:: 16. 清理所有Profile的Preferences文件
for /d %%i in ("%CHROME_DATA_DIR%\Profile *") do (
    if exist "%%i\Preferences" (
        echo ⚙️  清理 %%i 偏好设置...
        powershell -Command "(Get-Content '%%i\Preferences' -Raw) -replace '\"augmentcode[^\"]*\":[^,}]*[,}]?', '' | Set-Content '%%i\Preferences'"
        echo ✅ 已清理 %%i 偏好设置
    )
)

:: 17. 清理Service Workers
echo ⚙️  清理Service Workers...
if exist "%CHROME_DATA_DIR%\Default\Service Worker" (
    rmdir /s /q "%CHROME_DATA_DIR%\Default\Service Worker" 2>nul
    echo ✅ 已清理Default Profile Service Workers
) else (
    echo ℹ️  Default Profile Service Workers目录不存在
)

:: 18. 清理所有Profile的Service Workers
for /d %%i in ("%CHROME_DATA_DIR%\Profile *") do (
    if exist "%%i\Service Worker" (
        echo ⚙️  清理 %%i Service Workers...
        rmdir /s /q "%%i\Service Worker" 2>nul
        echo ✅ 已清理 %%i Service Workers
    )
)

echo.
echo =====================================
echo 🎉 Chrome数据清理完成！
echo.
echo 💡 建议操作：
echo    1. 关闭所有Chrome窗口
echo    2. 重新启动Chrome
echo    3. 使用无痕模式访问AugmentCode
echo    4. 运行浏览器指纹虚拟化脚本
echo.
echo ⚠️  注意：此操作会清除所有Chrome的缓存和登录数据
echo     请确保已备份重要信息
echo.

pause 