var hn=Object.defineProperty;var rt=u=>{throw TypeError(u)};var Dn=(u,e,t)=>e in u?hn(u,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):u[e]=t;var R=(u,e,t)=>Dn(u,typeof e!="symbol"?e+"":e,t),dn=(u,e,t)=>e.has(u)||rt("Cannot "+t);var st=(u,e,t)=>e.has(u)?rt("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(u):e.set(u,t);var me=(u,e,t)=>(dn(u,e,"access private method"),t);import{S as v,i as B,s as b,d as C,t as $,q as k,c as E,N as Y,J as O,a as pe,K as j,L as N,M as q,a9 as Ae,g as We,F as w,o as G,p as K,a7 as he,D as U,E as J,G as X,ay as ut,n as T,X as de,Y as te,h as m,e as L,V as se,az as fn,ax as ot,ah as gn,a3 as Ve,a4 as Fn,P as kn,a5 as mn,R as it,al as $n,U as xn,f as De,b as lt,u as Cn,v as En,w as An,x as wn,H as bn,j as at}from"./SpinnerAugment-VfHtkDdv.js";import{e as Q}from"./IconButtonAugment-BlRCK7lJ.js";import"./toggleHighContrast-Cb9MCs64.js";import{S as vn,g as Bn}from"./SimpleMonaco-JNVBjzXL.js";import{a as yn}from"./index-BsnNYDaF.js";function ct(...u){return"/"+u.flatMap(e=>e.split("/")).filter(e=>!!e).join("/")}function pt(u){return u.startsWith("/")||u.startsWith("#")}function Ie(u){let e,t;const n=u[5].default,r=O(n,u,u[4],null);let s=[{id:u[1]}],o={};for(let i=0;i<s.length;i+=1)o=pe(o,s[i]);return{c(){e=w(`h${u[0].depth}`),r&&r.c(),Ae(`h${u[0].depth}`)(e,o)},m(i,l){E(i,e,l),r&&r.m(e,null),t=!0},p(i,l){r&&r.p&&(!t||16&l)&&j(r,n,i,i[4],t?q(n,i[4],l,null):N(i[4]),null),Ae(`h${i[0].depth}`)(e,o=We(s,[(!t||2&l)&&{id:i[1]}]))},i(i){t||(k(r,i),t=!0)},o(i){$(r,i),t=!1},d(i){i&&C(e),r&&r.d(i)}}}function zn(u){let e,t,n=`h${u[0].depth}`,r=`h${u[0].depth}`&&Ie(u);return{c(){r&&r.c(),e=Y()},m(s,o){r&&r.m(s,o),E(s,e,o),t=!0},p(s,[o]){`h${s[0].depth}`?n?b(n,`h${s[0].depth}`)?(r.d(1),r=Ie(s),n=`h${s[0].depth}`,r.c(),r.m(e.parentNode,e)):r.p(s,o):(r=Ie(s),n=`h${s[0].depth}`,r.c(),r.m(e.parentNode,e)):n&&(r.d(1),r=null,n=`h${s[0].depth}`)},i(s){t||(k(r,s),t=!0)},o(s){$(r,s),t=!1},d(s){s&&C(e),r&&r.d(s)}}}function Sn(u,e,t){let{$$slots:n={},$$scope:r}=e,{token:s}=e,{options:o}=e,i;return u.$$set=l=>{"token"in l&&t(0,s=l.token),"options"in l&&t(2,o=l.options),"$$scope"in l&&t(4,r=l.$$scope)},u.$$.update=()=>{var l,a;5&u.$$.dirty&&t(1,(l=s.text,a=o.slugger,i=a.slug(l).replace(/--+/g,"-")))},[s,i,o,void 0,r,n]}class Tn extends v{constructor(e){super(),B(this,e,Sn,zn,b,{token:0,options:2,renderers:3})}get renderers(){return this.$$.ctx[3]}}function Rn(u){let e,t;const n=u[4].default,r=O(n,u,u[3],null);return{c(){e=w("blockquote"),r&&r.c()},m(s,o){E(s,e,o),r&&r.m(e,null),t=!0},p(s,[o]){r&&r.p&&(!t||8&o)&&j(r,n,s,s[3],t?q(n,s[3],o,null):N(s[3]),null)},i(s){t||(k(r,s),t=!0)},o(s){$(r,s),t=!1},d(s){s&&C(e),r&&r.d(s)}}}function In(u,e,t){let{$$slots:n={},$$scope:r}=e;return u.$$set=s=>{"$$scope"in s&&t(3,r=s.$$scope)},[void 0,void 0,void 0,r,n]}class Ln extends v{constructor(e){super(),B(this,e,In,Rn,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function ht(u,e,t){const n=u.slice();return n[3]=e[t],n}function Dt(u){let e,t,n=Q(u[0]),r=[];for(let o=0;o<n.length;o+=1)r[o]=dt(ht(u,n,o));const s=o=>$(r[o],1,1,()=>{r[o]=null});return{c(){for(let o=0;o<r.length;o+=1)r[o].c();e=Y()},m(o,i){for(let l=0;l<r.length;l+=1)r[l]&&r[l].m(o,i);E(o,e,i),t=!0},p(o,i){if(7&i){let l;for(n=Q(o[0]),l=0;l<n.length;l+=1){const a=ht(o,n,l);r[l]?(r[l].p(a,i),k(r[l],1)):(r[l]=dt(a),r[l].c(),k(r[l],1),r[l].m(e.parentNode,e))}for(G(),l=n.length;l<r.length;l+=1)s(l);K()}},i(o){if(!t){for(let i=0;i<n.length;i+=1)k(r[i]);t=!0}},o(o){r=r.filter(Boolean);for(let i=0;i<r.length;i+=1)$(r[i]);t=!1},d(o){o&&C(e),he(r,o)}}}function dt(u){let e,t;return e=new en({props:{token:u[3],renderers:u[1],options:u[2]}}),{c(){X(e.$$.fragment)},m(n,r){J(e,n,r),t=!0},p(n,r){const s={};1&r&&(s.token=n[3]),2&r&&(s.renderers=n[1]),4&r&&(s.options=n[2]),e.$set(s)},i(n){t||(k(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){U(e,n)}}}function Pn(u){let e,t,n=u[0]&&Dt(u);return{c(){n&&n.c(),e=Y()},m(r,s){n&&n.m(r,s),E(r,e,s),t=!0},p(r,[s]){r[0]?n?(n.p(r,s),1&s&&k(n,1)):(n=Dt(r),n.c(),k(n,1),n.m(e.parentNode,e)):n&&(G(),$(n,1,1,()=>{n=null}),K())},i(r){t||(k(n),t=!0)},o(r){$(n),t=!1},d(r){r&&C(e),n&&n.d(r)}}}function _n(u,e,t){let{tokens:n}=e,{renderers:r}=e,{options:s}=e;return u.$$set=o=>{"tokens"in o&&t(0,n=o.tokens),"renderers"in o&&t(1,r=o.renderers),"options"in o&&t(2,s=o.options)},[n,r,s]}class Se extends v{constructor(e){super(),B(this,e,_n,Pn,b,{tokens:0,renderers:1,options:2})}}function ft(u){let e,t,n;var r=u[1][u[0].type];function s(o,i){return{props:{token:o[0],options:o[2],renderers:o[1],$$slots:{default:[Nn]},$$scope:{ctx:o}}}}return r&&(e=ut(r,s(u))),{c(){e&&X(e.$$.fragment),t=Y()},m(o,i){e&&J(e,o,i),E(o,t,i),n=!0},p(o,i){if(3&i&&r!==(r=o[1][o[0].type])){if(e){G();const l=e;$(l.$$.fragment,1,0,()=>{U(l,1)}),K()}r?(e=ut(r,s(o)),X(e.$$.fragment),k(e.$$.fragment,1),J(e,t.parentNode,t)):e=null}else if(r){const l={};1&i&&(l.token=o[0]),4&i&&(l.options=o[2]),2&i&&(l.renderers=o[1]),15&i&&(l.$$scope={dirty:i,ctx:o}),e.$set(l)}},i(o){n||(e&&k(e.$$.fragment,o),n=!0)},o(o){e&&$(e.$$.fragment,o),n=!1},d(o){o&&C(t),e&&U(e,o)}}}function On(u){let e,t=u[0].raw+"";return{c(){e=te(t)},m(n,r){E(n,e,r)},p(n,r){1&r&&t!==(t=n[0].raw+"")&&de(e,t)},i:T,o:T,d(n){n&&C(e)}}}function jn(u){let e,t;return e=new Se({props:{tokens:u[0].tokens,renderers:u[1],options:u[2]}}),{c(){X(e.$$.fragment)},m(n,r){J(e,n,r),t=!0},p(n,r){const s={};1&r&&(s.tokens=n[0].tokens),2&r&&(s.renderers=n[1]),4&r&&(s.options=n[2]),e.$set(s)},i(n){t||(k(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){U(e,n)}}}function Nn(u){let e,t,n,r;const s=[jn,On],o=[];function i(l,a){return"tokens"in l[0]&&l[0].tokens?0:1}return e=i(u),t=o[e]=s[e](u),{c(){t.c(),n=Y()},m(l,a){o[e].m(l,a),E(l,n,a),r=!0},p(l,a){let c=e;e=i(l),e===c?o[e].p(l,a):(G(),$(o[c],1,1,()=>{o[c]=null}),K(),t=o[e],t?t.p(l,a):(t=o[e]=s[e](l),t.c()),k(t,1),t.m(n.parentNode,n))},i(l){r||(k(t),r=!0)},o(l){$(t),r=!1},d(l){l&&C(n),o[e].d(l)}}}function qn(u){let e,t,n=u[1][u[0].type]&&ft(u);return{c(){n&&n.c(),e=Y()},m(r,s){n&&n.m(r,s),E(r,e,s),t=!0},p(r,[s]){r[1][r[0].type]?n?(n.p(r,s),3&s&&k(n,1)):(n=ft(r),n.c(),k(n,1),n.m(e.parentNode,e)):n&&(G(),$(n,1,1,()=>{n=null}),K())},i(r){t||(k(n),t=!0)},o(r){$(n),t=!1},d(r){r&&C(e),n&&n.d(r)}}}function Zn(u,e,t){let{token:n}=e,{renderers:r}=e,{options:s}=e;return u.$$set=o=>{"token"in o&&t(0,n=o.token),"renderers"in o&&t(1,r=o.renderers),"options"in o&&t(2,s=o.options)},[n,r,s]}class en extends v{constructor(e){super(),B(this,e,Zn,qn,b,{token:0,renderers:1,options:2})}}function gt(u,e,t){const n=u.slice();return n[4]=e[t],n}function Ft(u){let e,t;return e=new en({props:{token:{...u[4]},options:u[1],renderers:u[2]}}),{c(){X(e.$$.fragment)},m(n,r){J(e,n,r),t=!0},p(n,r){const s={};1&r&&(s.token={...n[4]}),2&r&&(s.options=n[1]),4&r&&(s.renderers=n[2]),e.$set(s)},i(n){t||(k(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){U(e,n)}}}function Le(u){let e,t,n,r=Q(u[0].items),s=[];for(let a=0;a<r.length;a+=1)s[a]=Ft(gt(u,r,a));const o=a=>$(s[a],1,1,()=>{s[a]=null});let i=[{start:t=u[0].start||1}],l={};for(let a=0;a<i.length;a+=1)l=pe(l,i[a]);return{c(){e=w(u[3]);for(let a=0;a<s.length;a+=1)s[a].c();Ae(u[3])(e,l)},m(a,c){E(a,e,c);for(let p=0;p<s.length;p+=1)s[p]&&s[p].m(e,null);n=!0},p(a,c){if(7&c){let p;for(r=Q(a[0].items),p=0;p<r.length;p+=1){const D=gt(a,r,p);s[p]?(s[p].p(D,c),k(s[p],1)):(s[p]=Ft(D),s[p].c(),k(s[p],1),s[p].m(e,null))}for(G(),p=r.length;p<s.length;p+=1)o(p);K()}Ae(a[3])(e,l=We(i,[(!n||1&c&&t!==(t=a[0].start||1))&&{start:t}]))},i(a){if(!n){for(let c=0;c<r.length;c+=1)k(s[c]);n=!0}},o(a){s=s.filter(Boolean);for(let c=0;c<s.length;c+=1)$(s[c]);n=!1},d(a){a&&C(e),he(s,a)}}}function Mn(u){let e,t=u[3],n=u[3]&&Le(u);return{c(){n&&n.c(),e=Y()},m(r,s){n&&n.m(r,s),E(r,e,s)},p(r,[s]){r[3]?t?b(t,r[3])?(n.d(1),n=Le(r),t=r[3],n.c(),n.m(e.parentNode,e)):n.p(r,s):(n=Le(r),t=r[3],n.c(),n.m(e.parentNode,e)):t&&(n.d(1),n=null,t=r[3])},i:T,o(r){$(n,r)},d(r){r&&C(e),n&&n.d(r)}}}function Hn(u,e,t){let n,{token:r}=e,{options:s}=e,{renderers:o}=e;return u.$$set=i=>{"token"in i&&t(0,r=i.token),"options"in i&&t(1,s=i.options),"renderers"in i&&t(2,o=i.renderers)},u.$$.update=()=>{1&u.$$.dirty&&t(3,n=r.ordered?"ol":"ul")},[r,s,o,n]}class Qn extends v{constructor(e){super(),B(this,e,Hn,Mn,b,{token:0,options:1,renderers:2})}}function Wn(u){let e,t;const n=u[4].default,r=O(n,u,u[3],null);return{c(){e=w("li"),r&&r.c()},m(s,o){E(s,e,o),r&&r.m(e,null),t=!0},p(s,[o]){r&&r.p&&(!t||8&o)&&j(r,n,s,s[3],t?q(n,s[3],o,null):N(s[3]),null)},i(s){t||(k(r,s),t=!0)},o(s){$(r,s),t=!1},d(s){s&&C(e),r&&r.d(s)}}}function Vn(u,e,t){let{$$slots:n={},$$scope:r}=e;return u.$$set=s=>{"$$scope"in s&&t(3,r=s.$$scope)},[void 0,void 0,void 0,r,n]}class Un extends v{constructor(e){super(),B(this,e,Vn,Wn,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Jn(u){let e;return{c(){e=w("br")},m(t,n){E(t,e,n)},p:T,i:T,o:T,d(t){t&&C(e)}}}function Xn(u,e,t){return[void 0,void 0,void 0]}class Gn extends v{constructor(e){super(),B(this,e,Xn,Jn,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Kn(u){let e,t,n,r,s=u[0].text+"";return{c(){e=w("pre"),t=w("code"),n=te(s),m(t,"class",r=`lang-${u[0].lang}`)},m(o,i){E(o,e,i),L(e,t),L(t,n)},p(o,[i]){1&i&&s!==(s=o[0].text+"")&&de(n,s),1&i&&r!==(r=`lang-${o[0].lang}`)&&m(t,"class",r)},i:T,o:T,d(o){o&&C(e)}}}function Yn(u,e,t){let{token:n}=e;return u.$$set=r=>{"token"in r&&t(0,n=r.token)},[n,void 0,void 0]}class er extends v{constructor(e){super(),B(this,e,Yn,Kn,b,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function tr(u){let e,t,n=u[0].raw.slice(1,u[0].raw.length-1)+"";return{c(){e=w("code"),t=te(n)},m(r,s){E(r,e,s),L(e,t)},p(r,[s]){1&s&&n!==(n=r[0].raw.slice(1,r[0].raw.length-1)+"")&&de(t,n)},i:T,o:T,d(r){r&&C(e)}}}function nr(u,e,t){let{token:n}=e;return u.$$set=r=>{"token"in r&&t(0,n=r.token)},[n,void 0,void 0]}class rr extends v{constructor(e){super(),B(this,e,nr,tr,b,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function kt(u,e,t){const n=u.slice();return n[3]=e[t],n}function mt(u,e,t){const n=u.slice();return n[6]=e[t],n}function $t(u,e,t){const n=u.slice();return n[9]=e[t],n}function xt(u){let e,t,n,r;return t=new Se({props:{tokens:u[9].tokens,options:u[1],renderers:u[2]}}),{c(){e=w("th"),X(t.$$.fragment),n=se(),m(e,"scope","col")},m(s,o){E(s,e,o),J(t,e,null),L(e,n),r=!0},p(s,o){const i={};1&o&&(i.tokens=s[9].tokens),2&o&&(i.options=s[1]),4&o&&(i.renderers=s[2]),t.$set(i)},i(s){r||(k(t.$$.fragment,s),r=!0)},o(s){$(t.$$.fragment,s),r=!1},d(s){s&&C(e),U(t)}}}function Ct(u){let e,t,n;return t=new Se({props:{tokens:u[6].tokens,options:u[1],renderers:u[2]}}),{c(){e=w("td"),X(t.$$.fragment)},m(r,s){E(r,e,s),J(t,e,null),n=!0},p(r,s){const o={};1&s&&(o.tokens=r[6].tokens),2&s&&(o.options=r[1]),4&s&&(o.renderers=r[2]),t.$set(o)},i(r){n||(k(t.$$.fragment,r),n=!0)},o(r){$(t.$$.fragment,r),n=!1},d(r){r&&C(e),U(t)}}}function Et(u){let e,t,n,r=Q(u[3]),s=[];for(let i=0;i<r.length;i+=1)s[i]=Ct(mt(u,r,i));const o=i=>$(s[i],1,1,()=>{s[i]=null});return{c(){e=w("tr");for(let i=0;i<s.length;i+=1)s[i].c();t=se()},m(i,l){E(i,e,l);for(let a=0;a<s.length;a+=1)s[a]&&s[a].m(e,null);L(e,t),n=!0},p(i,l){if(7&l){let a;for(r=Q(i[3]),a=0;a<r.length;a+=1){const c=mt(i,r,a);s[a]?(s[a].p(c,l),k(s[a],1)):(s[a]=Ct(c),s[a].c(),k(s[a],1),s[a].m(e,t))}for(G(),a=r.length;a<s.length;a+=1)o(a);K()}},i(i){if(!n){for(let l=0;l<r.length;l+=1)k(s[l]);n=!0}},o(i){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)$(s[l]);n=!1},d(i){i&&C(e),he(s,i)}}}function sr(u){let e,t,n,r,s,o,i=Q(u[0].header),l=[];for(let h=0;h<i.length;h+=1)l[h]=xt($t(u,i,h));const a=h=>$(l[h],1,1,()=>{l[h]=null});let c=Q(u[0].rows),p=[];for(let h=0;h<c.length;h+=1)p[h]=Et(kt(u,c,h));const D=h=>$(p[h],1,1,()=>{p[h]=null});return{c(){e=w("table"),t=w("thead"),n=w("tr");for(let h=0;h<l.length;h+=1)l[h].c();r=se(),s=w("tbody");for(let h=0;h<p.length;h+=1)p[h].c()},m(h,f){E(h,e,f),L(e,t),L(t,n);for(let d=0;d<l.length;d+=1)l[d]&&l[d].m(n,null);L(e,r),L(e,s);for(let d=0;d<p.length;d+=1)p[d]&&p[d].m(s,null);o=!0},p(h,[f]){if(7&f){let d;for(i=Q(h[0].header),d=0;d<i.length;d+=1){const x=$t(h,i,d);l[d]?(l[d].p(x,f),k(l[d],1)):(l[d]=xt(x),l[d].c(),k(l[d],1),l[d].m(n,null))}for(G(),d=i.length;d<l.length;d+=1)a(d);K()}if(7&f){let d;for(c=Q(h[0].rows),d=0;d<c.length;d+=1){const x=kt(h,c,d);p[d]?(p[d].p(x,f),k(p[d],1)):(p[d]=Et(x),p[d].c(),k(p[d],1),p[d].m(s,null))}for(G(),d=c.length;d<p.length;d+=1)D(d);K()}},i(h){if(!o){for(let f=0;f<i.length;f+=1)k(l[f]);for(let f=0;f<c.length;f+=1)k(p[f]);o=!0}},o(h){l=l.filter(Boolean);for(let f=0;f<l.length;f+=1)$(l[f]);p=p.filter(Boolean);for(let f=0;f<p.length;f+=1)$(p[f]);o=!1},d(h){h&&C(e),he(l,h),he(p,h)}}}function ur(u,e,t){let{token:n}=e,{options:r}=e,{renderers:s}=e;return u.$$set=o=>{"token"in o&&t(0,n=o.token),"options"in o&&t(1,r=o.options),"renderers"in o&&t(2,s=o.renderers)},[n,r,s]}class or extends v{constructor(e){super(),B(this,e,ur,sr,b,{token:0,options:1,renderers:2})}}function ir(u){let e,t,n=u[0].text+"";return{c(){e=new fn(!1),t=Y(),e.a=t},m(r,s){e.m(n,r,s),E(r,t,s)},p(r,[s]){1&s&&n!==(n=r[0].text+"")&&e.p(n)},i:T,o:T,d(r){r&&(C(t),e.d())}}}function lr(u,e,t){let{token:n}=e;return u.$$set=r=>{"token"in r&&t(0,n=r.token)},[n,void 0,void 0]}class ar extends v{constructor(e){super(),B(this,e,lr,ir,b,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function cr(u){let e,t;const n=u[4].default,r=O(n,u,u[3],null);return{c(){e=w("p"),r&&r.c()},m(s,o){E(s,e,o),r&&r.m(e,null),t=!0},p(s,[o]){r&&r.p&&(!t||8&o)&&j(r,n,s,s[3],t?q(n,s[3],o,null):N(s[3]),null)},i(s){t||(k(r,s),t=!0)},o(s){$(r,s),t=!1},d(s){s&&C(e),r&&r.d(s)}}}function pr(u,e,t){let{$$slots:n={},$$scope:r}=e;return u.$$set=s=>{"$$scope"in s&&t(3,r=s.$$scope)},[void 0,void 0,void 0,r,n]}let hr=class extends v{constructor(u){super(),B(this,u,pr,cr,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}};function Dr(u){let e,t,n,r;const s=u[4].default,o=O(s,u,u[3],null);return{c(){e=w("a"),o&&o.c(),m(e,"href",t=pt(u[0].href)?ct(u[1].baseUrl,u[0].href):u[0].href),m(e,"title",n=u[0].title)},m(i,l){E(i,e,l),o&&o.m(e,null),r=!0},p(i,[l]){o&&o.p&&(!r||8&l)&&j(o,s,i,i[3],r?q(s,i[3],l,null):N(i[3]),null),(!r||3&l&&t!==(t=pt(i[0].href)?ct(i[1].baseUrl,i[0].href):i[0].href))&&m(e,"href",t),(!r||1&l&&n!==(n=i[0].title))&&m(e,"title",n)},i(i){r||(k(o,i),r=!0)},o(i){$(o,i),r=!1},d(i){i&&C(e),o&&o.d(i)}}}function dr(u,e,t){let{$$slots:n={},$$scope:r}=e,{token:s}=e,{options:o}=e;return u.$$set=i=>{"token"in i&&t(0,s=i.token),"options"in i&&t(1,o=i.options),"$$scope"in i&&t(3,r=i.$$scope)},[s,o,void 0,r,n]}class fr extends v{constructor(e){super(),B(this,e,dr,Dr,b,{token:0,options:1,renderers:2})}get renderers(){return this.$$.ctx[2]}}function gr(u){let e;const t=u[4].default,n=O(t,u,u[3],null);return{c(){n&&n.c()},m(r,s){n&&n.m(r,s),e=!0},p(r,[s]){n&&n.p&&(!e||8&s)&&j(n,t,r,r[3],e?q(t,r[3],s,null):N(r[3]),null)},i(r){e||(k(n,r),e=!0)},o(r){$(n,r),e=!1},d(r){n&&n.d(r)}}}function Fr(u,e,t){let{$$slots:n={},$$scope:r}=e;return u.$$set=s=>{"$$scope"in s&&t(3,r=s.$$scope)},[void 0,void 0,void 0,r,n]}class kr extends v{constructor(e){super(),B(this,e,Fr,gr,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function mr(u){let e,t;const n=u[4].default,r=O(n,u,u[3],null);return{c(){e=w("dfn"),r&&r.c()},m(s,o){E(s,e,o),r&&r.m(e,null),t=!0},p(s,[o]){r&&r.p&&(!t||8&o)&&j(r,n,s,s[3],t?q(n,s[3],o,null):N(s[3]),null)},i(s){t||(k(r,s),t=!0)},o(s){$(r,s),t=!1},d(s){s&&C(e),r&&r.d(s)}}}function $r(u,e,t){let{$$slots:n={},$$scope:r}=e;return u.$$set=s=>{"$$scope"in s&&t(3,r=s.$$scope)},[void 0,void 0,void 0,r,n]}class xr extends v{constructor(e){super(),B(this,e,$r,mr,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Cr(u){let e,t;const n=u[4].default,r=O(n,u,u[3],null);return{c(){e=w("del"),r&&r.c()},m(s,o){E(s,e,o),r&&r.m(e,null),t=!0},p(s,[o]){r&&r.p&&(!t||8&o)&&j(r,n,s,s[3],t?q(n,s[3],o,null):N(s[3]),null)},i(s){t||(k(r,s),t=!0)},o(s){$(r,s),t=!1},d(s){s&&C(e),r&&r.d(s)}}}function Er(u,e,t){let{$$slots:n={},$$scope:r}=e;return u.$$set=s=>{"$$scope"in s&&t(3,r=s.$$scope)},[void 0,void 0,void 0,r,n]}class Ar extends v{constructor(e){super(),B(this,e,Er,Cr,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function wr(u){let e,t;const n=u[4].default,r=O(n,u,u[3],null);return{c(){e=w("em"),r&&r.c()},m(s,o){E(s,e,o),r&&r.m(e,null),t=!0},p(s,[o]){r&&r.p&&(!t||8&o)&&j(r,n,s,s[3],t?q(n,s[3],o,null):N(s[3]),null)},i(s){t||(k(r,s),t=!0)},o(s){$(r,s),t=!1},d(s){s&&C(e),r&&r.d(s)}}}function br(u,e,t){let{$$slots:n={},$$scope:r}=e;return u.$$set=s=>{"$$scope"in s&&t(3,r=s.$$scope)},[void 0,void 0,void 0,r,n]}class vr extends v{constructor(e){super(),B(this,e,br,wr,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Br(u){let e;return{c(){e=w("hr")},m(t,n){E(t,e,n)},p:T,i:T,o:T,d(t){t&&C(e)}}}function yr(u,e,t){return[void 0,void 0,void 0]}class zr extends v{constructor(e){super(),B(this,e,yr,Br,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Sr(u){let e,t;const n=u[4].default,r=O(n,u,u[3],null);return{c(){e=w("strong"),r&&r.c()},m(s,o){E(s,e,o),r&&r.m(e,null),t=!0},p(s,[o]){r&&r.p&&(!t||8&o)&&j(r,n,s,s[3],t?q(n,s[3],o,null):N(s[3]),null)},i(s){t||(k(r,s),t=!0)},o(s){$(r,s),t=!1},d(s){s&&C(e),r&&r.d(s)}}}function Tr(u,e,t){let{$$slots:n={},$$scope:r}=e;return u.$$set=s=>{"$$scope"in s&&t(3,r=s.$$scope)},[void 0,void 0,void 0,r,n]}class Rr extends v{constructor(e){super(),B(this,e,Tr,Sr,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Ir(u){let e,t,n,r;return{c(){e=w("img"),ot(e.src,t=u[0].href)||m(e,"src",t),m(e,"title",n=u[0].title),m(e,"alt",r=u[0].text),m(e,"class","markdown-image svelte-z38cge")},m(s,o){E(s,e,o)},p(s,[o]){1&o&&!ot(e.src,t=s[0].href)&&m(e,"src",t),1&o&&n!==(n=s[0].title)&&m(e,"title",n),1&o&&r!==(r=s[0].text)&&m(e,"alt",r)},i:T,o:T,d(s){s&&C(e)}}}function Lr(u,e,t){let{token:n}=e;return u.$$set=r=>{"token"in r&&t(0,n=r.token)},[n,void 0,void 0]}class Pr extends v{constructor(e){super(),B(this,e,Lr,Ir,b,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function _r(u){let e;const t=u[4].default,n=O(t,u,u[3],null);return{c(){n&&n.c()},m(r,s){n&&n.m(r,s),e=!0},p(r,[s]){n&&n.p&&(!e||8&s)&&j(n,t,r,r[3],e?q(t,r[3],s,null):N(r[3]),null)},i(r){e||(k(n,r),e=!0)},o(r){$(n,r),e=!1},d(r){n&&n.d(r)}}}function Or(u,e,t){let{$$slots:n={},$$scope:r}=e;return u.$$set=s=>{"$$scope"in s&&t(3,r=s.$$scope)},[void 0,void 0,void 0,r,n]}class At extends v{constructor(e){super(),B(this,e,Or,_r,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function jr(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let re={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};function wt(u){re=u}const tn=/[&<>"']/,Nr=new RegExp(tn.source,"g"),nn=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,qr=new RegExp(nn.source,"g"),Zr={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},bt=u=>Zr[u];function M(u,e){if(e){if(tn.test(u))return u.replace(Nr,bt)}else if(nn.test(u))return u.replace(qr,bt);return u}const Mr=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function Hr(u){return u.replace(Mr,(e,t)=>(t=t.toLowerCase())==="colon"?":":t.charAt(0)==="#"?t.charAt(1)==="x"?String.fromCharCode(parseInt(t.substring(2),16)):String.fromCharCode(+t.substring(1)):"")}const Qr=/(^|[^\[])\^/g;function S(u,e){let t=typeof u=="string"?u:u.source;e=e||"";const n={replace:(r,s)=>{let o=typeof s=="string"?s:s.source;return o=o.replace(Qr,"$1"),t=t.replace(r,o),n},getRegex:()=>new RegExp(t,e)};return n}function vt(u){try{u=encodeURI(u).replace(/%25/g,"%")}catch{return null}return u}const ae={exec:()=>null};function Bt(u,e){const t=u.replace(/\|/g,(r,s,o)=>{let i=!1,l=s;for(;--l>=0&&o[l]==="\\";)i=!i;return i?"|":" |"}).split(/ \|/);let n=0;if(t[0].trim()||t.shift(),t.length>0&&!t[t.length-1].trim()&&t.pop(),e)if(t.length>e)t.splice(e);else for(;t.length<e;)t.push("");for(;n<t.length;n++)t[n]=t[n].trim().replace(/\\\|/g,"|");return t}function $e(u,e,t){const n=u.length;if(n===0)return"";let r=0;for(;r<n&&u.charAt(n-r-1)===e;)r++;return u.slice(0,n-r)}function yt(u,e,t,n){const r=e.href,s=e.title?M(e.title):null,o=u[1].replace(/\\([\[\]])/g,"$1");if(u[0].charAt(0)!=="!"){n.state.inLink=!0;const i={type:"link",raw:t,href:r,title:s,text:o,tokens:n.inlineTokens(o)};return n.state.inLink=!1,i}return{type:"image",raw:t,href:r,title:s,text:M(o)}}class we{constructor(e){R(this,"options");R(this,"rules");R(this,"lexer");this.options=e||re}space(e){const t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){const t=this.rules.block.code.exec(e);if(t){const n=t[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?n:$e(n,`
`)}}}fences(e){const t=this.rules.block.fences.exec(e);if(t){const n=t[0],r=function(s,o){const i=s.match(/^(\s+)(?:```)/);if(i===null)return o;const l=i[1];return o.split(`
`).map(a=>{const c=a.match(/^\s+/);if(c===null)return a;const[p]=c;return p.length>=l.length?a.slice(l.length):a}).join(`
`)}(n,t[3]||"");return{type:"code",raw:n,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:r}}}heading(e){const t=this.rules.block.heading.exec(e);if(t){let n=t[2].trim();if(/#$/.test(n)){const r=$e(n,"#");this.options.pedantic?n=r.trim():r&&!/ $/.test(r)||(n=r.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:n,tokens:this.lexer.inline(n)}}}hr(e){const t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:t[0]}}blockquote(e){const t=this.rules.block.blockquote.exec(e);if(t){const n=$e(t[0].replace(/^ *>[ \t]?/gm,""),`
`),r=this.lexer.state.top;this.lexer.state.top=!0;const s=this.lexer.blockTokens(n);return this.lexer.state.top=r,{type:"blockquote",raw:t[0],tokens:s,text:n}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n=t[1].trim();const r=n.length>1,s={type:"list",raw:"",ordered:r,start:r?+n.slice(0,-1):"",loose:!1,items:[]};n=r?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`,this.options.pedantic&&(n=r?n:"[*+-]");const o=new RegExp(`^( {0,3}${n})((?:[	 ][^\\n]*)?(?:\\n|$))`);let i="",l="",a=!1;for(;e;){let c=!1;if(!(t=o.exec(e))||this.rules.block.hr.test(e))break;i=t[0],e=e.substring(i.length);let p=t[2].split(`
`,1)[0].replace(/^\t+/,F=>" ".repeat(3*F.length)),D=e.split(`
`,1)[0],h=0;this.options.pedantic?(h=2,l=p.trimStart()):(h=t[2].search(/[^ ]/),h=h>4?1:h,l=p.slice(h),h+=t[1].length);let f=!1;if(!p&&/^ *$/.test(D)&&(i+=D+`
`,e=e.substring(D.length+1),c=!0),!c){const F=new RegExp(`^ {0,${Math.min(3,h-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),_=new RegExp(`^ {0,${Math.min(3,h-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),A=new RegExp(`^ {0,${Math.min(3,h-1)}}(?:\`\`\`|~~~)`),y=new RegExp(`^ {0,${Math.min(3,h-1)}}#`);for(;e;){const g=e.split(`
`,1)[0];if(D=g,this.options.pedantic&&(D=D.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),A.test(D)||y.test(D)||F.test(D)||_.test(e))break;if(D.search(/[^ ]/)>=h||!D.trim())l+=`
`+D.slice(h);else{if(f||p.search(/[^ ]/)>=4||A.test(p)||y.test(p)||_.test(p))break;l+=`
`+D}f||D.trim()||(f=!0),i+=g+`
`,e=e.substring(g.length+1),p=D.slice(h)}}s.loose||(a?s.loose=!0:/\n *\n *$/.test(i)&&(a=!0));let d,x=null;this.options.gfm&&(x=/^\[[ xX]\] /.exec(l),x&&(d=x[0]!=="[ ] ",l=l.replace(/^\[[ xX]\] +/,""))),s.items.push({type:"list_item",raw:i,task:!!x,checked:d,loose:!1,text:l,tokens:[]}),s.raw+=i}s.items[s.items.length-1].raw=i.trimEnd(),s.items[s.items.length-1].text=l.trimEnd(),s.raw=s.raw.trimEnd();for(let c=0;c<s.items.length;c++)if(this.lexer.state.top=!1,s.items[c].tokens=this.lexer.blockTokens(s.items[c].text,[]),!s.loose){const p=s.items[c].tokens.filter(h=>h.type==="space"),D=p.length>0&&p.some(h=>/\n.*\n/.test(h.raw));s.loose=D}if(s.loose)for(let c=0;c<s.items.length;c++)s.items[c].loose=!0;return s}}html(e){const t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:t[1]==="pre"||t[1]==="script"||t[1]==="style",text:t[0]}}def(e){const t=this.rules.block.def.exec(e);if(t){const n=t[1].toLowerCase().replace(/\s+/g," "),r=t[2]?t[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",s=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:n,raw:t[0],href:r,title:s}}}table(e){const t=this.rules.block.table.exec(e);if(!t||!/[:|]/.test(t[2]))return;const n=Bt(t[1]),r=t[2].replace(/^\||\| *$/g,"").split("|"),s=t[3]&&t[3].trim()?t[3].replace(/\n[ \t]*$/,"").split(`
`):[],o={type:"table",raw:t[0],header:[],align:[],rows:[]};if(n.length===r.length){for(const i of r)/^ *-+: *$/.test(i)?o.align.push("right"):/^ *:-+: *$/.test(i)?o.align.push("center"):/^ *:-+ *$/.test(i)?o.align.push("left"):o.align.push(null);for(const i of n)o.header.push({text:i,tokens:this.lexer.inline(i)});for(const i of s)o.rows.push(Bt(i,o.header.length).map(l=>({text:l,tokens:this.lexer.inline(l)})));return o}}lheading(e){const t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:t[2].charAt(0)==="="?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){const t=this.rules.block.paragraph.exec(e);if(t){const n=t[1].charAt(t[1].length-1)===`
`?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:n,tokens:this.lexer.inline(n)}}}text(e){const t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){const t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:M(t[1])}}tag(e){const t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&/^<a /i.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){const t=this.rules.inline.link.exec(e);if(t){const n=t[2].trim();if(!this.options.pedantic&&/^</.test(n)){if(!/>$/.test(n))return;const o=$e(n.slice(0,-1),"\\");if((n.length-o.length)%2==0)return}else{const o=function(i,l){if(i.indexOf(l[1])===-1)return-1;let a=0;for(let c=0;c<i.length;c++)if(i[c]==="\\")c++;else if(i[c]===l[0])a++;else if(i[c]===l[1]&&(a--,a<0))return c;return-1}(t[2],"()");if(o>-1){const i=(t[0].indexOf("!")===0?5:4)+t[1].length+o;t[2]=t[2].substring(0,o),t[0]=t[0].substring(0,i).trim(),t[3]=""}}let r=t[2],s="";if(this.options.pedantic){const o=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(r);o&&(r=o[1],s=o[3])}else s=t[3]?t[3].slice(1,-1):"";return r=r.trim(),/^</.test(r)&&(r=this.options.pedantic&&!/>$/.test(n)?r.slice(1):r.slice(1,-1)),yt(t,{href:r&&r.replace(this.rules.inline.anyPunctuation,"$1"),title:s&&s.replace(this.rules.inline.anyPunctuation,"$1")},t[0],this.lexer)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){const r=t[(n[2]||n[1]).replace(/\s+/g," ").toLowerCase()];if(!r){const s=n[0].charAt(0);return{type:"text",raw:s,text:s}}return yt(n,r,n[0],this.lexer)}}emStrong(e,t,n=""){let r=this.rules.inline.emStrongLDelim.exec(e);if(r&&!(r[3]&&n.match(/[\p{L}\p{N}]/u))&&(!(r[1]||r[2])||!n||this.rules.inline.punctuation.exec(n))){const s=[...r[0]].length-1;let o,i,l=s,a=0;const c=r[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(c.lastIndex=0,t=t.slice(-1*e.length+s);(r=c.exec(t))!=null;){if(o=r[1]||r[2]||r[3]||r[4]||r[5]||r[6],!o)continue;if(i=[...o].length,r[3]||r[4]){l+=i;continue}if((r[5]||r[6])&&s%3&&!((s+i)%3)){a+=i;continue}if(l-=i,l>0)continue;i=Math.min(i,i+l+a);const p=[...r[0]][0].length,D=e.slice(0,s+r.index+p+i);if(Math.min(s,i)%2){const f=D.slice(1,-1);return{type:"em",raw:D,text:f,tokens:this.lexer.inlineTokens(f)}}const h=D.slice(2,-2);return{type:"strong",raw:D,text:h,tokens:this.lexer.inlineTokens(h)}}}}codespan(e){const t=this.rules.inline.code.exec(e);if(t){let n=t[2].replace(/\n/g," ");const r=/[^ ]/.test(n),s=/^ /.test(n)&&/ $/.test(n);return r&&s&&(n=n.substring(1,n.length-1)),n=M(n,!0),{type:"codespan",raw:t[0],text:n}}}br(e){const t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){const t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){const t=this.rules.inline.autolink.exec(e);if(t){let n,r;return t[2]==="@"?(n=M(t[1]),r="mailto:"+n):(n=M(t[1]),r=n),{type:"link",raw:t[0],text:n,href:r,tokens:[{type:"text",raw:n,text:n}]}}}url(e){var n;let t;if(t=this.rules.inline.url.exec(e)){let r,s;if(t[2]==="@")r=M(t[0]),s="mailto:"+r;else{let o;do o=t[0],t[0]=((n=this.rules.inline._backpedal.exec(t[0]))==null?void 0:n[0])??"";while(o!==t[0]);r=M(t[0]),s=t[1]==="www."?"http://"+t[0]:t[0]}return{type:"link",raw:t[0],text:r,href:s,tokens:[{type:"text",raw:r,text:r}]}}}inlineText(e){const t=this.rules.inline.text.exec(e);if(t){let n;return n=this.lexer.state.inRawBlock?t[0]:M(t[0]),{type:"text",raw:t[0],text:n}}}}const fe=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,rn=/(?:[*+-]|\d{1,9}[.)])/,sn=S(/^(?!bull )((?:.|\n(?!\s*?\n|bull ))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,rn).getRegex(),Ue=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,Je=/(?!\s*\])(?:\\.|[^\[\]\\])+/,Wr=S(/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/).replace("label",Je).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),Vr=S(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,rn).getRegex(),Te="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",Xe=/<!--(?!-?>)[\s\S]*?(?:-->|$)/,Ur=S("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))","i").replace("comment",Xe).replace("tag",Te).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),zt=S(Ue).replace("hr",fe).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Te).getRegex(),Ge={blockquote:S(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",zt).getRegex(),code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,def:Wr,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:fe,html:Ur,lheading:sn,list:Vr,newline:/^(?: *(?:\n|$))+/,paragraph:zt,table:ae,text:/^[^\n]+/},St=S("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",fe).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Te).getRegex(),Jr={...Ge,table:St,paragraph:S(Ue).replace("hr",fe).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",St).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Te).getRegex()},Xr={...Ge,html:S(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",Xe).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:ae,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:S(Ue).replace("hr",fe).replace("heading",` *#{1,6} *[^
]`).replace("lheading",sn).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},un=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,on=/^( {2,}|\\)\n(?!\s*$)/,ge="\\p{P}$+<=>`^|~",Gr=S(/^((?![*_])[\spunctuation])/,"u").replace(/punctuation/g,ge).getRegex(),Kr=S(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,"u").replace(/punct/g,ge).getRegex(),Yr=S("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])","gu").replace(/punct/g,ge).getRegex(),es=S("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])","gu").replace(/punct/g,ge).getRegex(),ts=S(/\\([punct])/,"gu").replace(/punct/g,ge).getRegex(),ns=S(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),rs=S(Xe).replace("(?:-->|$)","-->").getRegex(),ss=S("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",rs).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),be=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,us=S(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",be).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),Tt=S(/^!?\[(label)\]\[(ref)\]/).replace("label",be).replace("ref",Je).getRegex(),Rt=S(/^!?\[(ref)\](?:\[\])?/).replace("ref",Je).getRegex(),Ke={_backpedal:ae,anyPunctuation:ts,autolink:ns,blockSkip:/\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g,br:on,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:ae,emStrongLDelim:Kr,emStrongRDelimAst:Yr,emStrongRDelimUnd:es,escape:un,link:us,nolink:Rt,punctuation:Gr,reflink:Tt,reflinkSearch:S("reflink|nolink(?!\\()","g").replace("reflink",Tt).replace("nolink",Rt).getRegex(),tag:ss,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:ae},os={...Ke,link:S(/^!?\[(label)\]\((.*?)\)/).replace("label",be).getRegex(),reflink:S(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",be).getRegex()},je={...Ke,escape:S(un).replace("])","~|])").getRegex(),url:S(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},is={...je,br:S(on).replace("{2,}","*").getRegex(),text:S(je.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},xe={normal:Ge,gfm:Jr,pedantic:Xr},oe={normal:Ke,gfm:je,breaks:is,pedantic:os};class H{constructor(e){R(this,"tokens");R(this,"options");R(this,"state");R(this,"tokenizer");R(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=e||re,this.options.tokenizer=this.options.tokenizer||new we,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={block:xe.normal,inline:oe.normal};this.options.pedantic?(t.block=xe.pedantic,t.inline=oe.pedantic):this.options.gfm&&(t.block=xe.gfm,this.options.breaks?t.inline=oe.breaks:t.inline=oe.gfm),this.tokenizer.rules=t}static get rules(){return{block:xe,inline:oe}}static lex(e,t){return new H(t).lex(e)}static lexInline(e,t){return new H(t).inlineTokens(e)}lex(e){e=e.replace(/\r\n|\r/g,`
`),this.blockTokens(e,this.tokens);for(let t=0;t<this.inlineQueue.length;t++){const n=this.inlineQueue[t];this.inlineTokens(n.src,n.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[]){let n,r,s,o;for(e=this.options.pedantic?e.replace(/\t/g,"    ").replace(/^ +$/gm,""):e.replace(/^( *)(\t+)/gm,(i,l,a)=>l+"    ".repeat(a.length));e;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(i=>!!(n=i.call({lexer:this},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0))))if(n=this.tokenizer.space(e))e=e.substring(n.raw.length),n.raw.length===1&&t.length>0?t[t.length-1].raw+=`
`:t.push(n);else if(n=this.tokenizer.code(e))e=e.substring(n.raw.length),r=t[t.length-1],!r||r.type!=="paragraph"&&r.type!=="text"?t.push(n):(r.raw+=`
`+n.raw,r.text+=`
`+n.text,this.inlineQueue[this.inlineQueue.length-1].src=r.text);else if(n=this.tokenizer.fences(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.heading(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.hr(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.blockquote(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.list(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.html(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.def(e))e=e.substring(n.raw.length),r=t[t.length-1],!r||r.type!=="paragraph"&&r.type!=="text"?this.tokens.links[n.tag]||(this.tokens.links[n.tag]={href:n.href,title:n.title}):(r.raw+=`
`+n.raw,r.text+=`
`+n.raw,this.inlineQueue[this.inlineQueue.length-1].src=r.text);else if(n=this.tokenizer.table(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.lheading(e))e=e.substring(n.raw.length),t.push(n);else{if(s=e,this.options.extensions&&this.options.extensions.startBlock){let i=1/0;const l=e.slice(1);let a;this.options.extensions.startBlock.forEach(c=>{a=c.call({lexer:this},l),typeof a=="number"&&a>=0&&(i=Math.min(i,a))}),i<1/0&&i>=0&&(s=e.substring(0,i+1))}if(this.state.top&&(n=this.tokenizer.paragraph(s)))r=t[t.length-1],o&&r.type==="paragraph"?(r.raw+=`
`+n.raw,r.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=r.text):t.push(n),o=s.length!==e.length,e=e.substring(n.raw.length);else if(n=this.tokenizer.text(e))e=e.substring(n.raw.length),r=t[t.length-1],r&&r.type==="text"?(r.raw+=`
`+n.raw,r.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=r.text):t.push(n);else if(e){const i="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(i);break}throw new Error(i)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){let n,r,s,o,i,l,a=e;if(this.tokens.links){const c=Object.keys(this.tokens.links);if(c.length>0)for(;(o=this.tokenizer.rules.inline.reflinkSearch.exec(a))!=null;)c.includes(o[0].slice(o[0].lastIndexOf("[")+1,-1))&&(a=a.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+a.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(o=this.tokenizer.rules.inline.blockSkip.exec(a))!=null;)a=a.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+a.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(o=this.tokenizer.rules.inline.anyPunctuation.exec(a))!=null;)a=a.slice(0,o.index)+"++"+a.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;e;)if(i||(l=""),i=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(c=>!!(n=c.call({lexer:this},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0))))if(n=this.tokenizer.escape(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.tag(e))e=e.substring(n.raw.length),r=t[t.length-1],r&&n.type==="text"&&r.type==="text"?(r.raw+=n.raw,r.text+=n.text):t.push(n);else if(n=this.tokenizer.link(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.reflink(e,this.tokens.links))e=e.substring(n.raw.length),r=t[t.length-1],r&&n.type==="text"&&r.type==="text"?(r.raw+=n.raw,r.text+=n.text):t.push(n);else if(n=this.tokenizer.emStrong(e,a,l))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.codespan(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.br(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.del(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.autolink(e))e=e.substring(n.raw.length),t.push(n);else if(this.state.inLink||!(n=this.tokenizer.url(e))){if(s=e,this.options.extensions&&this.options.extensions.startInline){let c=1/0;const p=e.slice(1);let D;this.options.extensions.startInline.forEach(h=>{D=h.call({lexer:this},p),typeof D=="number"&&D>=0&&(c=Math.min(c,D))}),c<1/0&&c>=0&&(s=e.substring(0,c+1))}if(n=this.tokenizer.inlineText(s))e=e.substring(n.raw.length),n.raw.slice(-1)!=="_"&&(l=n.raw.slice(-1)),i=!0,r=t[t.length-1],r&&r.type==="text"?(r.raw+=n.raw,r.text+=n.text):t.push(n);else if(e){const c="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(c);break}throw new Error(c)}}else e=e.substring(n.raw.length),t.push(n);return t}}class ve{constructor(e){R(this,"options");this.options=e||re}code(e,t,n){var s;const r=(s=(t||"").match(/^\S*/))==null?void 0:s[0];return e=e.replace(/\n$/,"")+`
`,r?'<pre><code class="language-'+M(r)+'">'+(n?e:M(e,!0))+`</code></pre>
`:"<pre><code>"+(n?e:M(e,!0))+`</code></pre>
`}blockquote(e){return`<blockquote>
${e}</blockquote>
`}html(e,t){return e}heading(e,t,n){return`<h${t}>${e}</h${t}>
`}hr(){return`<hr>
`}list(e,t,n){const r=t?"ol":"ul";return"<"+r+(t&&n!==1?' start="'+n+'"':"")+`>
`+e+"</"+r+`>
`}listitem(e,t,n){return`<li>${e}</li>
`}checkbox(e){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph(e){return`<p>${e}</p>
`}table(e,t){return t&&(t=`<tbody>${t}</tbody>`),`<table>
<thead>
`+e+`</thead>
`+t+`</table>
`}tablerow(e){return`<tr>
${e}</tr>
`}tablecell(e,t){const n=t.header?"th":"td";return(t.align?`<${n} align="${t.align}">`:`<${n}>`)+e+`</${n}>
`}strong(e){return`<strong>${e}</strong>`}em(e){return`<em>${e}</em>`}codespan(e){return`<code>${e}</code>`}br(){return"<br>"}del(e){return`<del>${e}</del>`}link(e,t,n){const r=vt(e);if(r===null)return n;let s='<a href="'+(e=r)+'"';return t&&(s+=' title="'+t+'"'),s+=">"+n+"</a>",s}image(e,t,n){const r=vt(e);if(r===null)return n;let s=`<img src="${e=r}" alt="${n}"`;return t&&(s+=` title="${t}"`),s+=">",s}text(e){return e}}class Ye{strong(e){return e}em(e){return e}codespan(e){return e}del(e){return e}html(e){return e}text(e){return e}link(e,t,n){return""+n}image(e,t,n){return""+n}br(){return""}}class W{constructor(e){R(this,"options");R(this,"renderer");R(this,"textRenderer");this.options=e||re,this.options.renderer=this.options.renderer||new ve,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new Ye}static parse(e,t){return new W(t).parse(e)}static parseInline(e,t){return new W(t).parseInline(e)}parse(e,t=!0){let n="";for(let r=0;r<e.length;r++){const s=e[r];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[s.type]){const o=s,i=this.options.extensions.renderers[o.type].call({parser:this},o);if(i!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(o.type)){n+=i||"";continue}}switch(s.type){case"space":continue;case"hr":n+=this.renderer.hr();continue;case"heading":{const o=s;n+=this.renderer.heading(this.parseInline(o.tokens),o.depth,Hr(this.parseInline(o.tokens,this.textRenderer)));continue}case"code":{const o=s;n+=this.renderer.code(o.text,o.lang,!!o.escaped);continue}case"table":{const o=s;let i="",l="";for(let c=0;c<o.header.length;c++)l+=this.renderer.tablecell(this.parseInline(o.header[c].tokens),{header:!0,align:o.align[c]});i+=this.renderer.tablerow(l);let a="";for(let c=0;c<o.rows.length;c++){const p=o.rows[c];l="";for(let D=0;D<p.length;D++)l+=this.renderer.tablecell(this.parseInline(p[D].tokens),{header:!1,align:o.align[D]});a+=this.renderer.tablerow(l)}n+=this.renderer.table(i,a);continue}case"blockquote":{const o=s,i=this.parse(o.tokens);n+=this.renderer.blockquote(i);continue}case"list":{const o=s,i=o.ordered,l=o.start,a=o.loose;let c="";for(let p=0;p<o.items.length;p++){const D=o.items[p],h=D.checked,f=D.task;let d="";if(D.task){const x=this.renderer.checkbox(!!h);a?D.tokens.length>0&&D.tokens[0].type==="paragraph"?(D.tokens[0].text=x+" "+D.tokens[0].text,D.tokens[0].tokens&&D.tokens[0].tokens.length>0&&D.tokens[0].tokens[0].type==="text"&&(D.tokens[0].tokens[0].text=x+" "+D.tokens[0].tokens[0].text)):D.tokens.unshift({type:"text",text:x+" "}):d+=x+" "}d+=this.parse(D.tokens,a),c+=this.renderer.listitem(d,f,!!h)}n+=this.renderer.list(c,i,l);continue}case"html":{const o=s;n+=this.renderer.html(o.text,o.block);continue}case"paragraph":{const o=s;n+=this.renderer.paragraph(this.parseInline(o.tokens));continue}case"text":{let o=s,i=o.tokens?this.parseInline(o.tokens):o.text;for(;r+1<e.length&&e[r+1].type==="text";)o=e[++r],i+=`
`+(o.tokens?this.parseInline(o.tokens):o.text);n+=t?this.renderer.paragraph(i):i;continue}default:{const o='Token with "'+s.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return n}parseInline(e,t){t=t||this.renderer;let n="";for(let r=0;r<e.length;r++){const s=e[r];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[s.type]){const o=this.options.extensions.renderers[s.type].call({parser:this},s);if(o!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(s.type)){n+=o||"";continue}}switch(s.type){case"escape":{const o=s;n+=t.text(o.text);break}case"html":{const o=s;n+=t.html(o.text);break}case"link":{const o=s;n+=t.link(o.href,o.title,this.parseInline(o.tokens,t));break}case"image":{const o=s;n+=t.image(o.href,o.title,o.text);break}case"strong":{const o=s;n+=t.strong(this.parseInline(o.tokens,t));break}case"em":{const o=s;n+=t.em(this.parseInline(o.tokens,t));break}case"codespan":{const o=s;n+=t.codespan(o.text);break}case"br":n+=t.br();break;case"del":{const o=s;n+=t.del(this.parseInline(o.tokens,t));break}case"text":{const o=s;n+=t.text(o.text);break}default:{const o='Token with "'+s.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return n}}class ce{constructor(e){R(this,"options");this.options=e||re}preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}}R(ce,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));var ne,Ne,ln,Yt;const ee=new(Yt=class{constructor(...u){st(this,ne);R(this,"defaults",{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null});R(this,"options",this.setOptions);R(this,"parse",me(this,ne,Ne).call(this,H.lex,W.parse));R(this,"parseInline",me(this,ne,Ne).call(this,H.lexInline,W.parseInline));R(this,"Parser",W);R(this,"Renderer",ve);R(this,"TextRenderer",Ye);R(this,"Lexer",H);R(this,"Tokenizer",we);R(this,"Hooks",ce);this.use(...u)}walkTokens(u,e){var n,r;let t=[];for(const s of u)switch(t=t.concat(e.call(this,s)),s.type){case"table":{const o=s;for(const i of o.header)t=t.concat(this.walkTokens(i.tokens,e));for(const i of o.rows)for(const l of i)t=t.concat(this.walkTokens(l.tokens,e));break}case"list":{const o=s;t=t.concat(this.walkTokens(o.items,e));break}default:{const o=s;(r=(n=this.defaults.extensions)==null?void 0:n.childTokens)!=null&&r[o.type]?this.defaults.extensions.childTokens[o.type].forEach(i=>{const l=o[i].flat(1/0);t=t.concat(this.walkTokens(l,e))}):o.tokens&&(t=t.concat(this.walkTokens(o.tokens,e)))}}return t}use(...u){const e=this.defaults.extensions||{renderers:{},childTokens:{}};return u.forEach(t=>{const n={...t};if(n.async=this.defaults.async||n.async||!1,t.extensions&&(t.extensions.forEach(r=>{if(!r.name)throw new Error("extension name required");if("renderer"in r){const s=e.renderers[r.name];e.renderers[r.name]=s?function(...o){let i=r.renderer.apply(this,o);return i===!1&&(i=s.apply(this,o)),i}:r.renderer}if("tokenizer"in r){if(!r.level||r.level!=="block"&&r.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const s=e[r.level];s?s.unshift(r.tokenizer):e[r.level]=[r.tokenizer],r.start&&(r.level==="block"?e.startBlock?e.startBlock.push(r.start):e.startBlock=[r.start]:r.level==="inline"&&(e.startInline?e.startInline.push(r.start):e.startInline=[r.start]))}"childTokens"in r&&r.childTokens&&(e.childTokens[r.name]=r.childTokens)}),n.extensions=e),t.renderer){const r=this.defaults.renderer||new ve(this.defaults);for(const s in t.renderer){if(!(s in r))throw new Error(`renderer '${s}' does not exist`);if(s==="options")continue;const o=s,i=t.renderer[o],l=r[o];r[o]=(...a)=>{let c=i.apply(r,a);return c===!1&&(c=l.apply(r,a)),c||""}}n.renderer=r}if(t.tokenizer){const r=this.defaults.tokenizer||new we(this.defaults);for(const s in t.tokenizer){if(!(s in r))throw new Error(`tokenizer '${s}' does not exist`);if(["options","rules","lexer"].includes(s))continue;const o=s,i=t.tokenizer[o],l=r[o];r[o]=(...a)=>{let c=i.apply(r,a);return c===!1&&(c=l.apply(r,a)),c}}n.tokenizer=r}if(t.hooks){const r=this.defaults.hooks||new ce;for(const s in t.hooks){if(!(s in r))throw new Error(`hook '${s}' does not exist`);if(s==="options")continue;const o=s,i=t.hooks[o],l=r[o];ce.passThroughHooks.has(s)?r[o]=a=>{if(this.defaults.async)return Promise.resolve(i.call(r,a)).then(p=>l.call(r,p));const c=i.call(r,a);return l.call(r,c)}:r[o]=(...a)=>{let c=i.apply(r,a);return c===!1&&(c=l.apply(r,a)),c}}n.hooks=r}if(t.walkTokens){const r=this.defaults.walkTokens,s=t.walkTokens;n.walkTokens=function(o){let i=[];return i.push(s.call(this,o)),r&&(i=i.concat(r.call(this,o))),i}}this.defaults={...this.defaults,...n}}),this}setOptions(u){return this.defaults={...this.defaults,...u},this}lexer(u,e){return H.lex(u,e??this.defaults)}parser(u,e){return W.parse(u,e??this.defaults)}},ne=new WeakSet,Ne=function(u,e){return(t,n)=>{const r={...n},s={...this.defaults,...r};this.defaults.async===!0&&r.async===!1&&(s.silent||console.warn("marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored."),s.async=!0);const o=me(this,ne,ln).call(this,!!s.silent,!!s.async);if(t==null)return o(new Error("marked(): input parameter is undefined or null"));if(typeof t!="string")return o(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));if(s.hooks&&(s.hooks.options=s),s.async)return Promise.resolve(s.hooks?s.hooks.preprocess(t):t).then(i=>u(i,s)).then(i=>s.hooks?s.hooks.processAllTokens(i):i).then(i=>s.walkTokens?Promise.all(this.walkTokens(i,s.walkTokens)).then(()=>i):i).then(i=>e(i,s)).then(i=>s.hooks?s.hooks.postprocess(i):i).catch(o);try{s.hooks&&(t=s.hooks.preprocess(t));let i=u(t,s);s.hooks&&(i=s.hooks.processAllTokens(i)),s.walkTokens&&this.walkTokens(i,s.walkTokens);let l=e(i,s);return s.hooks&&(l=s.hooks.postprocess(l)),l}catch(i){return o(i)}}},ln=function(u,e){return t=>{if(t.message+=`
Please report this to https://github.com/markedjs/marked.`,u){const n="<p>An error occurred:</p><pre>"+M(t.message+"",!0)+"</pre>";return e?Promise.resolve(n):n}if(e)return Promise.reject(t);throw t}},Yt);function z(u,e){return ee.parse(u,e)}z.options=z.setOptions=function(u){return ee.setOptions(u),z.defaults=ee.defaults,wt(z.defaults),z},z.getDefaults=jr,z.defaults=re,z.use=function(...u){return ee.use(...u),z.defaults=ee.defaults,wt(z.defaults),z},z.walkTokens=function(u,e){return ee.walkTokens(u,e)},z.parseInline=ee.parseInline,z.Parser=W,z.parser=W.parse,z.Renderer=ve,z.TextRenderer=Ye,z.Lexer=H,z.lexer=H.lex,z.Tokenizer=we,z.Hooks=ce,z.parse=z,z.options,z.setOptions,z.use,z.walkTokens,z.parseInline,W.parse,H.lex;const ls=/[\0-\x1F!-,\.\/:-@\[-\^`\{-\xA9\xAB-\xB4\xB6-\xB9\xBB-\xBF\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0378\u0379\u037E\u0380-\u0385\u0387\u038B\u038D\u03A2\u03F6\u0482\u0530\u0557\u0558\u055A-\u055F\u0589-\u0590\u05BE\u05C0\u05C3\u05C6\u05C8-\u05CF\u05EB-\u05EE\u05F3-\u060F\u061B-\u061F\u066A-\u066D\u06D4\u06DD\u06DE\u06E9\u06FD\u06FE\u0700-\u070F\u074B\u074C\u07B2-\u07BF\u07F6-\u07F9\u07FB\u07FC\u07FE\u07FF\u082E-\u083F\u085C-\u085F\u086B-\u089F\u08B5\u08C8-\u08D2\u08E2\u0964\u0965\u0970\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA\u09BB\u09C5\u09C6\u09C9\u09CA\u09CF-\u09D6\u09D8-\u09DB\u09DE\u09E4\u09E5\u09F2-\u09FB\u09FD\u09FF\u0A00\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A\u0A3B\u0A3D\u0A43-\u0A46\u0A49\u0A4A\u0A4E-\u0A50\u0A52-\u0A58\u0A5D\u0A5F-\u0A65\u0A76-\u0A80\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA\u0ABB\u0AC6\u0ACA\u0ACE\u0ACF\u0AD1-\u0ADF\u0AE4\u0AE5\u0AF0-\u0AF8\u0B00\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A\u0B3B\u0B45\u0B46\u0B49\u0B4A\u0B4E-\u0B54\u0B58-\u0B5B\u0B5E\u0B64\u0B65\u0B70\u0B72-\u0B81\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BBD\u0BC3-\u0BC5\u0BC9\u0BCE\u0BCF\u0BD1-\u0BD6\u0BD8-\u0BE5\u0BF0-\u0BFF\u0C0D\u0C11\u0C29\u0C3A-\u0C3C\u0C45\u0C49\u0C4E-\u0C54\u0C57\u0C5B-\u0C5F\u0C64\u0C65\u0C70-\u0C7F\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA\u0CBB\u0CC5\u0CC9\u0CCE-\u0CD4\u0CD7-\u0CDD\u0CDF\u0CE4\u0CE5\u0CF0\u0CF3-\u0CFF\u0D0D\u0D11\u0D45\u0D49\u0D4F-\u0D53\u0D58-\u0D5E\u0D64\u0D65\u0D70-\u0D79\u0D80\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0DC9\u0DCB-\u0DCE\u0DD5\u0DD7\u0DE0-\u0DE5\u0DF0\u0DF1\u0DF4-\u0E00\u0E3B-\u0E3F\u0E4F\u0E5A-\u0E80\u0E83\u0E85\u0E8B\u0EA4\u0EA6\u0EBE\u0EBF\u0EC5\u0EC7\u0ECE\u0ECF\u0EDA\u0EDB\u0EE0-\u0EFF\u0F01-\u0F17\u0F1A-\u0F1F\u0F2A-\u0F34\u0F36\u0F38\u0F3A-\u0F3D\u0F48\u0F6D-\u0F70\u0F85\u0F98\u0FBD-\u0FC5\u0FC7-\u0FFF\u104A-\u104F\u109E\u109F\u10C6\u10C8-\u10CC\u10CE\u10CF\u10FB\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B\u135C\u1360-\u137F\u1390-\u139F\u13F6\u13F7\u13FE-\u1400\u166D\u166E\u1680\u169B-\u169F\u16EB-\u16ED\u16F9-\u16FF\u170D\u1715-\u171F\u1735-\u173F\u1754-\u175F\u176D\u1771\u1774-\u177F\u17D4-\u17D6\u17D8-\u17DB\u17DE\u17DF\u17EA-\u180A\u180E\u180F\u181A-\u181F\u1879-\u187F\u18AB-\u18AF\u18F6-\u18FF\u191F\u192C-\u192F\u193C-\u1945\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19CF\u19DA-\u19FF\u1A1C-\u1A1F\u1A5F\u1A7D\u1A7E\u1A8A-\u1A8F\u1A9A-\u1AA6\u1AA8-\u1AAF\u1AC1-\u1AFF\u1B4C-\u1B4F\u1B5A-\u1B6A\u1B74-\u1B7F\u1BF4-\u1BFF\u1C38-\u1C3F\u1C4A-\u1C4C\u1C7E\u1C7F\u1C89-\u1C8F\u1CBB\u1CBC\u1CC0-\u1CCF\u1CD3\u1CFB-\u1CFF\u1DFA\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FBD\u1FBF-\u1FC1\u1FC5\u1FCD-\u1FCF\u1FD4\u1FD5\u1FDC-\u1FDF\u1FED-\u1FF1\u1FF5\u1FFD-\u203E\u2041-\u2053\u2055-\u2070\u2072-\u207E\u2080-\u208F\u209D-\u20CF\u20F1-\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F-\u215F\u2189-\u24B5\u24EA-\u2BFF\u2C2F\u2C5F\u2CE5-\u2CEA\u2CF4-\u2CFF\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D70-\u2D7E\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF\u2E00-\u2E2E\u2E30-\u3004\u3008-\u3020\u3030\u3036\u3037\u303D-\u3040\u3097\u3098\u309B\u309C\u30A0\u30FB\u3100-\u3104\u3130\u318F-\u319F\u31C0-\u31EF\u3200-\u33FF\u4DC0-\u4DFF\u9FFD-\u9FFF\uA48D-\uA4CF\uA4FE\uA4FF\uA60D-\uA60F\uA62C-\uA63F\uA673\uA67E\uA6F2-\uA716\uA720\uA721\uA789\uA78A\uA7C0\uA7C1\uA7CB-\uA7F4\uA828-\uA82B\uA82D-\uA83F\uA874-\uA87F\uA8C6-\uA8CF\uA8DA-\uA8DF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA954-\uA95F\uA97D-\uA97F\uA9C1-\uA9CE\uA9DA-\uA9DF\uA9FF\uAA37-\uAA3F\uAA4E\uAA4F\uAA5A-\uAA5F\uAA77-\uAA79\uAAC3-\uAADA\uAADE\uAADF\uAAF0\uAAF1\uAAF7-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F\uAB5B\uAB6A-\uAB6F\uABEB\uABEE\uABEF\uABFA-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uD7FF\uE000-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB29\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBB2-\uFBD2\uFD3E-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFC-\uFDFF\uFE10-\uFE1F\uFE30-\uFE32\uFE35-\uFE4C\uFE50-\uFE6F\uFE75\uFEFD-\uFF0F\uFF1A-\uFF20\uFF3B-\uFF3E\uFF40\uFF5B-\uFF65\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFFF]|\uD800[\uDC0C\uDC27\uDC3B\uDC3E\uDC4E\uDC4F\uDC5E-\uDC7F\uDCFB-\uDD3F\uDD75-\uDDFC\uDDFE-\uDE7F\uDE9D-\uDE9F\uDED1-\uDEDF\uDEE1-\uDEFF\uDF20-\uDF2C\uDF4B-\uDF4F\uDF7B-\uDF7F\uDF9E\uDF9F\uDFC4-\uDFC7\uDFD0\uDFD6-\uDFFF]|\uD801[\uDC9E\uDC9F\uDCAA-\uDCAF\uDCD4-\uDCD7\uDCFC-\uDCFF\uDD28-\uDD2F\uDD64-\uDDFF\uDF37-\uDF3F\uDF56-\uDF5F\uDF68-\uDFFF]|\uD802[\uDC06\uDC07\uDC09\uDC36\uDC39-\uDC3B\uDC3D\uDC3E\uDC56-\uDC5F\uDC77-\uDC7F\uDC9F-\uDCDF\uDCF3\uDCF6-\uDCFF\uDD16-\uDD1F\uDD3A-\uDD7F\uDDB8-\uDDBD\uDDC0-\uDDFF\uDE04\uDE07-\uDE0B\uDE14\uDE18\uDE36\uDE37\uDE3B-\uDE3E\uDE40-\uDE5F\uDE7D-\uDE7F\uDE9D-\uDEBF\uDEC8\uDEE7-\uDEFF\uDF36-\uDF3F\uDF56-\uDF5F\uDF73-\uDF7F\uDF92-\uDFFF]|\uD803[\uDC49-\uDC7F\uDCB3-\uDCBF\uDCF3-\uDCFF\uDD28-\uDD2F\uDD3A-\uDE7F\uDEAA\uDEAD-\uDEAF\uDEB2-\uDEFF\uDF1D-\uDF26\uDF28-\uDF2F\uDF51-\uDFAF\uDFC5-\uDFDF\uDFF7-\uDFFF]|\uD804[\uDC47-\uDC65\uDC70-\uDC7E\uDCBB-\uDCCF\uDCE9-\uDCEF\uDCFA-\uDCFF\uDD35\uDD40-\uDD43\uDD48-\uDD4F\uDD74\uDD75\uDD77-\uDD7F\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDFF\uDE12\uDE38-\uDE3D\uDE3F-\uDE7F\uDE87\uDE89\uDE8E\uDE9E\uDEA9-\uDEAF\uDEEB-\uDEEF\uDEFA-\uDEFF\uDF04\uDF0D\uDF0E\uDF11\uDF12\uDF29\uDF31\uDF34\uDF3A\uDF45\uDF46\uDF49\uDF4A\uDF4E\uDF4F\uDF51-\uDF56\uDF58-\uDF5C\uDF64\uDF65\uDF6D-\uDF6F\uDF75-\uDFFF]|\uD805[\uDC4B-\uDC4F\uDC5A-\uDC5D\uDC62-\uDC7F\uDCC6\uDCC8-\uDCCF\uDCDA-\uDD7F\uDDB6\uDDB7\uDDC1-\uDDD7\uDDDE-\uDDFF\uDE41-\uDE43\uDE45-\uDE4F\uDE5A-\uDE7F\uDEB9-\uDEBF\uDECA-\uDEFF\uDF1B\uDF1C\uDF2C-\uDF2F\uDF3A-\uDFFF]|\uD806[\uDC3B-\uDC9F\uDCEA-\uDCFE\uDD07\uDD08\uDD0A\uDD0B\uDD14\uDD17\uDD36\uDD39\uDD3A\uDD44-\uDD4F\uDD5A-\uDD9F\uDDA8\uDDA9\uDDD8\uDDD9\uDDE2\uDDE5-\uDDFF\uDE3F-\uDE46\uDE48-\uDE4F\uDE9A-\uDE9C\uDE9E-\uDEBF\uDEF9-\uDFFF]|\uD807[\uDC09\uDC37\uDC41-\uDC4F\uDC5A-\uDC71\uDC90\uDC91\uDCA8\uDCB7-\uDCFF\uDD07\uDD0A\uDD37-\uDD39\uDD3B\uDD3E\uDD48-\uDD4F\uDD5A-\uDD5F\uDD66\uDD69\uDD8F\uDD92\uDD99-\uDD9F\uDDAA-\uDEDF\uDEF7-\uDFAF\uDFB1-\uDFFF]|\uD808[\uDF9A-\uDFFF]|\uD809[\uDC6F-\uDC7F\uDD44-\uDFFF]|[\uD80A\uD80B\uD80E-\uD810\uD812-\uD819\uD824-\uD82B\uD82D\uD82E\uD830-\uD833\uD837\uD839\uD83D\uD83F\uD87B-\uD87D\uD87F\uD885-\uDB3F\uDB41-\uDBFF][\uDC00-\uDFFF]|\uD80D[\uDC2F-\uDFFF]|\uD811[\uDE47-\uDFFF]|\uD81A[\uDE39-\uDE3F\uDE5F\uDE6A-\uDECF\uDEEE\uDEEF\uDEF5-\uDEFF\uDF37-\uDF3F\uDF44-\uDF4F\uDF5A-\uDF62\uDF78-\uDF7C\uDF90-\uDFFF]|\uD81B[\uDC00-\uDE3F\uDE80-\uDEFF\uDF4B-\uDF4E\uDF88-\uDF8E\uDFA0-\uDFDF\uDFE2\uDFE5-\uDFEF\uDFF2-\uDFFF]|\uD821[\uDFF8-\uDFFF]|\uD823[\uDCD6-\uDCFF\uDD09-\uDFFF]|\uD82C[\uDD1F-\uDD4F\uDD53-\uDD63\uDD68-\uDD6F\uDEFC-\uDFFF]|\uD82F[\uDC6B-\uDC6F\uDC7D-\uDC7F\uDC89-\uDC8F\uDC9A-\uDC9C\uDC9F-\uDFFF]|\uD834[\uDC00-\uDD64\uDD6A-\uDD6C\uDD73-\uDD7A\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDE41\uDE45-\uDFFF]|\uD835[\uDC55\uDC9D\uDCA0\uDCA1\uDCA3\uDCA4\uDCA7\uDCA8\uDCAD\uDCBA\uDCBC\uDCC4\uDD06\uDD0B\uDD0C\uDD15\uDD1D\uDD3A\uDD3F\uDD45\uDD47-\uDD49\uDD51\uDEA6\uDEA7\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3\uDFCC\uDFCD]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85-\uDE9A\uDEA0\uDEB0-\uDFFF]|\uD838[\uDC07\uDC19\uDC1A\uDC22\uDC25\uDC2B-\uDCFF\uDD2D-\uDD2F\uDD3E\uDD3F\uDD4A-\uDD4D\uDD4F-\uDEBF\uDEFA-\uDFFF]|\uD83A[\uDCC5-\uDCCF\uDCD7-\uDCFF\uDD4C-\uDD4F\uDD5A-\uDFFF]|\uD83B[\uDC00-\uDDFF\uDE04\uDE20\uDE23\uDE25\uDE26\uDE28\uDE33\uDE38\uDE3A\uDE3C-\uDE41\uDE43-\uDE46\uDE48\uDE4A\uDE4C\uDE50\uDE53\uDE55\uDE56\uDE58\uDE5A\uDE5C\uDE5E\uDE60\uDE63\uDE65\uDE66\uDE6B\uDE73\uDE78\uDE7D\uDE7F\uDE8A\uDE9C-\uDEA0\uDEA4\uDEAA\uDEBC-\uDFFF]|\uD83C[\uDC00-\uDD2F\uDD4A-\uDD4F\uDD6A-\uDD6F\uDD8A-\uDFFF]|\uD83E[\uDC00-\uDFEF\uDFFA-\uDFFF]|\uD869[\uDEDE-\uDEFF]|\uD86D[\uDF35-\uDF3F]|\uD86E[\uDC1E\uDC1F]|\uD873[\uDEA2-\uDEAF]|\uD87A[\uDFE1-\uDFFF]|\uD87E[\uDE1E-\uDFFF]|\uD884[\uDF4B-\uDFFF]|\uDB40[\uDC00-\uDCFF\uDDF0-\uDFFF]/g,as=Object.hasOwnProperty;class cs{constructor(){this.occurrences,this.reset()}slug(e,t){const n=this;let r=function(o,i){return typeof o!="string"?"":(i||(o=o.toLowerCase()),o.replace(ls,"").replace(/ /g,"-"))}(e,t===!0);const s=r;for(;as.call(n.occurrences,r);)n.occurrences[s]++,r=s+"-"+n.occurrences[s];return n.occurrences[r]=0,r}reset(){this.occurrences=Object.create(null)}}function ps(u){let e,t;return e=new Se({props:{tokens:u[0],renderers:u[1],options:u[2]}}),{c(){X(e.$$.fragment)},m(n,r){J(e,n,r),t=!0},p(n,[r]){const s={};1&r&&(s.tokens=n[0]),2&r&&(s.renderers=n[1]),4&r&&(s.options=n[2]),e.$set(s)},i(n){t||(k(e.$$.fragment,n),t=!0)},o(n){$(e.$$.fragment,n),t=!1},d(n){U(e,n)}}}function hs(u,e,t){(function(){const a=console.warn;console.warn=c=>{c.includes("unknown prop")||c.includes("unexpected slot")||a(c)},gn(()=>{console.warn=a})})();let n,r,s,{source:o}=e,{options:i={}}=e,{renderers:l={}}=e;return u.$$set=a=>{"source"in a&&t(3,o=a.source),"options"in a&&t(4,i=a.options),"renderers"in a&&t(5,l=a.renderers)},u.$$.update=()=>{var a;56&u.$$.dirty&&(t(0,(a=o,n=new H().lex(a))),t(1,r={heading:Tn,blockquote:Ln,list:Qn,list_item:Un,br:Gn,code:er,codespan:rr,table:or,html:ar,paragraph:hr,link:fr,text:kr,def:xr,del:Ar,em:vr,hr:zr,strong:Rr,image:Pr,space:At,escape:At,...l}),t(2,s={baseUrl:"/",slugger:new cs,...i}))},[n,r,s,o,i,l]}class Ds extends v{constructor(e){super(),B(this,e,hs,ps,b,{source:3,options:4,renderers:5})}}const ds=u=>({}),It=u=>({}),fs=u=>({}),Lt=u=>({}),gs=u=>({}),Pt=u=>({});function Fs(u){let e,t,n,r,s,o,i,l,a,c,p,D;const h=u[13].topBarLeft,f=O(h,u,u[12],Pt),d=u[13].topBarRight,x=O(d,u,u[12],Lt);function F(g){u[16](g)}let _={options:{lineNumbers:"off",wrappingIndent:"same",padding:u[5],wordWrap:u[2]?"off":"on",contextmenu:!1,wordBasedSuggestions:"off",renderLineHighlight:"none",occurrencesHighlight:"off",selectionHighlight:!1,codeLens:!1,links:!1,hover:{enabled:!1},hideCursorInOverviewRuler:!0,renderWhitespace:"none",renderFinalNewline:"on"},text:u[3].text,lang:u[4]||u[3].lang,height:u[6]};u[0]!==void 0&&(_.editorInstance=u[0]),o=new vn({props:_}),Ve.push(()=>Fn(o,"editorInstance",F));const A=u[13].actionsBar,y=O(A,u,u[12],It);return{c(){e=w("div"),t=w("div"),n=w("div"),f&&f.c(),r=se(),x&&x.c(),s=se(),X(o.$$.fragment),l=se(),a=w("div"),y&&y.c(),m(n,"class","c-codeblock__top-bar-left svelte-mexfz1"),m(t,"class","c-codeblock__top-bar-anchor monaco-component svelte-mexfz1"),m(a,"class","c-codeblock__actions-bar-anchor svelte-mexfz1"),m(e,"class","c-codeblock svelte-mexfz1"),m(e,"role","button"),m(e,"tabindex","0")},m(g,I){E(g,e,I),L(e,t),L(t,n),f&&f.m(n,null),L(t,r),x&&x.m(t,null),L(e,s),J(o,e,null),L(e,l),L(e,a),y&&y.m(a,null),u[17](e),c=!0,p||(D=[it(window,"focus",u[15]),it(e,"mouseenter",u[14])],p=!0)},p(g,[I]){f&&f.p&&(!c||4096&I)&&j(f,h,g,g[12],c?q(h,g[12],I,gs):N(g[12]),Pt),x&&x.p&&(!c||4096&I)&&j(x,d,g,g[12],c?q(d,g[12],I,fs):N(g[12]),Lt);const P={};36&I&&(P.options={lineNumbers:"off",wrappingIndent:"same",padding:g[5],wordWrap:g[2]?"off":"on",contextmenu:!1,wordBasedSuggestions:"off",renderLineHighlight:"none",occurrencesHighlight:"off",selectionHighlight:!1,codeLens:!1,links:!1,hover:{enabled:!1},hideCursorInOverviewRuler:!0,renderWhitespace:"none",renderFinalNewline:"on"}),8&I&&(P.text=g[3].text),24&I&&(P.lang=g[4]||g[3].lang),64&I&&(P.height=g[6]),!i&&1&I&&(i=!0,P.editorInstance=g[0],mn(()=>i=!1)),o.$set(P),y&&y.p&&(!c||4096&I)&&j(y,A,g,g[12],c?q(A,g[12],I,ds):N(g[12]),It)},i(g){c||(k(f,g),k(x,g),k(o.$$.fragment,g),k(y,g),c=!0)},o(g){$(f,g),$(x,g),$(o.$$.fragment,g),$(y,g),c=!1},d(g){g&&C(e),f&&f.d(g),x&&x.d(g),U(o),y&&y.d(g),u[17](null),p=!1,kn(D)}}}function ks(u,e,t){let n,{$$slots:r={},$$scope:s}=e,{scroll:o=!1}=e,{token:i}=e,{language:l}=e,{padding:a={top:0,bottom:0}}=e,{editorInstance:c}=e,{element:p}=e,{height:D}=e;const h=yn.getContext().monaco;$n(u,h,F=>t(18,n=F));const f=Bn(),d=()=>{if(!c)return;const F=c.getSelections();if(!(F!=null&&F.length))return;const _=c.getModel();if(F.map(A=>(_==null?void 0:_.getValueLengthInRange(A))||0).reduce((A,y)=>A+y,0)!==0)return F.sort(n==null?void 0:n.Range.compareRangesUsingStarts).map(A=>(_==null?void 0:_.getValueInRange(A))||"").join(`
`)},x=()=>{if(c)return c.getValue()||""};return u.$$set=F=>{"scroll"in F&&t(2,o=F.scroll),"token"in F&&t(3,i=F.token),"language"in F&&t(4,l=F.language),"padding"in F&&t(5,a=F.padding),"editorInstance"in F&&t(0,c=F.editorInstance),"element"in F&&t(1,p=F.element),"height"in F&&t(6,D=F.height),"$$scope"in F&&t(12,s=F.$$scope)},u.$$.update=()=>{var F;32&u.$$.dirty&&(F=a,c==null||c.updateOptions({padding:F})),65&u.$$.dirty&&(c==null||c.updateOptions({scrollbar:{vertical:D!==void 0?"auto":"hidden"}}))},[c,p,o,i,l,a,D,h,f,()=>c&&(d()||x())||"",d,x,s,r,function(F){xn.call(this,u,F)},()=>f.requestLayout(),function(F){c=F,t(0,c)},function(F){Ve[F?"unshift":"push"](()=>{p=F,t(1,p)})}]}class _t extends v{constructor(e){super(),B(this,e,ks,Fs,b,{scroll:2,token:3,language:4,padding:5,editorInstance:0,element:1,height:6,getSelectionOrContents:9,getSelections:10,getContents:11})}get getSelectionOrContents(){return this.$$.ctx[9]}get getSelections(){return this.$$.ctx[10]}get getContents(){return this.$$.ctx[11]}}const ms=u=>({codespanContents:2&u}),Ot=u=>({codespanContents:u[1]});function $s(u){let e,t,n;const r=u[4].default,s=O(r,u,u[3],Ot),o=s||function(i){let l;return{c(){l=te(i[1])},m(a,c){E(a,l,c)},p(a,c){2&c&&de(l,a[1])},d(a){a&&C(l)}}}(u);return{c(){e=w("span"),t=w("code"),o&&o.c(),m(t,"class","markdown-codespan svelte-1dofrdh")},m(i,l){E(i,e,l),L(e,t),o&&o.m(t,null),u[5](e),n=!0},p(i,[l]){s?s.p&&(!n||10&l)&&j(s,r,i,i[3],n?q(r,i[3],l,ms):N(i[3]),Ot):o&&o.p&&(!n||2&l)&&o.p(i,n?l:-1)},i(i){n||(k(o,i),n=!0)},o(i){$(o,i),n=!1},d(i){i&&C(e),o&&o.d(i),u[5](null)}}}function xs(u,e,t){let n,{$$slots:r={},$$scope:s}=e,{token:o}=e,{element:i}=e;return u.$$set=l=>{"token"in l&&t(2,o=l.token),"element"in l&&t(0,i=l.element),"$$scope"in l&&t(3,s=l.$$scope)},u.$$.update=()=>{4&u.$$.dirty&&t(1,n=o.raw.slice(1,o.raw.length-1))},[i,n,o,s,r,function(l){Ve[l?"unshift":"push"](()=>{i=l,t(0,i)})}]}class jt extends v{constructor(e){super(),B(this,e,xs,$s,b,{token:2,element:0})}}function Cs(u){let e,t,n,r,s=u[0].text+"";return{c(){e=w("span"),t=te("~"),n=te(s),r=te("~")},m(o,i){E(o,e,i),L(e,t),L(e,n),L(e,r)},p(o,[i]){1&i&&s!==(s=o[0].text+"")&&de(n,s)},i:T,o:T,d(o){o&&C(e)}}}function Es(u,e,t){let{token:n}=e;return u.$$set=r=>{"token"in r&&t(0,n=r.token)},[n]}class Nt extends v{constructor(e){super(),B(this,e,Es,Cs,b,{token:0})}}function As(u){let e,t;const n=u[1].default,r=O(n,u,u[0],null);return{c(){e=w("p"),r&&r.c(),m(e,"class","augment-markdown-paragraph svelte-1edcdk9")},m(s,o){E(s,e,o),r&&r.m(e,null),t=!0},p(s,[o]){r&&r.p&&(!t||1&o)&&j(r,n,s,s[0],t?q(n,s[0],o,null):N(s[0]),null)},i(s){t||(k(r,s),t=!0)},o(s){$(r,s),t=!1},d(s){s&&C(e),r&&r.d(s)}}}function ws(u,e,t){let{$$slots:n={},$$scope:r}=e;return u.$$set=s=>{"$$scope"in s&&t(0,r=s.$$scope)},[r,n]}class qt extends v{constructor(e){super(),B(this,e,ws,As,b,{})}}function bs(u){let e,t,n;return t=new Ds({props:{source:u[0],renderers:{codespan:jt,code:_t,paragraph:qt,del:Nt,...u[1]}}}),{c(){e=w("div"),X(t.$$.fragment),m(e,"class","c-markdown svelte-n6ddeo")},m(r,s){E(r,e,s),J(t,e,null),n=!0},p(r,[s]){const o={};1&s&&(o.source=r[0]),2&s&&(o.renderers={codespan:jt,code:_t,paragraph:qt,del:Nt,...r[1]}),t.$set(o)},i(r){n||(k(t.$$.fragment,r),n=!0)},o(r){$(t.$$.fragment,r),n=!1},d(r){r&&C(e),U(t)}}}function vs(u,e,t){let{markdown:n}=e,{renderers:r={}}=e;return u.$$set=s=>{"markdown"in s&&t(0,n=s.markdown),"renderers"in s&&t(1,r=s.renderers)},[n,r]}class Qs extends v{constructor(e){super(),B(this,e,vs,bs,b,{markdown:0,renderers:1})}}function Bs(u){let e,t;return{c(){e=De("svg"),t=De("path"),m(t,"fill-rule","evenodd"),m(t,"clip-rule","evenodd"),m(t,"d","M5.26047 5.79388C5.07301 5.98134 5.07301 6.28525 5.26047 6.47271C5.44792 6.66015 5.75184 6.66015 5.93929 6.47271L7.99988 4.41211L10.0605 6.47271C10.2479 6.66015 10.5518 6.66015 10.7393 6.47271C10.9267 6.28525 10.9267 5.98134 10.7393 5.79388L8.33929 3.39388C8.24926 3.30387 8.12717 3.2533 7.99988 3.2533C7.87257 3.2533 7.75048 3.30387 7.66046 3.39388L5.26047 5.79388ZM10.7393 10.206C10.9267 10.0186 10.9267 9.71467 10.7393 9.52722C10.5518 9.33977 10.2479 9.33977 10.0605 9.52722L7.99988 11.5878L5.93929 9.52722C5.75184 9.33977 5.44792 9.33977 5.26047 9.52722C5.07301 9.71467 5.07301 10.0186 5.26047 10.206L7.66046 12.6061C7.84792 12.7935 8.15184 12.7935 8.33929 12.6061L10.7393 10.206Z"),m(t,"fill","currentColor"),m(t,"fill-opacity","1"),m(e,"width","16"),m(e,"height","16"),m(e,"viewBox","0 0 16 16"),m(e,"fill","none"),m(e,"xmlns","http://www.w3.org/2000/svg")},m(n,r){E(n,e,r),L(e,t)},p:T,i:T,o:T,d(n){n&&C(e)}}}class Ws extends v{constructor(e){super(),B(this,e,null,Bs,b,{})}}function ys(u){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{width:"16"},{height:"16"},{"data-ds-icon":"fa"},{viewBox:"0 1 16 16"},u[0]],r={};for(let s=0;s<n.length;s+=1)r=pe(r,n[s]);return{c(){e=De("svg"),t=new bn(!0),this.h()},l(s){e=En(s,"svg",{xmlns:!0,width:!0,height:!0,"data-ds-icon":!0,viewBox:!0});var o=An(e);t=wn(o,!0),o.forEach(C),this.h()},h(){t.a=null,lt(e,r)},m(s,o){Cn(s,e,o),t.m('<path fill-rule="evenodd" d="M4.049 3.252a.53.53 0 0 1 .524-.015l9.6 5.067a.533.533 0 0 1 0 .943l-9.6 5.067a.533.533 0 0 1-.782-.472V3.71c0-.187.098-.36.258-.457" clip-rule="evenodd"/>',e)},p(s,[o]){lt(e,r=We(n,[{xmlns:"http://www.w3.org/2000/svg"},{width:"16"},{height:"16"},{"data-ds-icon":"fa"},{viewBox:"0 1 16 16"},1&o&&s[0]]))},i:T,o:T,d(s){s&&C(e)}}}function zs(u,e,t){return u.$$set=n=>{t(0,e=pe(pe({},e),at(n)))},[e=at(e)]}class Vs extends v{constructor(e){super(),B(this,e,zs,ys,b,{})}}function Ss(u){let e,t;return{c(){e=De("svg"),t=De("path"),m(t,"fill-rule","evenodd"),m(t,"clip-rule","evenodd"),m(t,"d","M12 13C12.5523 13 13 12.5523 13 12V3C13 2.44771 12.5523 2 12 2H3C2.44771 2 2 2.44771 2 3V6.5C2 6.77614 2.22386 7 2.5 7C2.77614 7 3 6.77614 3 6.5V3H12V12H8.5C8.22386 12 8 12.2239 8 12.5C8 12.7761 8.22386 13 8.5 13H12ZM9 6.5C9 6.5001 9 6.50021 9 6.50031V6.50035V9.5C9 9.77614 8.77614 10 8.5 10C8.22386 10 8 9.77614 8 9.5V7.70711L2.85355 12.8536C2.65829 13.0488 2.34171 13.0488 2.14645 12.8536C1.95118 12.6583 1.95118 12.3417 2.14645 12.1464L7.29289 7H5.5C5.22386 7 5 6.77614 5 6.5C5 6.22386 5.22386 6 5.5 6H8.5C8.56779 6 8.63244 6.01349 8.69139 6.03794C8.74949 6.06198 8.80398 6.09744 8.85143 6.14433C8.94251 6.23434 8.9992 6.35909 8.99999 6.49708L8.99999 6.49738"),m(t,"fill","currentColor"),m(e,"class",u[0]),m(e,"width","15"),m(e,"height","15"),m(e,"viewBox","0 0 15 15"),m(e,"fill","none"),m(e,"xmlns","http://www.w3.org/2000/svg")},m(n,r){E(n,e,r),L(e,t)},p(n,[r]){1&r&&m(e,"class",n[0])},i:T,o:T,d(n){n&&C(e)}}}function Ts(u,e,t){let{class:n=""}=e;return u.$$set=r=>{"class"in r&&t(0,n=r.class)},[n]}class Us extends v{constructor(e){super(),B(this,e,Ts,Ss,b,{class:0})}}function V(){}function Zt(u,e,t,n,r){for(var s,o=[];e;)o.push(e),s=e.previousComponent,delete e.previousComponent,e=s;o.reverse();for(var i=0,l=o.length,a=0,c=0;i<l;i++){var p=o[i];if(p.removed)p.value=u.join(n.slice(c,c+p.count)),c+=p.count;else{if(!p.added&&r){var D=t.slice(a,a+p.count);D=D.map(function(h,f){var d=n[c+f];return d.length>h.length?d:h}),p.value=u.join(D)}else p.value=u.join(t.slice(a,a+p.count));a+=p.count,p.added||(c+=p.count)}}return o}function Mt(u,e){var t;for(t=0;t<u.length&&t<e.length;t++)if(u[t]!=e[t])return u.slice(0,t);return u.slice(0,t)}function Ht(u,e){var t;if(!u||!e||u[u.length-1]!=e[e.length-1])return"";for(t=0;t<u.length&&t<e.length;t++)if(u[u.length-(t+1)]!=e[e.length-(t+1)])return u.slice(-t);return u.slice(-t)}function qe(u,e,t){if(u.slice(0,e.length)!=e)throw Error("string ".concat(JSON.stringify(u)," doesn't start with prefix ").concat(JSON.stringify(e),"; this is a bug"));return t+u.slice(e.length)}function Ze(u,e,t){if(!e)return u+t;if(u.slice(-e.length)!=e)throw Error("string ".concat(JSON.stringify(u)," doesn't end with suffix ").concat(JSON.stringify(e),"; this is a bug"));return u.slice(0,-e.length)+t}function ie(u,e){return qe(u,e,"")}function Ce(u,e){return Ze(u,e,"")}function Qt(u,e){return e.slice(0,function(t,n){var r=0;t.length>n.length&&(r=t.length-n.length);var s=n.length;t.length<n.length&&(s=t.length);var o=Array(s),i=0;o[0]=0;for(var l=1;l<s;l++){for(n[l]==n[i]?o[l]=o[i]:o[l]=i;i>0&&n[l]!=n[i];)i=o[i];n[l]==n[i]&&i++}i=0;for(var a=r;a<t.length;a++){for(;i>0&&t[a]!=n[i];)i=o[i];t[a]==n[i]&&i++}return i}(u,e))}V.prototype={diff:function(u,e){var t,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},r=n.callback;typeof n=="function"&&(r=n,n={});var s=this;function o(A){return A=s.postProcess(A,n),r?(setTimeout(function(){r(A)},0),!0):A}u=this.castInput(u,n),e=this.castInput(e,n),u=this.removeEmpty(this.tokenize(u,n));var i=(e=this.removeEmpty(this.tokenize(e,n))).length,l=u.length,a=1,c=i+l;n.maxEditLength!=null&&(c=Math.min(c,n.maxEditLength));var p=(t=n.timeout)!==null&&t!==void 0?t:1/0,D=Date.now()+p,h=[{oldPos:-1,lastComponent:void 0}],f=this.extractCommon(h[0],e,u,0,n);if(h[0].oldPos+1>=l&&f+1>=i)return o(Zt(s,h[0].lastComponent,e,u,s.useLongestToken));var d=-1/0,x=1/0;function F(){for(var A=Math.max(d,-a);A<=Math.min(x,a);A+=2){var y=void 0,g=h[A-1],I=h[A+1];g&&(h[A-1]=void 0);var P=!1;if(I){var Z=I.oldPos-A;P=I&&0<=Z&&Z<i}var ue=g&&g.oldPos+1<l;if(P||ue){if(y=!ue||P&&g.oldPos<I.oldPos?s.addToPath(I,!0,!1,0,n):s.addToPath(g,!1,!0,1,n),f=s.extractCommon(y,e,u,A,n),y.oldPos+1>=l&&f+1>=i)return o(Zt(s,y.lastComponent,e,u,s.useLongestToken));h[A]=y,y.oldPos+1>=l&&(x=Math.min(x,A-1)),f+1>=i&&(d=Math.max(d,A+1))}else h[A]=void 0}a++}if(r)(function A(){setTimeout(function(){if(a>c||Date.now()>D)return r();F()||A()},0)})();else for(;a<=c&&Date.now()<=D;){var _=F();if(_)return _}},addToPath:function(u,e,t,n,r){var s=u.lastComponent;return s&&!r.oneChangePerToken&&s.added===e&&s.removed===t?{oldPos:u.oldPos+n,lastComponent:{count:s.count+1,added:e,removed:t,previousComponent:s.previousComponent}}:{oldPos:u.oldPos+n,lastComponent:{count:1,added:e,removed:t,previousComponent:s}}},extractCommon:function(u,e,t,n,r){for(var s=e.length,o=t.length,i=u.oldPos,l=i-n,a=0;l+1<s&&i+1<o&&this.equals(t[i+1],e[l+1],r);)l++,i++,a++,r.oneChangePerToken&&(u.lastComponent={count:1,previousComponent:u.lastComponent,added:!1,removed:!1});return a&&!r.oneChangePerToken&&(u.lastComponent={count:a,previousComponent:u.lastComponent,added:!1,removed:!1}),u.oldPos=i,l},equals:function(u,e,t){return t.comparator?t.comparator(u,e):u===e||t.ignoreCase&&u.toLowerCase()===e.toLowerCase()},removeEmpty:function(u){for(var e=[],t=0;t<u.length;t++)u[t]&&e.push(u[t]);return e},castInput:function(u){return u},tokenize:function(u){return Array.from(u)},join:function(u){return u.join("")},postProcess:function(u){return u}};var Be="a-zA-Z0-9_\\u{C0}-\\u{FF}\\u{D8}-\\u{F6}\\u{F8}-\\u{2C6}\\u{2C8}-\\u{2D7}\\u{2DE}-\\u{2FF}\\u{1E00}-\\u{1EFF}",Rs=new RegExp("[".concat(Be,"]+|\\s+|[^").concat(Be,"]"),"ug"),Ee=new V;function Wt(u,e,t,n){if(e&&t){var r=e.value.match(/^\s*/)[0],s=e.value.match(/\s*$/)[0],o=t.value.match(/^\s*/)[0],i=t.value.match(/\s*$/)[0];if(u){var l=Mt(r,o);u.value=Ze(u.value,o,l),e.value=ie(e.value,l),t.value=ie(t.value,l)}if(n){var a=Ht(s,i);n.value=qe(n.value,i,a),e.value=Ce(e.value,a),t.value=Ce(t.value,a)}}else if(t)u&&(t.value=t.value.replace(/^\s*/,"")),n&&(n.value=n.value.replace(/^\s*/,""));else if(u&&n){var c=n.value.match(/^\s*/)[0],p=e.value.match(/^\s*/)[0],D=e.value.match(/\s*$/)[0],h=Mt(c,p);e.value=ie(e.value,h);var f=Ht(ie(c,h),D);e.value=Ce(e.value,f),n.value=qe(n.value,c,f),u.value=Ze(u.value,c,c.slice(0,c.length-f.length))}else if(n){var d=n.value.match(/^\s*/)[0],x=Qt(e.value.match(/\s*$/)[0],d);e.value=Ce(e.value,x)}else if(u){var F=Qt(u.value.match(/\s*$/)[0],e.value.match(/^\s*/)[0]);e.value=ie(e.value,F)}}Ee.equals=function(u,e,t){return t.ignoreCase&&(u=u.toLowerCase(),e=e.toLowerCase()),u.trim()===e.trim()},Ee.tokenize=function(u){var e,t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(t.intlSegmenter){if(t.intlSegmenter.resolvedOptions().granularity!="word")throw new Error('The segmenter passed must have a granularity of "word"');e=Array.from(t.intlSegmenter.segment(u),function(s){return s.segment})}else e=u.match(Rs)||[];var n=[],r=null;return e.forEach(function(s){/\s/.test(s)?r==null?n.push(s):n.push(n.pop()+s):/\s/.test(r)?n[n.length-1]==r?n.push(n.pop()+s):n.push(r+s):n.push(s),r=s}),n},Ee.join=function(u){return u.map(function(e,t){return t==0?e:e.replace(/^\s+/,"")}).join("")},Ee.postProcess=function(u,e){if(!u||e.oneChangePerToken)return u;var t=null,n=null,r=null;return u.forEach(function(s){s.added?n=s:s.removed?r=s:((n||r)&&Wt(t,r,n,s),t=s,n=null,r=null)}),(n||r)&&Wt(t,r,n,null),u},new V().tokenize=function(u){var e=new RegExp("(\\r?\\n)|[".concat(Be,"]+|[^\\S\\n\\r]+|[^").concat(Be,"]"),"ug");return u.match(e)||[]};var ye=new V;function Vt(u,e,t){return ye.diff(u,e,t)}function Ut(u,e){var t=Object.keys(u);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(u);e&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(u,r).enumerable})),t.push.apply(t,n)}return t}function ze(u){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?Ut(Object(t),!0).forEach(function(n){Ls(u,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(u,Object.getOwnPropertyDescriptors(t)):Ut(Object(t)).forEach(function(n){Object.defineProperty(u,n,Object.getOwnPropertyDescriptor(t,n))})}return u}function Is(u){var e=function(t,n){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var s=r.call(t,n);if(typeof s!="object")return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return(n==="string"?String:Number)(t)}(u,"string");return typeof e=="symbol"?e:e+""}function Me(u){return Me=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Me(u)}function Ls(u,e,t){return(e=Is(e))in u?Object.defineProperty(u,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):u[e]=t,u}function Pe(u){return function(e){if(Array.isArray(e))return _e(e)}(u)||function(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}(u)||function(e,t){if(e){if(typeof e=="string")return _e(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _e(e,t)}}(u)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function _e(u,e){(e==null||e>u.length)&&(e=u.length);for(var t=0,n=new Array(e);t<e;t++)n[t]=u[t];return n}ye.tokenize=function(u,e){e.stripTrailingCr&&(u=u.replace(/\r\n/g,`
`));var t=[],n=u.split(/(\n|\r\n)/);n[n.length-1]||n.pop();for(var r=0;r<n.length;r++){var s=n[r];r%2&&!e.newlineIsToken?t[t.length-1]+=s:t.push(s)}return t},ye.equals=function(u,e,t){return t.ignoreWhitespace?(t.newlineIsToken&&u.includes(`
`)||(u=u.trim()),t.newlineIsToken&&e.includes(`
`)||(e=e.trim())):t.ignoreNewlineAtEof&&!t.newlineIsToken&&(u.endsWith(`
`)&&(u=u.slice(0,-1)),e.endsWith(`
`)&&(e=e.slice(0,-1))),V.prototype.equals.call(this,u,e,t)},new V().tokenize=function(u){return u.split(/(\S.+?[.!?])(?=\s+|$)/)},new V().tokenize=function(u){return u.split(/([{}:;,]|\s+)/)};var le=new V;function He(u,e,t,n,r){var s,o;for(e=e||[],t=t||[],n&&(u=n(r,u)),s=0;s<e.length;s+=1)if(e[s]===u)return t[s];if(Object.prototype.toString.call(u)==="[object Array]"){for(e.push(u),o=new Array(u.length),t.push(o),s=0;s<u.length;s+=1)o[s]=He(u[s],e,t,n,r);return e.pop(),t.pop(),o}if(u&&u.toJSON&&(u=u.toJSON()),Me(u)==="object"&&u!==null){e.push(u),o={},t.push(o);var i,l=[];for(i in u)Object.prototype.hasOwnProperty.call(u,i)&&l.push(i);for(l.sort(),s=0;s<l.length;s+=1)o[i=l[s]]=He(u[i],e,t,n,i);e.pop(),t.pop()}else o=u;return o}le.useLongestToken=!0,le.tokenize=ye.tokenize,le.castInput=function(u,e){var t=e.undefinedReplacement,n=e.stringifyReplacer,r=n===void 0?function(s,o){return o===void 0?t:o}:n;return typeof u=="string"?u:JSON.stringify(He(u,null,null,r),r,"  ")},le.equals=function(u,e,t){return V.prototype.equals.call(le,u.replace(/,([\r\n])/g,"$1"),e.replace(/,([\r\n])/g,"$1"),t)};var Oe=new V;function Js(u){var e=u.split(/\n/),t=[],n=0;function r(){var i={};for(t.push(i);n<e.length;){var l=e[n];if(/^(\-\-\-|\+\+\+|@@)\s/.test(l))break;var a=/^(?:Index:|diff(?: -r \w+)+)\s+(.+?)\s*$/.exec(l);a&&(i.index=a[1]),n++}for(s(i),s(i),i.hunks=[];n<e.length;){var c=e[n];if(/^(Index:\s|diff\s|\-\-\-\s|\+\+\+\s|===================================================================)/.test(c))break;if(/^@@/.test(c))i.hunks.push(o());else{if(c)throw new Error("Unknown line "+(n+1)+" "+JSON.stringify(c));n++}}}function s(i){var l=/^(---|\+\+\+)\s+(.*)\r?$/.exec(e[n]);if(l){var a=l[1]==="---"?"old":"new",c=l[2].split("	",2),p=c[0].replace(/\\\\/g,"\\");/^".*"$/.test(p)&&(p=p.substr(1,p.length-2)),i[a+"FileName"]=p,i[a+"Header"]=(c[1]||"").trim(),n++}}function o(){var i=n,l=e[n++].split(/@@ -(\d+)(?:,(\d+))? \+(\d+)(?:,(\d+))? @@/),a={oldStart:+l[1],oldLines:l[2]===void 0?1:+l[2],newStart:+l[3],newLines:l[4]===void 0?1:+l[4],lines:[]};a.oldLines===0&&(a.oldStart+=1),a.newLines===0&&(a.newStart+=1);for(var c=0,p=0;n<e.length&&(p<a.oldLines||c<a.newLines||(D=e[n])!==null&&D!==void 0&&D.startsWith("\\"));n++){var D,h=e[n].length==0&&n!=e.length-1?" ":e[n][0];if(h!=="+"&&h!=="-"&&h!==" "&&h!=="\\")throw new Error("Hunk at line ".concat(i+1," contained invalid line ").concat(e[n]));a.lines.push(e[n]),h==="+"?c++:h==="-"?p++:h===" "&&(c++,p++)}if(c||a.newLines!==1||(a.newLines=0),p||a.oldLines!==1||(a.oldLines=0),c!==a.newLines)throw new Error("Added line count did not match for hunk at line "+(i+1));if(p!==a.oldLines)throw new Error("Removed line count did not match for hunk at line "+(i+1));return a}for(;n<e.length;)r();return t}function Jt(u,e,t,n,r,s,o){if(o||(o={}),typeof o=="function"&&(o={callback:o}),o.context===void 0&&(o.context=4),o.newlineIsToken)throw new Error("newlineIsToken may not be used with patch-generation functions, only with diffing functions");if(!o.callback)return l(Vt(t,n,o));var i=o.callback;function l(a){if(a){a.push({value:"",lines:[]});for(var c=[],p=0,D=0,h=[],f=1,d=1,x=function(){var P=a[F],Z=P.lines||function(Fe){var cn=Fe.endsWith(`
`),ke=Fe.split(`
`).map(function(pn){return pn+`
`});return cn?ke.pop():ke.push(ke.pop().slice(0,-1)),ke}(P.value);if(P.lines=Z,P.added||P.removed){var ue;if(!p){var et=a[F-1];p=f,D=d,et&&(h=o.context>0?I(et.lines.slice(-o.context)):[],p-=h.length,D-=h.length)}(ue=h).push.apply(ue,Pe(Z.map(function(Fe){return(P.added?"+":"-")+Fe}))),P.added?d+=Z.length:f+=Z.length}else{if(p)if(Z.length<=2*o.context&&F<a.length-2){var tt;(tt=h).push.apply(tt,Pe(I(Z)))}else{var nt,Re=Math.min(Z.length,o.context);(nt=h).push.apply(nt,Pe(I(Z.slice(0,Re))));var an={oldStart:p,oldLines:f-p+Re,newStart:D,newLines:d-D+Re,lines:h};c.push(an),p=0,D=0,h=[]}f+=Z.length,d+=Z.length}},F=0;F<a.length;F++)x();for(var _=0,A=c;_<A.length;_++)for(var y=A[_],g=0;g<y.lines.length;g++)y.lines[g].endsWith(`
`)?y.lines[g]=y.lines[g].slice(0,-1):(y.lines.splice(g+1,0,"\\ No newline at end of file"),g++);return{oldFileName:u,newFileName:e,oldHeader:r,newHeader:s,hunks:c}}function I(P){return P.map(function(Z){return" "+Z})}}Vt(t,n,ze(ze({},o),{},{callback:function(a){var c=l(a);i(c)}}))}function Qe(u){if(Array.isArray(u))return u.map(Qe).join(`
`);var e=[];u.oldFileName==u.newFileName&&e.push("Index: "+u.oldFileName),e.push("==================================================================="),e.push("--- "+u.oldFileName+(u.oldHeader===void 0?"":"	"+u.oldHeader)),e.push("+++ "+u.newFileName+(u.newHeader===void 0?"":"	"+u.newHeader));for(var t=0;t<u.hunks.length;t++){var n=u.hunks[t];n.oldLines===0&&(n.oldStart-=1),n.newLines===0&&(n.newStart-=1),e.push("@@ -"+n.oldStart+","+n.oldLines+" +"+n.newStart+","+n.newLines+" @@"),e.push.apply(e,n.lines)}return e.join(`
`)+`
`}function Ps(u,e,t,n,r,s,o){var i;if(typeof o=="function"&&(o={callback:o}),(i=o)===null||i===void 0||!i.callback){var l=Jt(u,e,t,n,r,s,o);return l?Qe(l):void 0}var a=o.callback;Jt(u,e,t,n,r,s,ze(ze({},o),{},{callback:function(c){c?a(Qe(c)):a()}}))}function Xt(u){let e=0;const t=1e4,n=u.length>t?u.substring(0,5e3)+u.substring(u.length-5e3):u;for(let r=0;r<n.length;r++)e=(e<<5)-e+n.charCodeAt(r),e|=0;return Math.abs(e).toString(36)}function Gt(u,e,t,n,r={}){const{context:s=3,generateId:o=!0}=r,i=Ps(u,e,t,n,"","",{context:s}),l=e||u;let a;return o?a=`${Xt(l)}-${Xt(t+n)}`:a=Math.random().toString(36).substring(2,15),{id:a,path:l,diff:i,originalCode:t,modifiedCode:n}}function Kt(u){const e=u.split(`
`);return{additions:e.filter(t=>t.startsWith("+")&&!t.startsWith("+++")).length,deletions:e.filter(t=>t.startsWith("-")&&!t.startsWith("---")).length}}function _s(u){return!u.originalCode||u.originalCode.trim()===""}function Os(u){return!u.modifiedCode||u.modifiedCode.trim()===""}Oe.tokenize=function(u){return u.slice()},Oe.join=Oe.removeEmpty=function(u){return u};const Xs=100,Gs="Too many files changed to display in the diff view. Please review the files in the remote workspace directly to inspect changes.";class Ks{static generateDiff(e,t,n,r){return Gt(e,t,n,r)}static generateDiffs(e){return function(t,n={}){return t.map(r=>Gt(r.oldPath,r.newPath,r.oldContent,r.newContent,n))}(e)}static getDiffStats(e){return Kt(e)}static getDiffObjectStats(e){return Kt(e.diff)}static isNewFile(e){return _s(e)}static isDeletedFile(e){return Os(e)}}export{jt as C,Ks as D,Ws as E,Qs as M,Us as O,Vs as P,Kt as a,Os as b,Ps as c,_t as d,Xs as e,Gs as f,Xt as g,_s as i,Js as p};
