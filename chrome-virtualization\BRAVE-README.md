# 🦁 AugmentCode Brave浏览器虚拟化工具包

专门针对AugmentCode注册检测的Brave浏览器虚拟化解决方案，包含数据清理、指纹虚拟化、代理切换等功能，并针对Brave浏览器的特有功能进行了优化。

## 🦁 Brave浏览器优势

### 内置隐私保护功能：
- **Brave Shields**: 内置广告拦截和隐私保护
- **Tor模式**: 内置Tor网络支持
- **指纹随机化**: 内置浏览器指纹保护
- **HTTPS Everywhere**: 自动升级到HTTPS

### 与Chrome的兼容性：
- 基于Chromium内核，完全兼容Chrome扩展
- 支持所有Chrome的JavaScript API
- 数据存储结构与Chrome相同

## 📁 工具包内容

### 1. `clear-brave-data.bat` - Brave数据清理脚本
- **功能**: 一键清理Brave用户数据目录
- **使用方法**: 以管理员权限运行
- **清理内容**:
  - 所有Profile的缓存
  - Local/Session Storage
  - IndexedDB
  - Cookies
  - 登录数据
  - Web数据
  - 偏好设置中的AugmentCode数据
  - Service Workers
  - **Brave特有**: Shields数据
  - **Brave特有**: Tor数据

### 2. `brave-quick-start.js` - Brave快速启动脚本
- **功能**: 一键执行所有Brave虚拟化操作
- **使用方法**: 在Brave控制台中运行
- **功能特性**:
  - 自动检测Brave特有功能
  - 隐藏Brave浏览器标识
  - 支持Tor模式检测
  - 清理Brave特有数据
  - 虚拟化浏览器指纹
  - 网络请求监控

### 3. `clear-augment-data.js` - 浏览器数据清理脚本
- **功能**: 清理Brave中所有AugmentCode相关的缓存、cookies、localStorage等
- **使用方法**: 在Brave控制台中运行
- **清理内容**:
  - Cache Storage
  - Session Storage  
  - Local Storage
  - Cookies
  - IndexedDB
  - Service Workers
  - WebSQL
  - 浏览器指纹数据
  - **Brave特有**: Shields相关数据

### 4. `fingerprint-spoofer.js` - 浏览器指纹虚拟化脚本
- **功能**: 虚拟化浏览器指纹，让每次访问都像新用户
- **使用方法**: 在Brave控制台中运行
- **虚拟化内容**:
  - User-Agent
  - 屏幕分辨率
  - 时区信息
  - WebGL信息
  - Canvas指纹
  - 字体信息
  - 音频指纹
  - 网络信息
  - **Brave特有**: 隐藏Brave标识

### 5. `proxy-switcher.js` - 代理切换工具
- **功能**: 自动切换代理服务器，更换IP地址
- **使用方法**: 在Brave控制台中运行
- **功能特性**:
  - 代理服务器管理
  - IP地址检测
  - 网络请求拦截
  - 自动化流程
  - **Brave特有**: 与Tor模式兼容

## 🚀 完整使用流程

### 方案一：手动操作（推荐）

1. **准备阶段**
   ```bash
   # 1. 关闭所有Brave窗口
   # 2. 以管理员权限运行批处理脚本
   clear-brave-data.bat
   ```

2. **启动Brave无痕模式**
   - 按 `Ctrl+Shift+N` 启动无痕模式
   - 访问 `https://app.augmentcode.com/`

3. **运行快速启动脚本**
   ```javascript
   // 在Brave控制台中运行
   // 复制 brave-quick-start.js 的内容并粘贴执行
   ```

4. **开始注册**
   - 现在可以安全地注册AugmentCode账户
   - 所有数据都是虚拟化的

### 方案二：分步操作

1. **运行数据清理脚本**
   ```javascript
   // 在Brave控制台中运行
   // 复制 clear-augment-data.js 的内容并粘贴执行
   ```

2. **运行指纹虚拟化脚本**
   ```javascript
   // 在Brave控制台中运行
   // 复制 fingerprint-spoofer.js 的内容并粘贴执行
   ```

3. **运行代理切换工具**
   ```javascript
   // 在Brave控制台中运行
   // 复制 proxy-switcher.js 的内容并粘贴执行
   
   // 添加你的代理服务器
   proxyManager.addProxy('your-proxy-host', 8080, 'http');
   
   // 自动切换代理
   automationTools.autoSwitchProxy();
   ```

## 🦁 Brave特有功能配置

### 启用Tor模式
```javascript
// 在Brave中启用Tor模式
// 1. 点击菜单 -> 新建Tor窗口
// 2. 或者使用快捷键 Ctrl+Shift+N 然后选择Tor模式
```

### 配置Brave Shields
```javascript
// Brave Shields设置
// 1. 点击地址栏右侧的Shields图标
// 2. 选择"高级控制"
// 3. 关闭"指纹识别阻止"（避免干扰我们的虚拟化）
```

### 使用Brave钱包
```javascript
// 如果启用了Brave钱包，建议临时禁用
// 1. 点击菜单 -> 设置
// 2. 搜索"钱包"
// 3. 临时禁用钱包功能
```

## 🔧 高级配置

### 自定义代理服务器

```javascript
// 添加HTTP代理
proxyManager.addProxy('proxy.example.com', 8080, 'http');

// 添加HTTPS代理
proxyManager.addProxy('proxy.example.com', 443, 'https');

// 添加SOCKS代理
proxyManager.addProxy('proxy.example.com', 1080, 'socks5');

// 使用Brave内置Tor（推荐）
// 直接启用Tor模式即可
```

### 自定义浏览器指纹

```javascript
// 修改指纹生成器中的参数
const userAgents = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
    // 添加更多User-Agent
];

const screenResolutions = [
    "1920x1080",
    "1366x768",
    // 添加更多分辨率
];
```

### 网络监控

```javascript
// 激活网络监控
networkInterceptor.activate();

// 查看拦截统计
console.log(networkInterceptor.getStats());

// 重置统计
networkInterceptor.resetStats();
```

## 📊 监控和验证

### 检查虚拟化效果

```javascript
// 检查当前IP
ipDetector.detectIP();

// 检查IP历史
console.log(ipDetector.getIPHistory());

// 检查当前代理
console.log(proxyManager.getCurrentProxy());

// 验证Brave虚拟化
verifyBraveVirtualization();
```

### 验证数据清理

```javascript
// 检查localStorage
console.log(Object.keys(localStorage));

// 检查sessionStorage
console.log(Object.keys(sessionStorage));

// 检查cookies
console.log(document.cookie);

// 检查Brave特有数据
console.log('Brave Shields:', typeof navigator.brave);
```

## ⚠️ 注意事项

### 安全提醒
1. **代理服务器**: 示例中的代理服务器是虚构的，请使用真实的代理服务
2. **数据备份**: 清理脚本会删除所有Brave数据，请提前备份重要信息
3. **法律合规**: 请确保使用符合当地法律法规的代理服务
4. **Tor使用**: 使用Tor模式时请遵守相关法律法规

### 技术限制
1. **浏览器限制**: 某些指纹虚拟化可能被浏览器安全策略阻止
2. **代理稳定性**: 免费代理可能不稳定，建议使用付费服务
3. **检测技术**: AugmentCode可能使用其他检测技术，本工具不能保证100%有效
4. **Brave Shields**: 某些Shields功能可能与虚拟化脚本冲突

### 最佳实践
1. **定期清理**: 每次注册前都运行清理脚本
2. **多工具配合**: 结合VPN、虚拟机等其他工具
3. **环境隔离**: 使用独立的浏览器Profile或虚拟机
4. **时间间隔**: 在不同注册之间留出足够的时间间隔
5. **Tor模式**: 优先使用Brave内置的Tor模式
6. **Shields配置**: 适当配置Brave Shields避免冲突

## 🛠️ 故障排除

### 常见问题

**Q: 清理脚本运行后没有效果？**
A: 确保以管理员权限运行批处理脚本，并完全关闭Brave

**Q: 指纹虚拟化不生效？**
A: 检查Brave Shields设置，关闭"指纹识别阻止"功能

**Q: 代理切换失败？**
A: 检查代理服务器是否可用，或直接使用Brave的Tor模式

**Q: 仍然被检测到？**
A: AugmentCode可能使用其他检测技术，考虑使用虚拟机方案

**Q: Brave Shields与脚本冲突？**
A: 临时禁用Shields或调整Shields设置

### 调试命令

```javascript
// 检查所有工具状态
console.log('代理管理器:', proxyManager);
console.log('网络拦截器:', networkInterceptor.getStats());
console.log('IP检测器:', ipDetector.getIPHistory());

// 检查Brave特有功能
console.log('Brave Shields:', typeof navigator.brave);
console.log('Tor模式:', window.location.hostname.endsWith('.onion'));

// 手动测试各项功能
proxyManager.testProxy(proxyManager.getCurrentProxy());
ipDetector.checkIPChange();
verifyBraveVirtualization();
```

## 📞 技术支持

如果遇到问题，请检查：
1. Brave版本是否最新
2. 是否以管理员权限运行
3. 代理服务器是否可用
4. 网络连接是否正常
5. Brave Shields设置是否正确

## 🔄 更新日志

- **v1.0.0**: 初始版本，包含基础清理和虚拟化功能
- 支持Brave数据清理
- 支持浏览器指纹虚拟化
- 支持代理切换
- 支持网络监控
- **v1.1.0**: Brave专用版本
- 添加Brave特有功能支持
- 优化Shields兼容性
- 添加Tor模式检测
- 隐藏Brave浏览器标识

---

**免责声明**: 本工具仅供学习和研究使用，请遵守相关法律法规和服务条款。 