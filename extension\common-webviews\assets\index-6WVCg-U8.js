import{B as y,C as W}from"./SpinnerAugment-VfHtkDdv.js";var E="Expected a function",w=NaN,M="[object Symbol]",N=/^\s+|\s+$/g,S=/^[-+]0x[0-9a-f]+$/i,B=/^0b[01]+$/i,C=/^0o[0-7]+$/i,D=parseInt,F=typeof y=="object"&&y&&y.Object===Object&&y,I=typeof self=="object"&&self&&self.Object===Object&&self,k=F||I||Function("return this")(),q=Object.prototype.toString,z=Math.max,A=Math.min,h=function(){return k.Date.now()};function G(e,n,t){var r,o,v,l,u,a,s=0,x=!1,p=!1,b=!0;if(typeof e!="function")throw new TypeError(E);function g(i){var f=r,c=o;return r=o=void 0,s=i,l=e.apply(c,f)}function O(i){var f=i-a;return a===void 0||f>=n||f<0||p&&i-s>=v}function m(){var i=h();if(O(i))return T(i);u=setTimeout(m,function(f){var c=n-(f-a);return p?A(c,v-(f-s)):c}(i))}function T(i){return u=void 0,b&&r?g(i):(r=o=void 0,l)}function j(){var i=h(),f=O(i);if(r=arguments,o=this,a=i,f){if(u===void 0)return function(c){return s=c,u=setTimeout(m,n),x?g(c):l}(a);if(p)return u=setTimeout(m,n),g(a)}return u===void 0&&(u=setTimeout(m,n)),l}return n=$(n)||0,d(t)&&(x=!!t.leading,v=(p="maxWait"in t)?z($(t.maxWait)||0,n):v,b="trailing"in t?!!t.trailing:b),j.cancel=function(){u!==void 0&&clearTimeout(u),s=0,r=a=o=u=void 0},j.flush=function(){return u===void 0?l:T(h())},j}function d(e){var n=typeof e;return!!e&&(n=="object"||n=="function")}function $(e){if(typeof e=="number")return e;if(function(r){return typeof r=="symbol"||function(o){return!!o&&typeof o=="object"}(r)&&q.call(r)==M}(e))return w;if(d(e)){var n=typeof e.valueOf=="function"?e.valueOf():e;e=d(n)?n+"":n}if(typeof e!="string")return e===0?e:+e;e=e.replace(N,"");var t=B.test(e);return t||C.test(e)?D(e.slice(2),t?2:8):S.test(e)?w:+e}const J=W(function(e,n,t){var r=!0,o=!0;if(typeof e!="function")throw new TypeError(E);return d(t)&&(r="leading"in t?!!t.leading:r,o="trailing"in t?!!t.trailing:o),G(e,n,{leading:r,maxWait:n,trailing:o})});export{J as t};
